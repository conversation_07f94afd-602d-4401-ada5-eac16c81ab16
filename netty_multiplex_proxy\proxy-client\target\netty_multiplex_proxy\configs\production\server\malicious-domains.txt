# 恶意域名黑名单 - 生产环境
# 此文件包含已知的恶意域名，用于安全过滤
# 格式：每行一个域名，支持hosts文件格式
# 注释行以#开头

# 示例恶意域名（实际部署时应包含完整的恶意域名数据）
malware-site.com
phishing-example.net
spam-domain.org
fake-bank.com
scam-site.net
malicious-download.com
trojan-host.org
virus-site.net
adware-domain.com
spyware-site.org

# hosts文件格式示例
0.0.0.0 bad-site.com
0.0.0.0 another-malware.net
127.0.0.1 localhost-redirect.org

# 广告和跟踪域名
ads.example.com
tracker.example.net
analytics.spam.org
doubleclick.net
googleadservices.com

# 注意：这只是示例数据
# 生产环境中应该使用完整的恶意域名数据
# 可以从以下来源获取最新数据：
# - https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts
# - https://someonewhocares.org/hosts/zero/hosts
# - https://raw.githubusercontent.com/AdguardTeam/AdguardFilters/master/BaseFilter/sections/adservers.txt
