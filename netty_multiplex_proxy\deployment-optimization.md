# 部署和运维工具优化方案

## 1. 容器化部署

### Docker镜像构建

```dockerfile
# proxy-server/Dockerfile
FROM openjdk:17-jdk-slim as builder

WORKDIR /app
COPY pom.xml .
COPY src ./src

# 构建应用
RUN ./mvnw clean package -DskipTests

# 运行时镜像
FROM openjdk:17-jre-slim

# 安装必要工具
RUN apt-get update && apt-get install -y \
    curl \
    jq \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r proxy && useradd -r -g proxy proxy

WORKDIR /app

# 复制应用文件
COPY --from=builder /app/target/proxy-server-*.jar app.jar
COPY --chown=proxy:proxy configs ./configs/
COPY --chown=proxy:proxy scripts ./scripts/

# 创建数据目录
RUN mkdir -p /app/data /app/logs && \
    chown -R proxy:proxy /app

USER proxy

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/api/v1/system/health || exit 1

# 暴露端口
EXPOSE 8888 8080

# 启动命令
ENTRYPOINT ["java", "-jar", "app.jar"]
CMD ["--spring.profiles.active=docker"]
```

```dockerfile
# proxy-client/Dockerfile.native
FROM ghcr.io/graalvm/native-image:ol8-java17-22.3.0 as builder

WORKDIR /app
COPY pom.xml .
COPY src ./src

# 构建Native Image
RUN ./mvnw clean -Pnative native:compile

# 运行时镜像 - 使用distroless减小镜像大小
FROM gcr.io/distroless/base-debian11

WORKDIR /app

# 复制Native可执行文件
COPY --from=builder /app/target/proxy-client ./proxy-client
COPY configs ./configs/

# 暴露端口
EXPOSE 1081 1082

# 启动命令
ENTRYPOINT ["./proxy-client"]
```

### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  proxy-server:
    build:
      context: ./proxy-server
      dockerfile: Dockerfile
    container_name: proxy-server
    ports:
      - "8888:8888"
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - CONFIG_EXT_DIR=/app/configs/docker
      - JAVA_OPTS=-Xmx2g -Xms1g
    volumes:
      - ./configs/docker/server:/app/configs/docker
      - proxy-server-data:/app/data
      - proxy-server-logs:/app/logs
    networks:
      - proxy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  proxy-client:
    build:
      context: ./proxy-client
      dockerfile: Dockerfile.native
    container_name: proxy-client
    ports:
      - "1081:1081"
      - "1082:1082"
    environment:
      - PROXY_SERVER_HOST=proxy-server
      - PROXY_SERVER_PORT=8888
    volumes:
      - ./configs/docker/client:/app/configs
    networks:
      - proxy-network
    depends_on:
      proxy-server:
        condition: service_healthy
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: proxy-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - proxy-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    container_name: proxy-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx-logs:/var/log/nginx
    networks:
      - proxy-network
    depends_on:
      - proxy-server
    restart: unless-stopped

volumes:
  proxy-server-data:
  proxy-server-logs:
  redis-data:
  nginx-logs:

networks:
  proxy-network:
    driver: bridge
```

## 2. Kubernetes部署

### Kubernetes清单文件
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: proxy-system
  labels:
    name: proxy-system

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: proxy-server-config
  namespace: proxy-system
data:
  application.yml: |
    server:
      port: 8888
    management:
      endpoints:
        web:
          exposure:
            include: health,metrics,info
      endpoint:
        health:
          show-details: always
    logging:
      level:
        com.proxy: INFO
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxy-server
  namespace: proxy-system
  labels:
    app: proxy-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: proxy-server
  template:
    metadata:
      labels:
        app: proxy-server
    spec:
      containers:
      - name: proxy-server
        image: proxy-server:latest
        ports:
        - containerPort: 8888
          name: proxy-port
        - containerPort: 8080
          name: management
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes"
        - name: JAVA_OPTS
          value: "-Xmx2g -Xms1g -XX:+UseG1GC"
        volumeMounts:
        - name: config-volume
          mountPath: /app/configs
        - name: data-volume
          mountPath: /app/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/v1/system/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/system/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
      volumes:
      - name: config-volume
        configMap:
          name: proxy-server-config
      - name: data-volume
        persistentVolumeClaim:
          claimName: proxy-server-pvc

---
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: proxy-server-service
  namespace: proxy-system
  labels:
    app: proxy-server
spec:
  selector:
    app: proxy-server
  ports:
  - name: proxy
    port: 8888
    targetPort: 8888
    protocol: TCP
  - name: management
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP

---
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: proxy-server-ingress
  namespace: proxy-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - proxy.example.com
    secretName: proxy-server-tls
  rules:
  - host: proxy.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: proxy-server-service
            port:
              number: 8080

---
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: proxy-server-hpa
  namespace: proxy-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: proxy-server
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 3. 自动化部署脚本

### 部署脚本
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

# 配置变量
ENVIRONMENT=${1:-development}
VERSION=${2:-latest}
NAMESPACE="proxy-system"

echo "开始部署 Proxy System - 环境: $ENVIRONMENT, 版本: $VERSION"

# 检查依赖
check_dependencies() {
    echo "检查部署依赖..."
    
    if ! command -v kubectl &> /dev/null; then
        echo "错误: kubectl 未安装"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        echo "错误: docker 未安装"
        exit 1
    fi
    
    echo "依赖检查完成"
}

# 构建镜像
build_images() {
    echo "构建Docker镜像..."
    
    # 构建服务器镜像
    docker build -t proxy-server:$VERSION ./proxy-server/
    
    # 构建客户端镜像
    docker build -t proxy-client:$VERSION -f ./proxy-client/Dockerfile.native ./proxy-client/
    
    echo "镜像构建完成"
}

# 推送镜像到仓库
push_images() {
    if [ "$ENVIRONMENT" != "development" ]; then
        echo "推送镜像到仓库..."
        
        docker tag proxy-server:$VERSION $REGISTRY/proxy-server:$VERSION
        docker tag proxy-client:$VERSION $REGISTRY/proxy-client:$VERSION
        
        docker push $REGISTRY/proxy-server:$VERSION
        docker push $REGISTRY/proxy-client:$VERSION
        
        echo "镜像推送完成"
    fi
}

# 部署到Kubernetes
deploy_to_k8s() {
    echo "部署到Kubernetes..."
    
    # 创建命名空间
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # 应用配置
    envsubst < k8s/configmap-$ENVIRONMENT.yaml | kubectl apply -f -
    
    # 部署应用
    envsubst < k8s/deployment.yaml | kubectl apply -f -
    envsubst < k8s/service.yaml | kubectl apply -f -
    envsubst < k8s/ingress.yaml | kubectl apply -f -
    
    # 等待部署完成
    kubectl rollout status deployment/proxy-server -n $NAMESPACE --timeout=300s
    
    echo "Kubernetes部署完成"
}

# 健康检查
health_check() {
    echo "执行健康检查..."
    
    # 等待服务就绪
    sleep 30
    
    # 检查服务状态
    if kubectl get pods -n $NAMESPACE | grep -q "Running"; then
        echo "服务运行正常"
    else
        echo "警告: 部分服务可能未正常启动"
        kubectl get pods -n $NAMESPACE
    fi
    
    # 检查API健康状态
    SERVICE_IP=$(kubectl get service proxy-server-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
    if curl -f http://$SERVICE_IP:8080/api/v1/system/health; then
        echo "API健康检查通过"
    else
        echo "警告: API健康检查失败"
    fi
}

# 回滚函数
rollback() {
    echo "执行回滚..."
    kubectl rollout undo deployment/proxy-server -n $NAMESPACE
    kubectl rollout status deployment/proxy-server -n $NAMESPACE --timeout=300s
    echo "回滚完成"
}

# 清理函数
cleanup() {
    echo "清理资源..."
    kubectl delete namespace $NAMESPACE --ignore-not-found=true
    echo "清理完成"
}

# 主流程
main() {
    case "${3:-deploy}" in
        "deploy")
            check_dependencies
            build_images
            push_images
            deploy_to_k8s
            health_check
            ;;
        "rollback")
            rollback
            ;;
        "cleanup")
            cleanup
            ;;
        *)
            echo "用法: $0 <environment> <version> [deploy|rollback|cleanup]"
            exit 1
            ;;
    esac
}

# 错误处理
trap 'echo "部署失败，执行清理..."; cleanup; exit 1' ERR

main "$@"

echo "部署完成！"
```

### 监控部署脚本
```bash
#!/bin/bash
# scripts/setup-monitoring.sh

set -e

NAMESPACE="proxy-system"
MONITORING_NAMESPACE="monitoring"

echo "设置监控系统..."

# 安装Prometheus
install_prometheus() {
    echo "安装Prometheus..."
    
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update
    
    helm install prometheus prometheus-community/kube-prometheus-stack \
        --namespace $MONITORING_NAMESPACE \
        --create-namespace \
        --set prometheus.prometheusSpec.serviceMonitorSelectorNilUsesHelmValues=false \
        --set prometheus.prometheusSpec.podMonitorSelectorNilUsesHelmValues=false
}

# 配置ServiceMonitor
setup_service_monitor() {
    echo "配置ServiceMonitor..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: proxy-server-monitor
  namespace: $MONITORING_NAMESPACE
  labels:
    app: proxy-server
spec:
  selector:
    matchLabels:
      app: proxy-server
  namespaceSelector:
    matchNames:
    - $NAMESPACE
  endpoints:
  - port: management
    path: /actuator/prometheus
    interval: 30s
EOF
}

# 安装Grafana仪表板
install_grafana_dashboard() {
    echo "安装Grafana仪表板..."
    
    kubectl create configmap proxy-dashboard \
        --from-file=monitoring/grafana-dashboard.json \
        -n $MONITORING_NAMESPACE
    
    kubectl label configmap proxy-dashboard \
        grafana_dashboard=1 \
        -n $MONITORING_NAMESPACE
}

# 配置告警规则
setup_alert_rules() {
    echo "配置告警规则..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: proxy-server-alerts
  namespace: $MONITORING_NAMESPACE
  labels:
    app: proxy-server
spec:
  groups:
  - name: proxy-server
    rules:
    - alert: ProxyServerDown
      expr: up{job="proxy-server"} == 0
      for: 1m
      labels:
        severity: critical
      annotations:
        summary: "Proxy Server is down"
        description: "Proxy Server has been down for more than 1 minute"
    
    - alert: HighCPUUsage
      expr: rate(process_cpu_seconds_total{job="proxy-server"}[5m]) * 100 > 80
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "High CPU usage detected"
        description: "CPU usage is above 80% for more than 5 minutes"
    
    - alert: HighMemoryUsage
      expr: (jvm_memory_used_bytes{job="proxy-server"} / jvm_memory_max_bytes{job="proxy-server"}) * 100 > 85
      for: 3m
      labels:
        severity: warning
      annotations:
        summary: "High memory usage detected"
        description: "Memory usage is above 85% for more than 3 minutes"
EOF
}

# 主流程
install_prometheus
setup_service_monitor
install_grafana_dashboard
setup_alert_rules

echo "监控系统设置完成！"
echo "Grafana访问地址: kubectl port-forward -n $MONITORING_NAMESPACE svc/prometheus-grafana 3000:80"
echo "默认用户名: admin, 密码: prom-operator"
```

## 4. 运维工具

### 系统服务配置
```ini
# systemd/proxy-server.service
[Unit]
Description=Proxy Server
After=network.target
Wants=network.target

[Service]
Type=simple
User=proxy
Group=proxy
WorkingDirectory=/opt/proxy-server
ExecStart=/usr/bin/java -jar -Xmx2g -Xms1g proxy-server.jar
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
Restart=always
RestartSec=10

# 环境变量
Environment=JAVA_HOME=/usr/lib/jvm/java-17-openjdk
Environment=CONFIG_EXT_DIR=/etc/proxy-server
Environment=LOG_DIR=/var/log/proxy-server

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/proxy-server /var/lib/proxy-server

[Install]
WantedBy=multi-user.target
```

### 日志轮转配置
```
# logrotate.d/proxy-server
/var/log/proxy-server/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 proxy proxy
    postrotate
        systemctl reload proxy-server
    endscript
}
```

### 备份脚本
```bash
#!/bin/bash
# scripts/backup.sh

BACKUP_DIR="/backup/proxy-system"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

echo "开始备份 Proxy System - $DATE"

# 创建备份目录
mkdir -p $BACKUP_DIR/$DATE

# 备份配置文件
echo "备份配置文件..."
tar -czf $BACKUP_DIR/$DATE/configs.tar.gz /etc/proxy-server/ /etc/proxy-client/

# 备份数据库
echo "备份数据库..."
kubectl exec -n proxy-system deployment/proxy-server -- \
    pg_dump -h localhost -U proxy proxy_db > $BACKUP_DIR/$DATE/database.sql

# 备份日志
echo "备份日志..."
tar -czf $BACKUP_DIR/$DATE/logs.tar.gz /var/log/proxy-server/ /var/log/proxy-client/

# 清理旧备份
echo "清理旧备份..."
find $BACKUP_DIR -type d -mtime +$RETENTION_DAYS -exec rm -rf {} +

echo "备份完成: $BACKUP_DIR/$DATE"
```

### 性能调优脚本
```bash
#!/bin/bash
# scripts/performance-tuning.sh

echo "开始性能调优..."

# 系统参数优化
optimize_system() {
    echo "优化系统参数..."
    
    # 网络参数优化
    cat >> /etc/sysctl.conf << EOF
# 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000

# 文件描述符限制
fs.file-max = 1000000
EOF
    
    sysctl -p
}

# JVM参数优化
optimize_jvm() {
    echo "优化JVM参数..."
    
    cat > /etc/proxy-server/jvm.options << EOF
# 堆内存设置
-Xms2g
-Xmx4g

# GC优化
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m
-XX:+G1UseAdaptiveIHOP
-XX:G1MixedGCCountTarget=8

# JIT优化
-XX:+TieredCompilation
-XX:TieredStopAtLevel=4

# 监控参数
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-Xloggc:/var/log/proxy-server/gc.log
-XX:+UseGCLogFileRotation
-XX:NumberOfGCLogFiles=5
-XX:GCLogFileSize=10M

# 其他优化
-Djava.awt.headless=true
-Dfile.encoding=UTF-8
-Duser.timezone=Asia/Shanghai
EOF
}

# 应用配置优化
optimize_application() {
    echo "优化应用配置..."
    
    cat >> /etc/proxy-server/application.yml << EOF
# 性能优化配置
server:
  tomcat:
    threads:
      max: 200
      min-spare: 10
    connection-timeout: 20000
    max-connections: 8192
    accept-count: 100

# 连接池优化
connection-pool:
  max-connections-per-host: 50
  connection-timeout: 5000
  idle-timeout: 60000
  max-idle-time: 300000

# 缓存优化
cache:
  dns-cache-timeout-minutes: 30
  ip-cache-timeout-minutes: 240
  max-cache-size: 100000

# 线程池优化
thread-pool:
  core-size: 20
  max-size: 100
  queue-capacity: 1000
  keep-alive-seconds: 60
EOF
}

# 主流程
optimize_system
optimize_jvm
optimize_application

echo "性能调优完成！"
echo "请重启服务以应用新配置: systemctl restart proxy-server"
```