server:
  port: 5556

spring:
  application:
    name: gateway
  cloud:
    gateway:
      enabled: true
      discovery:
        locator:
          enabled: true
          lowerCaseServiceId: true
      routes:
        - id: chat-service-websocket
          uri: lb://chat-service-ws
          predicates:
            - Path=/ws/**
    nacos:
      discovery:
        server-addr: 192.168.5.134:8848
        namespace: public
        group: DEFAULT_GROUP
        username: nacos
        password: nacos
        enabled: true
        register-enabled: true
