package com.xiang.proxy.server.core;

import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.socket.DatagramChannel;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.SocketChannel;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 活跃连接管理器 - 参考server DispatchProcessor的ActiveConnection机制并改进
 * 实现连接复用、消息队列缓存、超时管理等功能
 */
public class ActiveConnectionManager {
    private static final Logger logger = LoggerFactory.getLogger(ActiveConnectionManager.class);
    
    /**
     * 默认连接超时时间 - 60秒
     */
    private static final long DEFAULT_TIMEOUT = 60 * 1000L;
    
    /**
     * 消息队列最大大小，防止内存溢出
     */
    private static final int MAX_QUEUE_SIZE = 1000;
    
    /**
     * 活跃连接映射 - 使用ConcurrentHashMap提高并发性能
     */
    private final Map<ConnectionKey, ActiveConnection> activeConnections;
    
    /**
     * 统计信息
     */
    private final AtomicInteger totalConnections = new AtomicInteger(0);
    private final AtomicInteger activeConnectionCount = new AtomicInteger(0);
    private final AtomicLong totalMessages = new AtomicLong(0);
    private final AtomicLong queuedMessages = new AtomicLong(0);
    
    public ActiveConnectionManager() {
        this.activeConnections = new ConcurrentHashMap<>();
    }
    
    /**
     * 获取或创建活跃连接
     */
    public ActiveConnection getOrCreateConnection(String host, int port, String clientId, String protocol) {
        ConnectionKey key = new ConnectionKey(host, port, clientId, protocol);
        return activeConnections.computeIfAbsent(key, k -> {
            totalConnections.incrementAndGet();
            activeConnectionCount.incrementAndGet();
            logger.debug("创建新的活跃连接: {}", key);
            return new ActiveConnection(host, port, clientId, protocol);
        });
    }
    
    /**
     * 移除连接
     */
    public void removeConnection(String host, int port, String clientId, String protocol) {
        ConnectionKey key = new ConnectionKey(host, port, clientId, protocol);
        ActiveConnection connection = activeConnections.remove(key);
        if (connection != null) {
            activeConnectionCount.decrementAndGet();
            connection.cleanup();
            logger.debug("移除活跃连接: {}", key);
        }
    }
    
    /**
     * 清理超时连接
     */
    public int cleanupTimeoutConnections() {
        long currentTime = System.currentTimeMillis();
        int cleanedCount = 0;
        
        Iterator<Map.Entry<ConnectionKey, ActiveConnection>> iterator = activeConnections.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<ConnectionKey, ActiveConnection> entry = iterator.next();
            ActiveConnection connection = entry.getValue();
            
            if (connection.isTimeout(currentTime, DEFAULT_TIMEOUT)) {
                iterator.remove();
                activeConnectionCount.decrementAndGet();
                connection.cleanup();
                cleanedCount++;
                logger.debug("清理超时连接: {}", entry.getKey());
            }
        }
        
        if (cleanedCount > 0) {
            logger.info("清理了 {} 个超时连接", cleanedCount);
        }
        
        return cleanedCount;
    }
    
    /**
     * 获取统计信息
     */
    public ConnectionStats getStats() {
        return new ConnectionStats(
            totalConnections.get(),
            activeConnectionCount.get(),
            totalMessages.get(),
            queuedMessages.get()
        );
    }
    
    /**
     * 连接键 - 用于唯一标识一个连接
     */
    public static class ConnectionKey {
        private final String host;
        private final int port;
        private final String clientId;
        private final String protocol;
        private final int hashCode;
        
        public ConnectionKey(String host, int port, String clientId, String protocol) {
            this.host = host;
            this.port = port;
            this.clientId = clientId;
            this.protocol = protocol;
            this.hashCode = Objects.hash(host, port, clientId, protocol);
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            ConnectionKey that = (ConnectionKey) obj;
            return port == that.port &&
                   Objects.equals(host, that.host) &&
                   Objects.equals(clientId, that.clientId) &&
                   Objects.equals(protocol, that.protocol);
        }
        
        @Override
        public int hashCode() {
            return hashCode;
        }
        
        @Override
        public String toString() {
            return String.format("%s:%d[%s](%s)", host, port, clientId, protocol);
        }
    }
    
    /**
     * 活跃连接 - 参考server DispatchProcessor的ActiveConnection并改进
     */
    public class ActiveConnection {
        private final String host;
        private final int port;
        private final String clientId;
        private final String protocol;
        private volatile ChannelFuture channelFuture;
        private final Queue<ByteBuf> messageQueue;
        private volatile long lastActiveTime;
        private final AtomicInteger messageCount = new AtomicInteger(0);
        
        public ActiveConnection(String host, int port, String clientId, String protocol) {
            this.host = host;
            this.port = port;
            this.clientId = clientId;
            this.protocol = protocol;
            this.messageQueue = new ConcurrentLinkedQueue<>();
            this.lastActiveTime = System.currentTimeMillis();
        }
        
        /**
         * 设置Channel Future
         */
        public void setChannelFuture(ChannelFuture future) {
            this.channelFuture = future;
        }
        
        /**
         * 发送消息 - 支持队列缓存
         */
        public boolean sendMessage(ByteBuf message) {
            totalMessages.incrementAndGet();
            messageCount.incrementAndGet();
            
            ChannelFuture future = this.channelFuture;
            if (future != null && future.isDone() && future.isSuccess()) {
                Channel channel = future.channel();
                if (channel.isActive()) {
                    // 连接已建立且活跃，先发送队列中的消息
                    flushQueuedMessages(channel);
                    // 然后发送当前消息
                    channel.writeAndFlush(message);
                    updateLastActiveTime();
                    return true;
                }
            }
            
            // 连接未建立或不活跃，加入队列
            if (messageQueue.size() < MAX_QUEUE_SIZE) {
                messageQueue.offer(message);
                queuedMessages.incrementAndGet();
                logger.debug("消息已加入队列，当前队列大小: {}", messageQueue.size());
                return true;
            } else {
                // 队列已满，释放消息并返回失败
                ReferenceCountUtil.release(message);
                logger.warn("消息队列已满，丢弃消息");
                return false;
            }
        }
        
        /**
         * 刷新队列中的消息
         */
        public void flushQueuedMessages(Channel channel) {
            if (messageQueue.isEmpty()) {
                return;
            }
            
            ByteBuf message;
            int flushedCount = 0;
            while ((message = messageQueue.poll()) != null) {
                if (channel.isActive()) {
                    if (channel instanceof SocketChannel) {
                        channel.write(message);
                    } else if (channel instanceof DatagramChannel) {
                        InetSocketAddress addr = new InetSocketAddress(host, port);
                        channel.write(new DatagramPacket(message, addr));
                    }
                    flushedCount++;
                    queuedMessages.decrementAndGet();
                } else {
                    // 连接已断开，释放剩余消息
                    ReferenceCountUtil.release(message);
                    queuedMessages.decrementAndGet();
                }
            }
            
            if (flushedCount > 0) {
                channel.flush();
                updateLastActiveTime();
                logger.debug("刷新了 {} 条队列消息", flushedCount);
            }
        }
        
        /**
         * 检查是否超时
         */
        public boolean isTimeout(long currentTime, long timeoutMs) {
            return (currentTime - lastActiveTime) > timeoutMs;
        }
        
        /**
         * 更新最后活跃时间
         */
        public void updateLastActiveTime() {
            this.lastActiveTime = System.currentTimeMillis();
        }
        
        /**
         * 清理资源
         */
        public void cleanup() {
            // 释放队列中的消息
            ByteBuf message;
            while ((message = messageQueue.poll()) != null) {
                ReferenceCountUtil.release(message);
                queuedMessages.decrementAndGet();
            }
            
            // 关闭连接
            if (channelFuture != null && channelFuture.isDone() && channelFuture.isSuccess()) {
                Channel channel = channelFuture.channel();
                if (channel.isActive()) {
                    channel.close();
                }
            }
        }
        
        // Getters
        public String getHost() { return host; }
        public int getPort() { return port; }
        public String getClientId() { return clientId; }
        public String getProtocol() { return protocol; }
        public ChannelFuture getChannelFuture() { return channelFuture; }
        public int getQueueSize() { return messageQueue.size(); }
        public long getLastActiveTime() { return lastActiveTime; }
        public int getMessageCount() { return messageCount.get(); }
    }
    
    /**
     * 连接统计信息
     */
    public static class ConnectionStats {
        private final int totalConnections;
        private final int activeConnections;
        private final long totalMessages;
        private final long queuedMessages;
        
        public ConnectionStats(int totalConnections, int activeConnections, 
                             long totalMessages, long queuedMessages) {
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
            this.totalMessages = totalMessages;
            this.queuedMessages = queuedMessages;
        }
        
        public int getTotalConnections() { return totalConnections; }
        public int getActiveConnections() { return activeConnections; }
        public long getTotalMessages() { return totalMessages; }
        public long getQueuedMessages() { return queuedMessages; }
        
        @Override
        public String toString() {
            return String.format("ConnectionStats{total=%d, active=%d, totalMsg=%d, queuedMsg=%d}",
                totalConnections, activeConnections, totalMessages, queuedMessages);
        }
    }
}
