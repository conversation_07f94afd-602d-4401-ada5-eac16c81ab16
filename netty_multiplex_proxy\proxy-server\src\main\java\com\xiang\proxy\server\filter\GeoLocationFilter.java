package com.xiang.proxy.server.filter;

import com.xiang.proxy.server.config.ProxyServerConfigManager;
import com.xiang.proxy.server.util.GeoIPUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * 地理位置过滤器
 * 基于地理位置和域名模式过滤访问请求，阻止非中国地区的不良网站访问
 */
public class GeoLocationFilter {
    private static final Logger logger = LoggerFactory.getLogger(GeoLocationFilter.class);
    
    private static final GeoLocationFilter INSTANCE = new GeoLocationFilter();
    
    // 恶意域名黑名单 - 支持精确匹配和模式匹配
    private final Set<String> maliciousDomains = ConcurrentHashMap.newKeySet();
    private final Set<Pattern> maliciousDomainPatterns = ConcurrentHashMap.newKeySet();
    
    // 恶意域名关键词 - 用于检测可疑域名
    private final Set<String> maliciousKeywords = ConcurrentHashMap.newKeySet();
    
    // 白名单域名 - 即使在海外也允许访问的域名
    private final Set<String> whitelistDomains = ConcurrentHashMap.newKeySet();
    private final Set<Pattern> whitelistDomainPatterns = ConcurrentHashMap.newKeySet();
    
    // 统计信息
    private volatile long totalRequests = 0;
    private volatile long blockedByGeoLocation = 0;
    private volatile long blockedByDomainFilter = 0;
    private volatile long allowedRequests = 0;
    
    // DNS解析缓存 - 避免重复解析
    private final Map<String, String> dnsCache = new ConcurrentHashMap<>();
    private final Map<String, Long> dnsCacheTime = new ConcurrentHashMap<>();

    // IP地理位置缓存
    private final Map<String, Boolean> ipLocationCache = new ConcurrentHashMap<>();
    private final Map<String, Long> ipLocationCacheTime = new ConcurrentHashMap<>();
    
    private GeoLocationFilter() {
        initializeMaliciousDomains();
        initializeWhitelistDomains();

        // 显示配置状态
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
        logger.info("地理位置过滤器已初始化");
        logger.info("  - 恶意域名: {}, 恶意关键词: {}, 白名单域名: {}",
            maliciousDomains.size(), maliciousKeywords.size(), whitelistDomains.size());
        logger.info("  - 配置状态: 总开关={}, 海外可疑={}, 域名过滤={}, 关键词过滤={}, 白名单={}",
            configManager.isGeoLocationFilterEnabled(),
            configManager.isBlockOverseasSuspicious(),
            configManager.isDomainFilterEnabled(),
            configManager.isKeywordFilterEnabled(),
            configManager.isWhitelistEnabled());
        logger.info("  - 缓存配置: DNS缓存={}分钟, IP缓存={}分钟, 最大缓存={}条目",
            configManager.getDnsCacheTimeoutMinutes(),
            configManager.getIpCacheTimeoutMinutes(),
            configManager.getMaxCacheSize());
        logger.info("  - 更新配置: 自动更新={}, 更新间隔={}小时",
            configManager.isAutoUpdateIPRanges(),
            configManager.getUpdateIntervalHours());
    }
    
    public static GeoLocationFilter getInstance() {
        return INSTANCE;
    }
    
    /**
     * 检查主机访问是否应该被允许
     * 
     * @param host 目标主机（域名或IP）
     * @param port 目标端口
     * @return FilterResult 过滤结果
     */
    public FilterResult checkAccess(String host, int port) {
        totalRequests++;
        
        try {
            // 获取配置管理器
            ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
            
            // 如果地理位置过滤未启用，直接允许
            if (!configManager.isGeoLocationFilterEnabled()) {
                allowedRequests++;
                return FilterResult.allowed("地理位置过滤未启用");
            }
            
            // 1. 检查白名单（如果启用）
            if (configManager.isWhitelistEnabled() && isWhitelistDomain(host)) {
                allowedRequests++;
                return FilterResult.allowed("域名在白名单中");
            }

            // 2. 检查恶意域名黑名单（如果启用域名过滤）
            if (configManager.isDomainFilterEnabled() && isMaliciousDomain(host)) {
                blockedByDomainFilter++;
                // 调用性能监控统计
                com.xiang.proxy.server.metrics.PerformanceMetrics.getInstance().incrementDomainFilterBlocks();
                return FilterResult.blocked("域名在恶意黑名单中", BlockReason.MALICIOUS_DOMAIN);
            }

            // 3. 检查恶意关键词（如果启用关键词过滤）
            if (configManager.isKeywordFilterEnabled() && containsMaliciousKeywords(host)) {
                blockedByDomainFilter++;
                // 调用性能监控统计
                com.xiang.proxy.server.metrics.PerformanceMetrics.getInstance().incrementKeywordFilterBlocks();
                return FilterResult.blocked("域名包含恶意关键词", BlockReason.MALICIOUS_KEYWORDS);
            }
            
            // 4. 进行地理位置检查（如果启用海外可疑网站阻止）
            if (configManager.isBlockOverseasSuspicious()) {
                String resolvedIP = resolveHostToIP(host);
                if (resolvedIP != null) {
                    if (!GeoIPUtil.getInstance().isChinaIP(resolvedIP)) {
                        // 非中国IP，需要进一步检查
                        if (isSuspiciousOverseasSite(host, port)) {
                            blockedByGeoLocation++;
                            // 调用性能监控统计
                            com.xiang.proxy.server.metrics.PerformanceMetrics.getInstance().incrementOverseasSuspiciousBlocks();
                            return FilterResult.blocked("海外可疑网站", BlockReason.OVERSEAS_SUSPICIOUS);
                        }
                    }
                }
            }
            
            // 5. 默认允许访问
            allowedRequests++;
            return FilterResult.allowed("通过所有过滤检查");
            
        } catch (Exception e) {
            logger.warn("地理位置过滤检查异常，主机: {}, 错误: {}", host, e.getMessage());
            allowedRequests++;
            return FilterResult.allowed("过滤检查异常，默认允许");
        }
    }
    
    /**
     * 解析主机名到IP地址（带缓存）
     */
    private String resolveHostToIP(String host) {
        // 如果已经是IP地址，直接返回
        if (isIPAddress(host)) {
            return host;
        }
        
        // 检查缓存
        String cachedIP = dnsCache.get(host);
        Long cacheTime = dnsCacheTime.get(host);
        if (cachedIP != null && cacheTime != null) {
            ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
            long dnsCacheTimeout = configManager.getDnsCacheTimeoutMinutes() * 60 * 1000L;
            if ((System.currentTimeMillis() - cacheTime) < dnsCacheTimeout) {
                return cachedIP;
            }
        }
        
        // 进行DNS解析
        try {
            InetAddress address = InetAddress.getByName(host);
            String ip = address.getHostAddress();
            
            // 更新缓存
            dnsCache.put(host, ip);
            dnsCacheTime.put(host, System.currentTimeMillis());
            
            return ip;
        } catch (UnknownHostException e) {
            logger.debug("无法解析主机名: {}", host);
            return null;
        }
    }
    
    /**
     * 检查字符串是否为IP地址
     */
    private boolean isIPAddress(String host) {
        if (host == null || host.isEmpty()) {
            return false;
        }
        
        // 简单的IPv4检查
        String[] parts = host.split("\\.");
        if (parts.length == 4) {
            try {
                for (String part : parts) {
                    int num = Integer.parseInt(part);
                    if (num < 0 || num > 255) {
                        return false;
                    }
                }
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        // IPv6检查（简单）
        return host.contains(":");
    }
    
    /**
     * 初始化恶意域名黑名单
     */
    private void initializeMaliciousDomains() {
        try {
            // 使用配置文件加载器加载恶意域名和关键词
            Set<String> loadedDomains = MaliciousContentLoader.loadMaliciousDomains();
            Set<String> loadedKeywords = MaliciousContentLoader.loadMaliciousKeywords();

            maliciousDomains.addAll(loadedDomains);
            maliciousKeywords.addAll(loadedKeywords);

            // 添加基于关键词的域名模式
            initializeMaliciousDomainPatterns();

            logger.info("从配置文件加载恶意内容完成 - 域名: {}, 关键词: {}, 模式: {}",
                maliciousDomains.size(), maliciousKeywords.size(), maliciousDomainPatterns.size());

        } catch (Exception e) {
            logger.error("加载恶意内容配置失败，使用内置默认配置", e);
            // 如果配置文件加载失败，使用内置的基本配置
            initializeBuiltinMaliciousContent();
        }
    }

    /**
     * 初始化恶意域名模式
     */
    private void initializeMaliciousDomainPatterns() {
        // 免费顶级域名（高风险）
        maliciousDomainPatterns.add(Pattern.compile(".*\\.(tk|ml|ga|cf|gq)$", Pattern.CASE_INSENSITIVE));

        // 注意：不再基于关键词创建域名模式，避免与关键词过滤器冲突
        // 关键词匹配应该由 containsMaliciousKeywords() 方法专门处理

        // 特殊的高风险模式
        maliciousDomainPatterns.add(Pattern.compile(".*\\d{4,}\\.(tk|ml|ga|cf|gq)$", Pattern.CASE_INSENSITIVE)); // 数字+免费域名
        maliciousDomainPatterns.add(Pattern.compile(".*-\\d+\\.(com|net|org)$", Pattern.CASE_INSENSITIVE)); // 域名-数字格式
        maliciousDomainPatterns.add(Pattern.compile(".*[0-9]{8,}.*", Pattern.CASE_INSENSITIVE)); // 包含长数字串
    }

    /**
     * 初始化内置的恶意内容（备用）
     */
    private void initializeBuiltinMaliciousContent() {
        // 基本的恶意关键词
        maliciousKeywords.addAll(Arrays.asList(
            "porn", "xxx", "sex", "adult", "casino", "gambling", "bet", "poker",
            "phishing", "scam", "fraud", "malware", "virus", "trojan",
            "torrent", "pirate", "crack", "warez", "drugs", "weapon"
        ));

        // 基本的恶意域名
        maliciousDomains.addAll(Arrays.asList(
            "malware-site.com", "phishing-bank.com", "fake-paypal.com",
            "virus-download.net", "scam-lottery.org", "illegal-casino.biz"
        ));

        // 基本的域名模式
        maliciousDomainPatterns.add(Pattern.compile(".*\\.(tk|ml|ga|cf)$", Pattern.CASE_INSENSITIVE));
        maliciousDomainPatterns.add(Pattern.compile(".*porn.*", Pattern.CASE_INSENSITIVE));
        maliciousDomainPatterns.add(Pattern.compile(".*casino.*", Pattern.CASE_INSENSITIVE));
        maliciousDomainPatterns.add(Pattern.compile(".*phishing.*", Pattern.CASE_INSENSITIVE));

        logger.warn("使用内置恶意内容配置 - 域名: {}, 关键词: {}, 模式: {}",
            maliciousDomains.size(), maliciousKeywords.size(), maliciousDomainPatterns.size());
    }
    
    /**
     * 初始化白名单域名
     */
    private void initializeWhitelistDomains() {
        try {
            // 使用配置文件加载器加载白名单域名
            Set<String> loadedDomains = MaliciousContentLoader.loadWhitelistDomains();
            whitelistDomains.addAll(loadedDomains);

            // 初始化白名单域名模式
            initializeWhitelistDomainPatterns();

            logger.info("从配置文件加载白名单域名完成 - 域名: {}, 模式: {}",
                whitelistDomains.size(), whitelistDomainPatterns.size());

        } catch (Exception e) {
            logger.error("加载白名单配置失败，使用内置默认配置", e);
            // 如果配置文件加载失败，使用内置的基本配置
            initializeBuiltinWhitelistDomains();
        }
    }

    /**
     * 初始化白名单域名模式
     */
    private void initializeWhitelistDomainPatterns() {
        // 教育和政府机构
        whitelistDomainPatterns.add(Pattern.compile(".*\\.edu$", Pattern.CASE_INSENSITIVE));      // 教育机构
        whitelistDomainPatterns.add(Pattern.compile(".*\\.gov$", Pattern.CASE_INSENSITIVE));      // 政府网站
        whitelistDomainPatterns.add(Pattern.compile(".*\\.mil$", Pattern.CASE_INSENSITIVE));      // 军事网站
        whitelistDomainPatterns.add(Pattern.compile(".*\\.ac\\.[a-z]{2}$", Pattern.CASE_INSENSITIVE)); // 学术机构
        whitelistDomainPatterns.add(Pattern.compile(".*\\.edu\\.[a-z]{2}$", Pattern.CASE_INSENSITIVE)); // 国际教育机构

        // 知名技术公司的子域名
        whitelistDomainPatterns.add(Pattern.compile(".*\\.google\\.(com|org|net)$", Pattern.CASE_INSENSITIVE));
        whitelistDomainPatterns.add(Pattern.compile(".*\\.microsoft\\.(com|net)$", Pattern.CASE_INSENSITIVE));
        whitelistDomainPatterns.add(Pattern.compile(".*\\.amazon\\.(com|aws)$", Pattern.CASE_INSENSITIVE));
        whitelistDomainPatterns.add(Pattern.compile(".*\\.apple\\.(com|net)$", Pattern.CASE_INSENSITIVE));
        whitelistDomainPatterns.add(Pattern.compile(".*\\.github\\.(com|io)$", Pattern.CASE_INSENSITIVE));
        whitelistDomainPatterns.add(Pattern.compile(".*\\.stackoverflow\\.(com|net)$", Pattern.CASE_INSENSITIVE));
    }

    /**
     * 初始化内置的白名单域名（备用）
     */
    private void initializeBuiltinWhitelistDomains() {
        // 基本的白名单域名
        whitelistDomains.addAll(Arrays.asList(
            "google.com", "youtube.com", "github.com", "stackoverflow.com",
            "microsoft.com", "apple.com", "amazon.com", "wikipedia.org",
            "mozilla.org", "w3.org", "apache.org", "nodejs.org"
        ));

        // 基本的白名单模式
        whitelistDomainPatterns.add(Pattern.compile(".*\\.edu$", Pattern.CASE_INSENSITIVE));
        whitelistDomainPatterns.add(Pattern.compile(".*\\.gov$", Pattern.CASE_INSENSITIVE));
        whitelistDomainPatterns.add(Pattern.compile(".*\\.org$", Pattern.CASE_INSENSITIVE));

        logger.warn("使用内置白名单配置 - 域名: {}, 模式: {}",
            whitelistDomains.size(), whitelistDomainPatterns.size());
    }
    
    /**
     * 检查是否为白名单域名
     */
    private boolean isWhitelistDomain(String host) {
        if (host == null) return false;
        
        String domain = extractDomain(host);
        
        // 精确匹配
        if (whitelistDomains.contains(domain)) {
            return true;
        }
        
        // 模式匹配
        for (Pattern pattern : whitelistDomainPatterns) {
            if (pattern.matcher(domain).matches()) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为恶意域名
     */
    private boolean isMaliciousDomain(String host) {
        if (host == null) return false;
        
        String domain = extractDomain(host);
        
        // 精确匹配
        if (maliciousDomains.contains(domain)) {
            return true;
        }
        
        // 模式匹配
        for (Pattern pattern : maliciousDomainPatterns) {
            if (pattern.matcher(domain).matches()) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查域名是否包含恶意关键词
     */
    private boolean containsMaliciousKeywords(String host) {
        if (host == null) return false;
        
        String domain = extractDomain(host).toLowerCase();
        
        for (String keyword : maliciousKeywords) {
            if (domain.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 提取域名（去除子域名）
     */
    private String extractDomain(String host) {
        if (host == null || host.isEmpty()) {
            return host;
        }
        
        // 如果是IP地址，直接返回
        if (isIPAddress(host)) {
            return host;
        }
        
        // 简单的域名提取逻辑
        String[] parts = host.split("\\.");
        if (parts.length >= 2) {
            return parts[parts.length - 2] + "." + parts[parts.length - 1];
        }
        
        return host;
    }
    
    /**
     * 检查是否为可疑的海外网站
     */
    private boolean isSuspiciousOverseasSite(String host, int port) {
        // 检查特定端口（常用于不良内容的端口）
        if (port == 8080 || port == 9999 || port == 6666) {
            return true;
        }
        
        // 检查域名长度（过短或过长的域名可能可疑）
        String domain = extractDomain(host);
        if (domain.length() < 4 || domain.length() > 50) {
            return true;
        }
        
        // 检查是否包含数字过多（可能是临时域名）
        long digitCount = domain.chars().filter(Character::isDigit).count();
        if (digitCount > domain.length() / 2) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取过滤统计信息
     */
    public FilterStats getStats() {
        return new FilterStats(totalRequests, blockedByGeoLocation, 
                              blockedByDomainFilter, allowedRequests);
    }
    
    /**
     * 清理所有缓存中的过期条目
     */
    public void cleanupCache() {
        long currentTime = System.currentTimeMillis();
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
        long dnsCacheTimeout = configManager.getDnsCacheTimeoutMinutes() * 60 * 1000L;
        long ipCacheTimeout = configManager.getIpCacheTimeoutMinutes() * 60 * 1000L;
        int maxCacheSize = configManager.getMaxCacheSize();

        // 清理过期的DNS缓存
        List<String> expiredDnsKeys = new ArrayList<>();
        for (Map.Entry<String, Long> entry : dnsCacheTime.entrySet()) {
            if (currentTime - entry.getValue() > dnsCacheTimeout) {
                expiredDnsKeys.add(entry.getKey());
            }
        }

        for (String key : expiredDnsKeys) {
            dnsCache.remove(key);
            dnsCacheTime.remove(key);
        }

        // 清理过期的IP地理位置缓存
        List<String> expiredIpKeys = new ArrayList<>();
        for (Map.Entry<String, Long> entry : ipLocationCacheTime.entrySet()) {
            if (currentTime - entry.getValue() > ipCacheTimeout) {
                expiredIpKeys.add(entry.getKey());
            }
        }

        for (String key : expiredIpKeys) {
            ipLocationCache.remove(key);
            ipLocationCacheTime.remove(key);
        }

        // 如果缓存超过最大大小，清理最旧的条目
        cleanupOldestCacheEntries(maxCacheSize);

        if (!expiredDnsKeys.isEmpty() || !expiredIpKeys.isEmpty()) {
            logger.debug("清理了 {} 个过期的DNS缓存条目和 {} 个过期的IP缓存条目",
                        expiredDnsKeys.size(), expiredIpKeys.size());
        }
    }

    /**
     * 清理最旧的缓存条目以保持缓存大小在限制内
     */
    private void cleanupOldestCacheEntries(int maxCacheSize) {
        int totalCacheSize = dnsCache.size() + ipLocationCache.size();
        if (totalCacheSize <= maxCacheSize) {
            return;
        }

        int toRemove = totalCacheSize - maxCacheSize;
        List<Map.Entry<String, Long>> allEntries = new ArrayList<>();

        // 收集所有缓存条目及其时间戳
        for (Map.Entry<String, Long> entry : dnsCacheTime.entrySet()) {
            allEntries.add(entry);
        }
        for (Map.Entry<String, Long> entry : ipLocationCacheTime.entrySet()) {
            allEntries.add(entry);
        }

        // 按时间戳排序，最旧的在前
        allEntries.sort(Map.Entry.comparingByValue());

        // 移除最旧的条目
        for (int i = 0; i < toRemove && i < allEntries.size(); i++) {
            String key = allEntries.get(i).getKey();
            dnsCache.remove(key);
            dnsCacheTime.remove(key);
            ipLocationCache.remove(key);
            ipLocationCacheTime.remove(key);
        }

        logger.debug("清理了 {} 个最旧的缓存条目以保持缓存大小限制", toRemove);
    }

    /**
     * 重新加载恶意内容配置
     * 支持运行时动态更新配置
     */
    public void reloadMaliciousContent() {
        logger.info("开始重新加载恶意内容配置...");

        // 清空现有配置
        maliciousDomains.clear();
        maliciousKeywords.clear();
        maliciousDomainPatterns.clear();

        // 重新初始化
        initializeMaliciousDomains();

        logger.info("恶意内容配置重新加载完成 - 域名: {}, 关键词: {}, 模式: {}",
            maliciousDomains.size(), maliciousKeywords.size(), maliciousDomainPatterns.size());
    }

    /**
     * 重新加载白名单配置
     * 支持运行时动态更新配置
     */
    public void reloadWhitelistContent() {
        logger.info("开始重新加载白名单配置...");

        // 清空现有配置
        whitelistDomains.clear();
        whitelistDomainPatterns.clear();

        // 重新初始化
        initializeWhitelistDomains();

        logger.info("白名单配置重新加载完成 - 域名: {}, 模式: {}",
            whitelistDomains.size(), whitelistDomainPatterns.size());
    }

    /**
     * 重新加载所有配置
     */
    public void reloadAllConfigurations() {
        logger.info("开始重新加载所有过滤配置...");

        reloadMaliciousContent();
        reloadWhitelistContent();

        // 清理缓存，强制重新判断
        dnsCache.clear();
        dnsCacheTime.clear();

        logger.info("所有过滤配置重新加载完成");
    }

    /**
     * 添加自定义恶意域名
     */
    public void addMaliciousDomain(String domain) {
        if (domain != null && !domain.trim().isEmpty()) {
            maliciousDomains.add(domain.toLowerCase().trim());
            logger.info("添加恶意域名: {}", domain);
        }
    }

    /**
     * 移除恶意域名
     */
    public void removeMaliciousDomain(String domain) {
        if (domain != null && maliciousDomains.remove(domain.toLowerCase().trim())) {
            logger.info("移除恶意域名: {}", domain);
        }
    }

    /**
     * 添加自定义白名单域名
     */
    public void addWhitelistDomain(String domain) {
        if (domain != null && !domain.trim().isEmpty()) {
            whitelistDomains.add(domain.toLowerCase().trim());
            logger.info("添加白名单域名: {}", domain);
        }
    }

    /**
     * 移除白名单域名
     */
    public void removeWhitelistDomain(String domain) {
        if (domain != null && whitelistDomains.remove(domain.toLowerCase().trim())) {
            logger.info("移除白名单域名: {}", domain);
        }
    }
}
