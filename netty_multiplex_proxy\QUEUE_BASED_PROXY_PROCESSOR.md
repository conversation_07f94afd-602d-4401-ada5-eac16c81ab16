# 队列化代理处理器架构

## 概述

新的 `ProxyProcessor` 采用多队列、多线程的异步处理架构，通过将请求按照目标主机和端口进行哈希分配到不同队列，实现高并发、高吞吐量的代理处理能力。

## 核心特性

### 1. 多队列分发机制
- 基于 `host:port` 的哈希值将请求分配到不同队列
- 相同目标的请求会被分配到同一队列，保证处理顺序
- 默认队列数量为 `CPU核心数 × 2`，可配置

### 2. 专用工作线程
- 每个队列对应一个专用工作线程
- 线程持续从队列中拉取请求进行处理
- 避免线程池调度开销，提高处理效率

### 3. 异步处理模式
- 请求提交后立即返回 `CompletableFuture`
- 支持非阻塞的异步处理流程
- 客户端可以并发提交大量请求

### 4. 队列监控和统计
- 实时监控各队列的大小和处理状态
- 提供详细的统计信息用于性能调优
- 支持队列满载保护机制

## 架构图

```
[Inbound Request] 
       ↓
[Hash Calculator] → Queue Index = hash(host:port) % queueCount
       ↓
[Queue Dispatcher]
       ↓
┌─────────────────────────────────────────────────────┐
│  Queue 0    Queue 1    Queue 2    ...    Queue N   │
│     ↓          ↓          ↓                  ↓     │
│ Worker 0   Worker 1   Worker 2   ...   Worker N    │
└─────────────────────────────────────────────────────┘
       ↓
[Router] → [OutboundHandler] → [Target Server]
```

## 使用方式

### 基本使用

```java
// 创建默认配置的处理器
Router router = new RouterImpl();
ProxyProcessor processor = new ProxyProcessor(router);

// 启动处理器
processor.start();

// 处理请求
ProxyRequest request = ProxyRequest.builder()
    .protocol("HTTP")
    .target("example.com", 80)
    .data(requestData)
    .clientChannel(clientChannel)
    .build();

CompletableFuture<ProxyResponse> future = processor.processRequest(request);
future.whenComplete((response, throwable) -> {
    if (throwable != null) {
        logger.error("请求处理失败", throwable);
    } else {
        logger.info("请求处理成功: {}", response.isSuccess());
    }
});

// 关闭处理器
processor.shutdown();
```

### 自定义配置

```java
// 高性能配置
ProxyProcessorConfig config = ProxyProcessorConfig.highPerformanceConfig();
ProxyProcessor processor = new ProxyProcessor(router, config);

// 或者自定义配置
ProxyProcessorConfig customConfig = new ProxyProcessorConfig()
    .setQueueCount(16)
    .setQueueCapacity(20000)
    .setShutdownTimeoutSeconds(60)
    .setWorkerThreadPrefix("my-proxy-worker-");

ProxyProcessor processor = new ProxyProcessor(router, customConfig);
```

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| queueCount | CPU核心数×2 | 队列数量，也是工作线程数量 |
| queueCapacity | 10000 | 每个队列的最大容量 |
| shutdownTimeoutSeconds | 30 | 关闭时等待工作线程结束的超时时间 |
| enableQueueMonitoring | true | 是否启用队列监控 |
| workerThreadPrefix | "proxy-worker-" | 工作线程名称前缀 |

## 性能优势

### 1. 负载均衡
- 通过哈希分发实现请求的均匀分布
- 避免单线程瓶颈，充分利用多核CPU

### 2. 缓存友好
- 相同目标的请求在同一线程处理
- 提高连接复用率和缓存命中率

### 3. 低延迟
- 减少线程切换开销
- 专用线程避免线程池调度延迟

### 4. 高吞吐
- 支持大量并发请求
- 队列缓冲机制平滑流量峰值

## 监控和调优

### 队列状态监控

```java
// 获取队列统计信息
ProxyProcessor.QueueStats stats = processor.getQueueStats();
logger.info("总队列大小: {}", stats.getTotalQueueSize());
logger.info("已处理请求数: {}", stats.getProcessedRequests());

// 查看各队列详情
int[] queueSizes = stats.getQueueSizes();
for (int i = 0; i < queueSizes.length; i++) {
    logger.info("队列 {} 大小: {}", i, queueSizes[i]);
}
```

### 性能调优建议

1. **队列数量调优**
   - CPU密集型：队列数 = CPU核心数
   - IO密集型：队列数 = CPU核心数 × 2-4
   - 混合型：根据实际测试调整

2. **队列容量调优**
   - 根据内存大小和请求大小调整
   - 过小可能导致请求被拒绝
   - 过大可能导致内存溢出

3. **监控关键指标**
   - 队列大小：避免持续满载
   - 处理延迟：监控请求处理时间
   - 拒绝率：监控队列满载导致的请求拒绝

## 故障处理

### 队列满载
- 当队列容量达到上限时，新请求会被立即拒绝
- 返回失败响应，避免系统过载
- 建议增加队列容量或优化处理速度

### 工作线程异常
- 单个工作线程异常不影响其他队列
- 异常会被捕获并记录日志
- 线程会继续处理后续请求

### 优雅关闭
- 停止接收新请求
- 等待队列中的请求处理完成
- 超时后强制关闭工作线程
- 清理所有资源

## 最佳实践

1. **合理设置队列数量**：根据系统负载特性选择合适的队列数量
2. **监控队列状态**：定期检查队列大小，及时发现性能瓶颈
3. **优雅关闭**：确保在系统关闭时正确调用 `shutdown()` 方法
4. **异常处理**：妥善处理请求处理过程中的异常
5. **资源管理**：注意 ByteBuf 等资源的正确释放

## 与原架构对比

| 特性 | 原架构 | 新架构 |
|------|--------|--------|
| 处理模式 | 同步阻塞 | 异步队列 |
| 并发能力 | 受线程池限制 | 高并发支持 |
| 负载均衡 | 无 | 基于哈希的均衡分发 |
| 监控能力 | 基础 | 详细的队列统计 |
| 资源利用 | 一般 | 更高效的CPU利用 |
| 扩展性 | 有限 | 良好的水平扩展能力 |

这种队列化架构特别适合高并发、大吞吐量的代理场景，能够显著提升系统的处理能力和稳定性。
</text>