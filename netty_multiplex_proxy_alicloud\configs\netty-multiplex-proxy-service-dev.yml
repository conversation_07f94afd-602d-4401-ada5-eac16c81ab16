server:
  port: 8887

spring:
  application:
    name: netty-multiplex-proxy-service
  lifecycle:
    timeout-per-shutdown-phase: 30s
  cloud:
    nacos:
      discovery:
        #直接指定公网IP地址注册
        ip: **************
# Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  health:
    defaults:
      enabled: true

# 日志配置
logging:
  level:
    com.xiang.proxy: INFO
    org.springframework: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
