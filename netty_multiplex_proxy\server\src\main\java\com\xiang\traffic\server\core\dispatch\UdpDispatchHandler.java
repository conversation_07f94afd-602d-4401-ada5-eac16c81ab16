package com.xiang.traffic.server.core.dispatch;

import com.xiang.traffic.protocol.ProxyResponseMessage;
import com.xiang.traffic.server.core.ProxyTask;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.DatagramPacket;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:20
 */
class UdpDispatchHandler extends ChannelInboundHandlerAdapter {

    private static final Logger log = LoggerFactory.getLogger(UdpDispatchHandler.class);

    private final ProxyTask proxyTask;

    private final String host;

    private final int port;

    UdpDispatchHandler(ProxyTask task) {
        this.proxyTask = task;
        this.host = proxyTask.getRequestMessage().getHost();
        this.port = proxyTask.getRequestMessage().getPort();
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        ByteBuf buf = proxyTask.getRequestMessage().getMessage();
        DatagramPacket packet = new DatagramPacket(buf, new InetSocketAddress(host, port));
        ctx.writeAndFlush(packet);
        super.channelActive(ctx);
    }


    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        if (msg instanceof DatagramPacket) {
            try {
                channelRead0(ctx, (DatagramPacket) msg);
            } finally {
                ReferenceCountUtil.release(msg);
            }
        } else {
            ctx.fireChannelRead(msg);
        }
    }

    protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket msg) throws Exception {
        log.trace("Receive from {}:{} Datagram.", host, port);

        ProxyResponseMessage prm = new ProxyResponseMessage(proxyTask.getRequestMessage().serialId());
        prm.setState(ProxyResponseMessage.State.SUCCESS);
        prm.setMessage(msg.content().retain());
        try {
            proxyTask.session().writeAndFlushMessage(prm.serialize(ctx.alloc()));
        } catch (IllegalStateException e) {
            ctx.close();
        }
    }

}
