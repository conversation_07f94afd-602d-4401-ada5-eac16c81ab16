package com.xiang.proxy.server.router;

import com.xiang.proxy.server.core.ProxyRequest;

import java.util.List;

/**
 * 路由器接口
 * 负责根据路由规则决定请求应该转发到哪个outbound组件
 */
public interface Router {
    
    /**
     * 路由请求到合适的outbound
     * @param request 代理请求
     * @return 路由结果
     */
    RouteResult route(ProxyRequest request);
    
    /**
     * 添加路由规则
     * @param rule 路由规则
     */
    void addRoute(RouteRule rule);
    
    /**
     * 移除路由规则
     * @param ruleId 规则ID
     * @return 是否成功移除
     */
    boolean removeRoute(String ruleId);
    
    /**
     * 获取所有路由规则
     * @return 路由规则列表
     */
    List<RouteRule> getRoutes();
    
    /**
     * 获取指定ID的路由规则
     * @param ruleId 规则ID
     * @return 路由规则，如果不存在返回null
     */
    RouteRule getRoute(String ruleId);
    
    /**
     * 更新路由规则
     * @param rule 路由规则
     * @return 是否成功更新
     */
    boolean updateRoute(RouteRule rule);
    
    /**
     * 启用/禁用路由规则
     * @param ruleId 规则ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean setRouteEnabled(String ruleId, boolean enabled);
    
    /**
     * 重新加载路由配置
     */
    void reloadRoutes();
    
    /**
     * 获取路由统计信息
     * @return 路由统计
     */
    RouteStatistics getStatistics();
    
    /**
     * 清空所有路由规则
     */
    void clearRoutes();
    
    /**
     * 验证路由规则
     * @param rule 路由规则
     * @return 验证结果
     */
    ValidationResult validateRule(RouteRule rule);
}