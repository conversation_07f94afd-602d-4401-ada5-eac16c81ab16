# 配置优化指南

## 📋 概述

本指南提供了针对不同环境和负载场景的proxy-server配置优化建议，帮助您获得最佳性能。

## 🎯 配置文件层次

### 1. 开发环境配置
**文件**: `configs/development/server/proxy-server.yml`
**特点**: 资源消耗低，便于调试
```yaml
performance:
  boss-threads: 0
  worker-threads: 0
  io-ratio: 60
  max-worker-threads: 32
  min-worker-threads: 2

pool:
  max-connections:
    per-host: 20
  idle-timeout:
    seconds: 60
```

### 2. 生产环境配置
**文件**: `configs/production/server/proxy-server.yml`
**特点**: 平衡性能和稳定性
```yaml
performance:
  boss-threads: 0
  worker-threads: 0
  io-ratio: 75
  max-worker-threads: 0
  min-worker-threads: 4

pool:
  max-connections:
    per-host: 50
  idle-timeout:
    seconds: 120
```

### 3. 高性能配置
**文件**: `configs/production/server/proxy-server-high-performance.yml`
**特点**: 高并发优化
```yaml
performance:
  boss-threads: 2
  worker-threads: 0
  io-ratio: 80
  min-worker-threads: 8

pool:
  max-connections:
    per-host: 50
  idle-timeout:
    seconds: 120
```

### 4. 超高性能配置
**文件**: `configs/production/server/proxy-server-ultra-performance.yml`
**特点**: 极限性能优化
```yaml
performance:
  boss-threads: 4
  worker-threads: 0
  io-ratio: 90
  min-worker-threads: 16

pool:
  max-connections:
    per-host: 100
  idle-timeout:
    seconds: 300
```

## ⚙️ 关键参数调优

### 线程池配置

#### Boss线程数 (boss-threads)
- **开发环境**: 0 (自动=1)
- **生产环境**: 0 (自动=1-2)
- **高并发**: 2-4
- **原则**: 通常1个足够，高并发场景可增加到2-4个

#### Worker线程数 (worker-threads)
- **计算公式**: CPU核心数 × (1 + I/O等待时间/CPU时间)
- **自动计算**: 设置为0，系统根据I/O比例智能计算
- **手动设置**: CPU核心数 × 4-8倍
- **最小值**: 建议不少于CPU核心数

#### I/O比例 (io-ratio)
- **代理服务器**: 70-90 (I/O密集型)
- **计算密集**: 30-50
- **混合负载**: 50-70

### 连接池配置

#### 每主机最大连接数 (max-connections.per-host)
- **低负载**: 10-20
- **中等负载**: 20-50
- **高负载**: 50-100
- **超高负载**: 100+

#### 空闲超时 (idle-timeout.seconds)
- **开发环境**: 60秒
- **生产环境**: 120-300秒
- **原则**: 平衡连接复用和资源占用

### 监控配置

#### 报告间隔 (metrics.report.interval.seconds)
- **开发环境**: 60-300秒
- **生产环境**: 180-600秒
- **原则**: 降低监控开销，但保持可观测性

### 黑名单配置

#### 失败阈值 (blacklist.failure.threshold)
- **严格模式**: 3-5次
- **宽松模式**: 5-10次
- **原则**: 平衡误判和保护效果

#### 缓存超时 (blacklist.cache.timeout.seconds)
- **快速恢复**: 180-300秒
- **长期保护**: 300-600秒

## 🚀 JVM优化参数

### 内存配置
```bash
# 基础配置 (4GB堆内存)
-Xms4g -Xmx4g

# 高性能配置 (8GB堆内存)
-Xms8g -Xmx8g

# 超高性能配置 (16GB堆内存)
-Xms16g -Xmx16g

# 直接内存
-XX:MaxDirectMemorySize=4g  # 堆内存的1/2-1/4
```

### 垃圾收集器
```bash
# G1GC (推荐)
-XX:+UseG1GC
-XX:MaxGCPauseMillis=100
-XX:G1HeapRegionSize=16m

# ZGC (Java 11+, 超低延迟)
-XX:+UnlockExperimentalVMOptions
-XX:+UseZGC

# Parallel GC (高吞吐量)
-XX:+UseParallelGC
-XX:ParallelGCThreads=8
```

### Netty优化
```bash
# 内存分配器
-Dio.netty.allocator.type=pooled
-Dio.netty.allocator.numDirectArenas=16
-Dio.netty.allocator.numHeapArenas=16

# 性能优化
-Dio.netty.recycler.maxCapacityPerThread=0
-Dio.netty.leakDetection.level=DISABLED  # 生产环境
```

## 📊 性能调优流程

### 1. 基准测试
```bash
# 使用默认配置进行基准测试
java -jar proxy-server.jar

# 记录关键指标
- 吞吐量 (requests/second)
- 延迟 (P50, P95, P99)
- 资源使用率 (CPU, Memory)
- 错误率
```

### 2. 逐步优化
1. **线程池优化**: 调整worker线程数和I/O比例
2. **连接池优化**: 增加每主机连接数
3. **内存优化**: 调整JVM堆大小和GC参数
4. **网络优化**: 调整缓冲区大小和水位标记

### 3. 压力测试
```bash
# 使用工具进行压力测试
# 例如: wrk, ab, JMeter

wrk -t12 -c400 -d30s --latency http://localhost:8888/
```

### 4. 监控和调整
- 观察性能指标变化
- 根据瓶颈调整配置
- 重复测试验证效果

## 🎯 不同场景的推荐配置

### 场景1: 小型企业 (< 1000并发)
```yaml
performance:
  boss-threads: 0
  worker-threads: 0
  io-ratio: 70
  min-worker-threads: 4

pool:
  max-connections:
    per-host: 20
```

### 场景2: 中型企业 (1000-10000并发)
```yaml
performance:
  boss-threads: 2
  worker-threads: 0
  io-ratio: 75
  min-worker-threads: 8

pool:
  max-connections:
    per-host: 50
```

### 场景3: 大型企业 (10000+并发)
```yaml
performance:
  boss-threads: 4
  worker-threads: 0
  io-ratio: 85
  min-worker-threads: 16

pool:
  max-connections:
    per-host: 100
```

## ⚠️ 注意事项

### 1. 资源限制
- 确保系统有足够的文件描述符
- 监控内存使用，避免OOM
- 注意网络带宽限制

### 2. 配置验证
- 在测试环境充分验证
- 逐步部署到生产环境
- 准备回滚方案

### 3. 监控告警
- 设置关键指标告警
- 定期检查性能趋势
- 及时调整配置

## 📈 性能优化检查清单

- [ ] 线程池配置是否合理
- [ ] 连接池大小是否足够
- [ ] JVM参数是否优化
- [ ] 操作系统参数是否调优
- [ ] 网络配置是否优化
- [ ] 监控系统是否完善
- [ ] 压力测试是否通过
- [ ] 生产环境是否稳定

## 🔧 故障排查

### 高CPU使用率
1. 检查线程数是否过多
2. 检查GC频率和时间
3. 分析热点代码

### 高内存使用率
1. 检查连接池大小
2. 检查缓存配置
3. 分析内存泄漏

### 高延迟
1. 检查网络配置
2. 检查连接池命中率
3. 分析慢查询

### 连接错误
1. 检查文件描述符限制
2. 检查网络配置
3. 检查目标服务器状态
