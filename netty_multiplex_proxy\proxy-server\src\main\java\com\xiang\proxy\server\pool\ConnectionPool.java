package com.xiang.proxy.server.pool;

import com.xiang.proxy.server.config.ConnectionPoolConfig;
import io.netty.channel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 连接池实现
 * 管理后端服务器连接的复用
 */
public class ConnectionPool {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionPool.class);
    private static final ConnectionPool INSTANCE = new ConnectionPool();

    private final Map<String, Queue<PooledConnection>> connectionPool = new ConcurrentHashMap<>();

    // 定时清理任务
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "ConnectionPool-Cleanup");
        t.setDaemon(true);
        return t;
    });

    private volatile boolean started = false;

    // 传统连接池的统计信息（用于回退模式）
    private final AtomicLong legacyHitCount = new AtomicLong(0);
    private final AtomicLong legacyMissCount = new AtomicLong(0);

    private ConnectionPool() {
    }

    public static ConnectionPool getInstance() {
        return INSTANCE;
    }

    /**
     * 启动连接池（包括定时清理任务）
     */
    public synchronized void start() {
        if (!started) {
            started = true;
            // 启动定时清理任务
            cleanupExecutor.scheduleAtFixedRate(
                    this::cleanupExpiredConnections,
                    ConnectionPoolConfig.POOL_CLEANUP_INTERVAL,
                    ConnectionPoolConfig.POOL_CLEANUP_INTERVAL,
                    TimeUnit.MILLISECONDS
            );
            logger.info("连接池已启动，清理间隔: {}ms", ConnectionPoolConfig.POOL_CLEANUP_INTERVAL);
        }
    }

    /**
     * 获取连接
     *
     * @param hostKey 主机标识 (host:port)
     * @return 可用的连接，如果没有则返回null
     */
    public Channel getConnection(String hostKey) {
        if (!ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
            return null;
        }

        if (hostKey == null || hostKey.trim().isEmpty()) {
            logger.warn("无效的主机键值: {}", hostKey);
            legacyMissCount.incrementAndGet();
            return null;
        }

        Queue<PooledConnection> connections = connectionPool.get(hostKey);
        if (connections == null || connections.isEmpty()) {
            logger.debug("没有可用连接，需要创建新连接: {}", hostKey);
            legacyMissCount.incrementAndGet();
            return null;
        }

        // 使用更高效的连接获取策略
        PooledConnection conn;
        while ((conn = connections.poll()) != null) {
            Channel channel = conn.getChannelWithoutUpdate();

            // 检查连接是否超时（包括空闲超时和存活时间超时）
            if (isConnectionExpired(conn)) {
                if (channel != null && channel.isActive()) {
                    long currentTime = System.currentTimeMillis();
                    long idleTime = currentTime - conn.getLastUsedTime();
                    long totalLifetime = currentTime - conn.getCreationTime();
                    
                    String reason = idleTime > ConnectionPoolConfig.CONNECTION_IDLE_TIMEOUT ? 
                                  "空闲超时" : "存活时间超时";
                    logger.debug("连接已{}，关闭连接: {}，连接ID: {} (空闲: {}ms, 存活: {}ms)", 
                               reason, hostKey, channel.id(), idleTime, totalLifetime);
                    safeCloseChannel(channel);
                }
                continue;
            }

            // 严格检查连接是否可用
            if (isConnectionUsable(channel)) {
                // 清理连接状态并准备重用
                if (prepareConnectionForReuse(channel, hostKey)) {
                    // 更新使用时间并返回连接
                    Channel activeChannel = conn.getChannel();
                    logger.debug("从连接池获取连接: {}，连接ID: {}，剩余连接数: {}",
                            hostKey, activeChannel.id(), connections.size());
                    legacyHitCount.incrementAndGet();
                    return activeChannel;
                } else {
                    // 准备失败，继续寻找下一个连接
                    continue;
                }
            } else {
                // 连接已失效，关闭
                if (channel != null) {
                    logger.debug("连接已失效，关闭连接: {}，连接ID: {} (active:{}, writable:{}, open:{})",
                            hostKey, channel.id(), channel.isActive(), channel.isWritable(), channel.isOpen());
                    safeCloseChannel(channel);
                }
            }
        }

        logger.debug("没有可用的有效连接，需要创建新连接: {}", hostKey);
        legacyMissCount.incrementAndGet();
        return null;
    }

    /**
     * 检查连接是否可用
     */
    private boolean isConnectionUsable(Channel channel) {
        if (channel == null) {
            return false;
        }

        // 基本状态检查
        if (!channel.isActive() || !channel.isWritable() || !channel.isOpen()) {
            return false;
        }

        // 检查连接是否正在被其他线程使用
        // 这里可以添加更多的状态检查逻辑

        return true;
    }

    /**
     * 检查连接是否已过期
     */
    private boolean isConnectionExpired(PooledConnection conn) {
        long currentTime = System.currentTimeMillis();
        long idleTime = currentTime - conn.getLastUsedTime();
        long totalLifetime = currentTime - conn.getCreationTime();
        
        // 检查空闲超时
        if (idleTime > ConnectionPoolConfig.CONNECTION_IDLE_TIMEOUT) {
            logger.debug("连接空闲超时: {}ms > {}ms", idleTime, ConnectionPoolConfig.CONNECTION_IDLE_TIMEOUT);
            return true;
        }
        
        // 检查连接存活时间
        if (totalLifetime > ConnectionPoolConfig.CONNECTION_MAX_LIFETIME) {
            logger.debug("连接存活时间超时: {}ms > {}ms", totalLifetime, ConnectionPoolConfig.CONNECTION_MAX_LIFETIME);
            return true;
        }
        
        return false;
    }

    /**
     * 准备连接以供重用
     */
    private boolean prepareConnectionForReuse(Channel channel, String hostKey) {
        try {
            // 清理所有可能存在的旧处理器
            ChannelPipeline pipeline = channel.pipeline();

            // 移除所有自定义处理器，保留基础的编解码器
            String[] handlersToRemove = {
                    "multiplexBackendHandler", "multiplex-backend-handler",
                    "udp-response-handler", "httpHandler", "sslHandler",
                    "timeoutHandler", "authHandler", "proxyHandler"
            };

            for (String handlerName : handlersToRemove) {
                if (pipeline.get(handlerName) != null) {
                    pipeline.remove(handlerName);
                    logger.debug("清理连接池连接的处理器: {}", handlerName);
                }
            }

            // 清理Channel的用户属性，避免状态污染
            channel.attr(io.netty.util.AttributeKey.valueOf("sessionId")).set(null);
            channel.attr(io.netty.util.AttributeKey.valueOf("clientChannel")).set(null);

            // 验证连接在清理后仍然可用
            if (!isConnectionUsable(channel)) {
                logger.warn("连接在清理后变为不可用: {}", hostKey);
                safeCloseChannel(channel);
                return false;
            }

            // 额外的连接健康检查
            if (!performConnectionHealthCheck(channel, hostKey)) {
                logger.warn("连接健康检查失败: {}", hostKey);
                safeCloseChannel(channel);
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.warn("准备连接重用时发生异常: {}，连接ID: {}", hostKey, channel.id(), e);
            safeCloseChannel(channel);
            return false;
        }
    }

    /**
     * 执行连接健康检查 - 优化性能，减少不必要的检查
     */
    private boolean performConnectionHealthCheck(Channel channel, String hostKey) {
        try {
            // 快速检查：基本状态
            if (!channel.isWritable() || channel.remoteAddress() == null) {
                return false;
            }

            // 检查EventLoop状态（较重的操作，放在后面）
            if (channel.eventLoop().isShuttingDown() || channel.eventLoop().isShutdown()) {
                logger.debug("连接的EventLoop已关闭，健康检查失败: {}", hostKey);
                return false;
            }

            // 检查是否有活跃的业务处理器（最重的操作，放在最后）
            if (channel.pipeline().context("multiplexBackendHandler") != null ||
                channel.pipeline().context("multiplex-backend-handler") != null) {
                logger.debug("连接仍有活跃处理器，健康检查失败: {}", hostKey);
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.debug("连接健康检查异常: {}, {}", hostKey, e.getMessage());
            return false;
        }
    }

    /**
     * 安全关闭连接
     */
    private void safeCloseChannel(Channel channel) {
        if (channel != null) {
            try {
                channel.close();
            } catch (Exception e) {
                logger.debug("关闭连接时出现异常: {}", e.getMessage());
            }
        }
    }

    /**
     * 归还连接到连接池
     *
     * @param hostKey 主机标识 (host:port)
     * @param channel 要归还的连接
     */
    public void returnConnection(String hostKey, Channel channel) {
        // 快速检查，无需同步
        if (!ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
            logger.debug("连接池未启用，关闭连接: {}，连接ID: {}",
                    hostKey, channel != null ? channel.id() : "null");
            safeCloseChannel(channel);
            return;
        }

        if (channel == null) {
            logger.warn("尝试归还空连接到连接池: {}", hostKey);
            return;
        }

        if (hostKey == null || hostKey.trim().isEmpty()) {
            logger.warn("主机键值无效，关闭连接: 连接ID: {}", channel.id());
            safeCloseChannel(channel);
            return;
        }

        // 在同步块外进行连接适用性检查和清理
        if (!isConnectionSuitableForReturn(channel, hostKey)) {
            return;
        }

        // 在同步块外进行连接清理，减少锁持有时间
        if (!cleanupConnectionForReturn(channel, hostKey)) {
            logger.debug("连接清理失败，关闭连接: {}，连接ID: {}", hostKey, channel.id());
            safeCloseChannel(channel);
            return;
        }

        try {
            // 获取或创建该主机的连接队列（ConcurrentHashMap是线程安全的）
            Queue<PooledConnection> connections = connectionPool.computeIfAbsent(
                    hostKey, k -> new ConcurrentLinkedQueue<>()
            );

            // 检查连接数是否超过限制
            int maxConnections = ConnectionPoolConfig.MAX_CONNECTIONS_PER_HOST;
            if (connections.size() >= maxConnections) {
                // 连接池已满，关闭连接
                logger.debug("连接池已满，关闭连接: {}，连接ID: {}", hostKey, channel.id());
                safeCloseChannel(channel);
                return;
            }

            // 添加连接到池中（ConcurrentLinkedQueue是线程安全的）
            PooledConnection pooledConnection = new PooledConnection(channel);
            connections.offer(pooledConnection);
            logger.debug("连接归还到连接池: {}，连接ID: {}，当前池中连接数: {}",
                    hostKey, channel.id(), connections.size());

        } catch (Exception e) {
            logger.error("归还连接到连接池时发生异常: {}，连接ID: {}",
                    hostKey, channel.id(), e);
            safeCloseChannel(channel);
        }
    }

    /**
     * 检查连接是否适合归还到连接池
     */
    private boolean isConnectionSuitableForReturn(Channel channel, String hostKey) {
        if (!channel.isActive()) {
            logger.debug("连接已失效，无法归还到连接池: {}，连接ID: {}", hostKey, channel.id());
            return false;
        }

        // 基本状态检查（writable 可能短暂为 false，这里只要求 open）
        if (!channel.isOpen()) {
            logger.debug("连接状态不佳，无法归还: {}，连接ID: {} (active:{}, writable:{}, open:{})",
                    hostKey, channel.id(), channel.isActive(), channel.isWritable(), channel.isOpen());
            safeCloseChannel(channel);
            return false;
        }

        // 不在此处判断是否还挂载业务处理器，交由清理阶段移除处理器后再复用


        return true;
    }

    /**
     * 清理连接状态以准备归还
     */
    private boolean cleanupConnectionForReturn(Channel channel, String hostKey) {
        try {
            ChannelPipeline pipeline = channel.pipeline();

            // 移除所有可能影响重用的处理器
            String[] handlersToRemove = {"multiplexBackendHandler", "multiplex-backend-handler", "udp-response-handler", "httpHandler", "sslHandler", "timeoutHandler", "authHandler", "proxyHandler"};
            for (String handlerName : handlersToRemove) {
                if (pipeline.get(handlerName) != null) {
                    pipeline.remove(handlerName);
                    logger.debug("清理归还连接的处理器: {} - {}", handlerName, hostKey);
                }
            }

            // 清理Channel的用户属性，避免状态污染
            channel.attr(io.netty.util.AttributeKey.valueOf("sessionId")).set(null);
            channel.attr(io.netty.util.AttributeKey.valueOf("clientChannel")).set(null);

            // 验证连接在清理后仍然可用（写缓冲可能短暂不可写，放宽为只检查active）
            if (!channel.isActive()) {
                logger.warn("连接在清理后变为不可用: {}", hostKey);
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.warn("清理归还连接时发生异常: {}，连接ID: {}", hostKey, channel.id(), e);
            return false;
        }
    }

    /**
     * 清理过期连接
     */
    private void cleanupExpiredConnections() {
        try {
            // 清理传统连接池
            int totalCleaned = 0;
            int idleTimeoutCleaned = 0;
            int lifetimeTimeoutCleaned = 0;
            
            for (Map.Entry<String, Queue<PooledConnection>> entry : connectionPool.entrySet()) {
                String hostKey = entry.getKey();
                Queue<PooledConnection> connections = entry.getValue();

                Iterator<PooledConnection> iterator = connections.iterator();
                int cleanedForHost = 0;

                while (iterator.hasNext()) {
                    PooledConnection conn = iterator.next();
                    Channel channel = conn.getChannelWithoutUpdate();

                    // 检查连接是否过期或无效
                    if (isConnectionExpired(conn) || !isConnectionUsable(channel)) {
                        iterator.remove();
                        if (channel != null && channel.isActive()) {
                            safeCloseChannel(channel);
                        }
                        cleanedForHost++;
                        totalCleaned++;
                        
                        // 统计清理原因
                        long currentTime = System.currentTimeMillis();
                        long idleTime = currentTime - conn.getLastUsedTime();
                        long totalLifetime = currentTime - conn.getCreationTime();
                        
                        if (idleTime > ConnectionPoolConfig.CONNECTION_IDLE_TIMEOUT) {
                            idleTimeoutCleaned++;
                        } else if (totalLifetime > ConnectionPoolConfig.CONNECTION_MAX_LIFETIME) {
                            lifetimeTimeoutCleaned++;
                        }
                    }
                }

                if (connections.isEmpty()) {
                    connectionPool.remove(hostKey);
                }

                if (cleanedForHost > 0) {
                    logger.debug("传统连接池清理过期连接: {} - {} 个连接", hostKey, cleanedForHost);
                }
            }

            if (totalCleaned > 0) {
                logger.info("传统连接池清理完成，共清理 {} 个过期连接 (空闲超时: {}, 存活时间超时: {})", 
                           totalCleaned, idleTimeoutCleaned, lifetimeTimeoutCleaned);
            }
        } catch (Exception e) {
            logger.error("清理过期连接时发生异常", e);
        }
    }

    /**
     * 关闭连接池
     */
    public synchronized void shutdown() {
        started = false;

        // 关闭定时清理任务
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭所有连接
        for (Queue<PooledConnection> connections : connectionPool.values()) {
            for (PooledConnection conn : connections) {
                Channel channel = conn.getChannelWithoutUpdate();
                if (channel != null && channel.isActive()) {
                    safeCloseChannel(channel);
                }
            }
        }

        // 清空连接池
        connectionPool.clear();

        logger.info("连接池已关闭");
    }


    /**
     * 获取连接池状态信息
     */
    public String getPoolStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("连接池状态:\n");

        // 显示传统连接池统计
        long legacyHits = legacyHitCount.get();
        long legacyMisses = legacyMissCount.get();
        long legacyTotal = legacyHits + legacyMisses;
        double legacyHitRate = legacyTotal > 0 ? (double) legacyHits / legacyTotal * 100.0 : 0.0;

        stats.append(String.format("  传统连接池: 命中=%d, 未命中=%d, 命中率=%.2f%%\n",
                legacyHits, legacyMisses, legacyHitRate));

        // 显示连接池配置信息
        stats.append(String.format("  配置: 最大连接数/主机=%d, 空闲超时=%dms, 存活时间=%dms, 清理间隔=%dms\n",
                ConnectionPoolConfig.MAX_CONNECTIONS_PER_HOST,
                ConnectionPoolConfig.CONNECTION_IDLE_TIMEOUT,
                ConnectionPoolConfig.CONNECTION_MAX_LIFETIME,
                ConnectionPoolConfig.POOL_CLEANUP_INTERVAL));

        for (Map.Entry<String, Queue<PooledConnection>> entry : connectionPool.entrySet()) {
            String hostKey = entry.getKey();
            Queue<PooledConnection> connections = entry.getValue();
            int pooledCount = connections.size();
            
            // 计算连接的平均存活时间和空闲时间
            long totalLifetime = 0;
            long totalIdleTime = 0;
            int validConnections = 0;
            
            for (PooledConnection conn : connections) {
                if (conn.getChannelWithoutUpdate() != null && conn.getChannelWithoutUpdate().isActive()) {
                    totalLifetime += conn.getLifetime();
                    totalIdleTime += conn.getIdleTime();
                    validConnections++;
                }
            }
            
            long avgLifetime = validConnections > 0 ? totalLifetime / validConnections : 0;
            long avgIdleTime = validConnections > 0 ? totalIdleTime / validConnections : 0;
            
            stats.append(String.format("    %s: 池中=%d, 平均存活时间=%dms, 平均空闲时间=%dms\n", 
                    hostKey, pooledCount, avgLifetime, avgIdleTime));
        }

        return stats.toString();
    }

    /**
     * 从连接池中移除失败的连接
     * 
     * @param hostKey 主机标识
     * @param failedChannel 失败的连接
     */
    public void removeFailedConnection(String hostKey, Channel failedChannel) {
        if (!ConnectionPoolConfig.ENABLE_CONNECTION_POOL || hostKey == null || failedChannel == null) {
            return;
        }

        Queue<PooledConnection> connections = connectionPool.get(hostKey);
        if (connections == null || connections.isEmpty()) {
            return;
        }

        // 移除失败的连接
        boolean removed = connections.removeIf(pooledConn -> {
            Channel channel = pooledConn.getChannelWithoutUpdate();
            return channel != null && channel.equals(failedChannel);
        });

        if (removed) {
            logger.debug("从连接池移除失败连接: {}, 连接ID: {}, 剩余连接数: {}", 
                    hostKey, failedChannel.id(), connections.size());
            
            // 如果队列为空，移除整个条目
            if (connections.isEmpty()) {
                connectionPool.remove(hostKey);
            }
        }

        // 确保失败的连接被关闭
        safeCloseChannel(failedChannel);
    }

    /**
     * 获取连接池性能统计
     */
    public OptimizedConnectionPool.PoolStats getPerformanceStats() {
        // 返回传统连接池统计
        int totalConnections = connectionPool.values().stream()
                .mapToInt(Queue::size)
                .sum();

        return new OptimizedConnectionPool.PoolStats(
                totalConnections, totalConnections, 0,
                legacyHitCount.get(), legacyMissCount.get()
        );
    }


    /**
     * 连接池中的连接包装类
     */
    private static class PooledConnection {
        private final Channel channel;
        private final long creationTime;
        private long lastUsedTime;

        public PooledConnection(Channel channel) {
            this.channel = channel;
            this.creationTime = System.currentTimeMillis();
            this.lastUsedTime = this.creationTime;
        }

        /**
         * 获取通道并更新最后使用时间
         *
         * @return 通道对象
         */
        public Channel getChannel() {
            this.lastUsedTime = System.currentTimeMillis();
            return channel;
        }

        /**
         * 获取通道但不更新最后使用时间
         *
         * @return 通道对象
         */
        public Channel getChannelWithoutUpdate() {
            return channel;
        }

        /**
         * 获取最后使用时间
         *
         * @return 最后使用时间戳
         */
        public long getLastUsedTime() {
            return lastUsedTime;
        }

        /**
         * 获取创建时间
         *
         * @return 创建时间戳
         */
        public long getCreationTime() {
            return creationTime;
        }

        /**
         * 检查连接是否空闲超时
         *
         * @return 如果超时返回true
         */
        public boolean isIdleTimeout() {
            long currentTime = System.currentTimeMillis();
            long idleTime = currentTime - lastUsedTime;
            return idleTime > ConnectionPoolConfig.CONNECTION_IDLE_TIMEOUT;
        }
        
        /**
         * 检查连接是否存活时间超时
         *
         * @return 如果超时返回true
         */
        public boolean isLifetimeTimeout() {
            long currentTime = System.currentTimeMillis();
            long totalLifetime = currentTime - creationTime;
            return totalLifetime > ConnectionPoolConfig.CONNECTION_MAX_LIFETIME;
        }
        
        /**
         * 获取连接存活时间（毫秒）
         *
         * @return 连接存活时间
         */
        public long getLifetime() {
            return System.currentTimeMillis() - creationTime;
        }
        
        /**
         * 获取连接空闲时间（毫秒）
         *
         * @return 连接空闲时间
         */
        public long getIdleTime() {
            return System.currentTimeMillis() - lastUsedTime;
        }
    }
}