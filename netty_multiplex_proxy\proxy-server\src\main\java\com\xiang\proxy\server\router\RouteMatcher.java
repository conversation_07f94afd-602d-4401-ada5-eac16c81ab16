package com.xiang.proxy.server.router;

import java.util.regex.Pattern;

/**
 * 路由匹配器
 */
public class RouteMatcher {
    private String type;
    private String operator;
    private String value;
    private boolean caseSensitive;
    private Pattern compiledPattern; // 缓存编译后的正则表达式

    public RouteMatcher() {
        this.caseSensitive = true;
    }

    public RouteMatcher(String type, String operator, String value) {
        this();
        this.type = type;
        this.operator = operator;
        this.value = value;
        compilePatternIfNeeded();
    }

    // Getters and Setters
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
        compilePatternIfNeeded();
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
        compilePatternIfNeeded();
    }

    public boolean isCaseSensitive() {
        return caseSensitive;
    }

    public void setCaseSensitive(boolean caseSensitive) {
        this.caseSensitive = caseSensitive;
        compilePatternIfNeeded();
    }

    public Pattern getCompiledPattern() {
        return compiledPattern;
    }

    // 编译正则表达式模式
    private void compilePatternIfNeeded() {
        if (Operator.REGEX.equals(operator) && value != null) {
            try {
                int flags = caseSensitive ? 0 : Pattern.CASE_INSENSITIVE;
                this.compiledPattern = Pattern.compile(value, flags);
            } catch (Exception e) {
                // 正则表达式编译失败，设置为null
                this.compiledPattern = null;
            }
        } else {
            this.compiledPattern = null;
        }
    }

    @Override
    public String toString() {
        return String.format("RouteMatcher{type=%s, operator=%s, value=%s, caseSensitive=%s}",
                type, operator, value, caseSensitive);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        RouteMatcher that = (RouteMatcher) obj;
        return caseSensitive == that.caseSensitive &&
                (type != null ? type.equals(that.type) : that.type == null) &&
                (operator != null ? operator.equals(that.operator) : that.operator == null) &&
                (value != null ? value.equals(that.value) : that.value == null);
    }

    @Override
    public int hashCode() {
        int result = type != null ? type.hashCode() : 0;
        result = 31 * result + (operator != null ? operator.hashCode() : 0);
        result = 31 * result + (value != null ? value.hashCode() : 0);
        result = 31 * result + (caseSensitive ? 1 : 0);
        return result;
    }

    // 匹配类型常量
    public static final class Type {
        public static final String HOST = "host";
        public static final String PORT = "port";
        public static final String PROTOCOL = "protocol";
        public static final String CLIENT_IP = "client_ip";
        public static final String CLIENT_ID = "client_id";
        public static final String SESSION_ID = "session_id";
        public static final String TARGET = "target"; // host:port
        public static final String TIME = "time"; // 时间范围
        public static final String ATTRIBUTE = "attribute"; // 自定义属性
    }

    // 操作符常量
    public static final class Operator {
        public static final String EQUALS = "equals";
        public static final String NOT_EQUALS = "not_equals";
        public static final String CONTAINS = "contains";
        public static final String NOT_CONTAINS = "not_contains";
        public static final String STARTS_WITH = "starts_with";
        public static final String ENDS_WITH = "ends_with";
        public static final String REGEX = "regex";
        public static final String NOT_REGEX = "not_regex";
        public static final String RANGE = "range"; // 数值范围
        public static final String IN = "in"; // 在列表中
        public static final String NOT_IN = "not_in"; // 不在列表中
        public static final String GREATER_THAN = "gt";
        public static final String GREATER_EQUAL = "gte";
        public static final String LESS_THAN = "lt";
        public static final String LESS_EQUAL = "lte";
    }
}