package com.proxy.client.queue;

import com.proxy.client.protocol.MultiplexProtocol;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PacketQueue测试类
 * 测试多线程随机分发功能
 */
public class PacketQueueTest {
    private static final Logger logger = LoggerFactory.getLogger(PacketQueueTest.class);
    
    private PacketQueue packetQueue;
    private TestPacketSender packetSender;
    
    @BeforeEach
    void setUp() {
        // 创建测试用的队列配置
        PacketQueueConfig config = new PacketQueueConfig(4)
            .setQueueCapacity(1000)
            .setBatchSize(50)
            .setFlushIntervalMs(10)
            .setRetryAttempts(2)
            .setRetryDelayMs(100)
            .setDistributionStrategy(PacketQueueConfig.DistributionStrategy.ROUND_ROBIN);
        
        packetQueue = new PacketQueue(config);
        packetSender = new TestPacketSender();
        packetQueue.setPacketSender(packetSender);
        packetQueue.start();
    }
    
    @AfterEach
    void tearDown() {
        if (packetQueue != null) {
            packetQueue.stop();
        }
    }
    
    @Test
    void testRoundRobinDistribution() throws InterruptedException {
        logger.info("测试轮询分发策略");
        
        // 发送100个数据包
        int packetCount = 100;
        for (int i = 0; i < packetCount; i++) {
            MultiplexProtocol.Packet packet = createTestPacket(i);
            assertTrue(packetQueue.enqueue(packet));
        }
        
        // 等待处理完成
        Thread.sleep(2000);
        
        // 验证统计信息
        PacketQueue.QueueStats stats = packetQueue.getStats();
        logger.info("轮询分发统计: {}", stats);
        
        assertEquals(packetCount, stats.getEnqueuedCount());
        assertEquals(packetCount, packetSender.getProcessedCount());
        assertEquals(0, stats.getDroppedCount());
    }
    
    @Test
    void testTrueRandomDistribution() throws InterruptedException {
        logger.info("测试真随机分发策略");
        
        // 重新创建队列使用真随机策略
        packetQueue.stop();
        
        PacketQueueConfig config = new PacketQueueConfig(4)
            .setQueueCapacity(1000)
            .setBatchSize(50)
            .setFlushIntervalMs(10)
            .setRetryAttempts(2)
            .setRetryDelayMs(100)
            .setDistributionStrategy(PacketQueueConfig.DistributionStrategy.TRUE_RANDOM);
        
        packetQueue = new PacketQueue(config);
        packetSender = new TestPacketSender();
        packetQueue.setPacketSender(packetSender);
        packetQueue.start();
        
        // 发送100个数据包
        int packetCount = 100;
        for (int i = 0; i < packetCount; i++) {
            MultiplexProtocol.Packet packet = createTestPacket(i);
            assertTrue(packetQueue.enqueue(packet));
        }
        
        // 等待处理完成
        Thread.sleep(2000);
        
        // 验证统计信息
        PacketQueue.QueueStats stats = packetQueue.getStats();
        logger.info("真随机分发统计: {}", stats);
        
        assertEquals(packetCount, stats.getEnqueuedCount());
        assertEquals(packetCount, packetSender.getProcessedCount());
        assertEquals(0, stats.getDroppedCount());
    }
    
    @Test
    void testConcurrentEnqueue() throws InterruptedException {
        logger.info("测试并发入队");
        
        int threadCount = 10;
        int packetsPerThread = 50;
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        // 启动多个线程并发入队
        for (int t = 0; t < threadCount; t++) {
            final int threadId = t;
            new Thread(() -> {
                try {
                    for (int i = 0; i < packetsPerThread; i++) {
                        MultiplexProtocol.Packet packet = createTestPacket(threadId * packetsPerThread + i);
                        packetQueue.enqueue(packet);
                    }
                } finally {
                    latch.countDown();
                }
            }).start();
        }
        
        // 等待所有线程完成
        assertTrue(latch.await(10, TimeUnit.SECONDS));
        
        // 等待队列处理完成
        Thread.sleep(3000);
        
        // 验证统计信息
        PacketQueue.QueueStats stats = packetQueue.getStats();
        logger.info("并发入队统计: {}", stats);
        
        int expectedCount = threadCount * packetsPerThread;
        assertEquals(expectedCount, stats.getEnqueuedCount());
        assertEquals(expectedCount, packetSender.getProcessedCount());
    }
    
    @Test
    void testWorkerQueueStats() throws InterruptedException {
        logger.info("测试工作队列统计");
        
        // 发送一些数据包
        for (int i = 0; i < 20; i++) {
            MultiplexProtocol.Packet packet = createTestPacket(i);
            packetQueue.enqueue(packet);
        }
        
        // 等待处理完成
        Thread.sleep(1000);
        
        // 检查每个工作队列的统计信息
        for (int i = 0; i < 4; i++) {
            PacketQueue.WorkerQueue.QueueStats workerStats = packetQueue.getWorkerStats(i);
            assertNotNull(workerStats);
            logger.info("工作队列{}统计: {}", i, workerStats);
        }
    }
    
    /**
     * 创建测试用的数据包
     */
    private MultiplexProtocol.Packet createTestPacket(int id) {
        MultiplexProtocol.DataPacket.Builder dataBuilder = 
            MultiplexProtocol.DataPacket.newBuilder()
                .setConnectionId(id)
                .setData(com.google.protobuf.ByteString.copyFromUtf8("test data " + id));
        
        return MultiplexProtocol.Packet.newBuilder()
            .setData(dataBuilder.build())
            .build();
    }
    
    /**
     * 测试用的数据包发送器
     */
    private static class TestPacketSender implements PacketQueue.PacketSender {
        private final AtomicInteger processedCount = new AtomicInteger(0);
        
        @Override
        public boolean sendPacket(MultiplexProtocol.Packet packet) {
            try {
                // 模拟处理延迟
                Thread.sleep(1);
                processedCount.incrementAndGet();
                return true;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        public int getProcessedCount() {
            return processedCount.get();
        }
    }
}