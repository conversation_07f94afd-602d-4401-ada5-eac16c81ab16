package com.xiang.proxy.server.util;

import com.xiang.proxy.server.metrics.AdvancedMetrics;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @TODO 分配和释放buffer没使用。
 * 内存优化工具类
 * 提供内存管理、ByteBuf优化和内存泄漏检测功能
 */
public class MemoryOptimizer {
    private static final Logger logger = LoggerFactory.getLogger(MemoryOptimizer.class);
    
    private static final MemoryOptimizer INSTANCE = new MemoryOptimizer();
    
    // 内存统计
    private final AtomicLong totalAllocatedBytes = new AtomicLong(0);
    private final AtomicLong totalReleasedBytes = new AtomicLong(0);
    private final AtomicLong activeBuffers = new AtomicLong(0);
    
    // 内存阈值配置
    private static final double HIGH_MEMORY_THRESHOLD = 0.8; // 80%
    private static final double CRITICAL_MEMORY_THRESHOLD = 0.9; // 90%
    
    // 缓冲区大小配置
    private static final int MIN_BUFFER_SIZE = 256;
    private static final int MAX_BUFFER_SIZE = 64 * 1024; // 64KB
    private static final int DEFAULT_BUFFER_SIZE = 4 * 1024; // 4KB
    
    private final MemoryMXBean memoryMXBean;
    
    private MemoryOptimizer() {
        this.memoryMXBean = ManagementFactory.getMemoryMXBean();
    }
    
    public static MemoryOptimizer getInstance() {
        return INSTANCE;
    }
    
    /**
     * 智能分配ByteBuf
     * 根据当前内存使用情况和数据大小选择最优的分配策略
     */
    public ByteBuf allocateBuffer(ByteBufAllocator allocator, int size) {
        // 调整缓冲区大小到合理范围
        int adjustedSize = adjustBufferSize(size);
        
        // 检查内存压力
        double memoryUsage = getMemoryUsagePercentage();
        
        ByteBuf buffer;
        if (memoryUsage > CRITICAL_MEMORY_THRESHOLD) {
            // 内存压力极高，使用堆外内存
            logger.warn("内存使用率过高 ({}%)，使用堆外内存分配", String.format("%.2f", memoryUsage * 100));
            buffer = allocator.directBuffer(adjustedSize);
        } else if (memoryUsage > HIGH_MEMORY_THRESHOLD) {
            // 内存压力较高，使用较小的缓冲区
            logger.debug("内存使用率较高 ({}%)，使用优化分配策略", String.format("%.2f", memoryUsage * 100));
            buffer = allocator.buffer(Math.min(adjustedSize, DEFAULT_BUFFER_SIZE));
        } else {
            // 正常分配
            buffer = allocator.buffer(adjustedSize);
        }
        
        // 统计
        long allocatedBytes = buffer.capacity();
        totalAllocatedBytes.addAndGet(allocatedBytes);
        activeBuffers.incrementAndGet();
        
        // Record memory allocation in AdvancedMetrics
        AdvancedMetrics.getInstance().recordMemoryAllocation(allocatedBytes);
        
        return buffer;
    }
    
    /**
     * 安全释放ByteBuf
     */
    public void safeRelease(ByteBuf buffer) {
        if (buffer != null && buffer.refCnt() > 0) {
            try {
                int capacity = buffer.capacity();
                ReferenceCountUtil.release(buffer);
                
                // 统计
                totalReleasedBytes.addAndGet(capacity);
                activeBuffers.decrementAndGet();
                
                // Record memory release in AdvancedMetrics
                AdvancedMetrics.getInstance().recordMemoryRelease(capacity);
                
            } catch (Exception e) {
                logger.warn("释放ByteBuf时发生异常", e);
            }
        }
    }
    
    /**
     * 优化缓冲区大小
     */
        public ByteBuf optimizeBuffer(ByteBuf buffer) {
        if (buffer == null || !buffer.isReadable()) {
            return buffer;
        }
        
        int readableBytes = buffer.readableBytes();
        int capacity = buffer.capacity();
        
        // 如果使用率过低且容量较大，考虑压缩
        if (readableBytes < capacity / 4 && capacity > MAX_BUFFER_SIZE) {
            ByteBuf optimized = buffer.alloc().buffer(Math.max(readableBytes * 2, MIN_BUFFER_SIZE));
            optimized.writeBytes(buffer);
            safeRelease(buffer);
            
            logger.debug("缓冲区优化: {}B -> {}B", capacity, optimized.capacity());
            return optimized;
        }
        
        // 正常压缩读取位置
        buffer.discardReadBytes();
        return buffer;
    }
    
    /**
     * 调整缓冲区大小到合理范围
     */
    private int adjustBufferSize(int requestedSize) {
        if (requestedSize <= 0) {
            return DEFAULT_BUFFER_SIZE;
        }
        
        // 限制在合理范围内
        int adjustedSize = Math.max(requestedSize, MIN_BUFFER_SIZE);
        adjustedSize = Math.min(adjustedSize, MAX_BUFFER_SIZE);
        
        // 向上取整到2的幂次，提高内存分配效率
        return nextPowerOfTwo(adjustedSize);
    }
    
    /**
     * 计算下一个2的幂次
     */
    private int nextPowerOfTwo(int value) {
        if (value <= 0) {
            return 1;
        }
        
        // 如果已经是2的幂次，直接返回
        if ((value & (value - 1)) == 0) {
            return value;
        }
        
        // 计算下一个2的幂次
        int powerOfTwo = 1;
        while (powerOfTwo < value) {
            powerOfTwo <<= 1;
        }
        
        return powerOfTwo;
    }
    
    /**
     * 获取当前内存使用率
     */
    public double getMemoryUsagePercentage() {
        MemoryUsage heapMemory = memoryMXBean.getHeapMemoryUsage();
        return (double) heapMemory.getUsed() / heapMemory.getMax();
    }
    
    /**
     * 检查是否需要进行内存清理
     */
    public boolean shouldPerformGC() {
        double memoryUsage = getMemoryUsagePercentage();
        return memoryUsage > HIGH_MEMORY_THRESHOLD;
    }
    
    /**
     * 建议进行垃圾回收
     */
    public void suggestGC() {
        if (shouldPerformGC()) {
            logger.info("内存使用率过高，建议进行垃圾回收");
            System.gc();
        }
    }
    
    /**
     * 获取内存统计信息
     */
    public MemoryStats getMemoryStats() {
        MemoryUsage heapMemory = memoryMXBean.getHeapMemoryUsage();
        MemoryUsage nonHeapMemory = memoryMXBean.getNonHeapMemoryUsage();
        
        return new MemoryStats(
            heapMemory.getUsed(),
            heapMemory.getMax(),
            nonHeapMemory.getUsed(),
            nonHeapMemory.getMax(),
            totalAllocatedBytes.get(),
            totalReleasedBytes.get(),
            activeBuffers.get()
        );
    }
    
    /**
     * 记录内存统计信息
     */
    public void logMemoryStats() {
        MemoryStats stats = getMemoryStats();
        logger.info("=== 内存使用统计 ===");
        logger.info("堆内存: 已用={}MB, 最大={}MB, 使用率={}%",
                   String.format("%.2f", stats.heapUsed / 1024.0 / 1024.0),
                   String.format("%.2f", stats.heapMax / 1024.0 / 1024.0),
                   String.format("%.2f", stats.getHeapUsagePercentage()));
        logger.info("非堆内存: 已用={}MB, 最大={}MB",
                   String.format("%.2f", stats.nonHeapUsed / 1024.0 / 1024.0),
                   String.format("%.2f", stats.nonHeapMax / 1024.0 / 1024.0));
//        logger.info("ByteBuf统计: 已分配={}MB, 已释放={}MB, 活跃缓冲区={}",
//                   String.format("%.2f", stats.totalAllocated / 1024.0 / 1024.0),
//                   String.format("%.2f", stats.totalReleased / 1024.0 / 1024.0),
//                   stats.activeBuffers);
        
        if (stats.getHeapUsagePercentage() > HIGH_MEMORY_THRESHOLD * 100) {
            logger.warn("内存使用率过高，建议检查内存泄漏");
        }
    }
    
    /**
     * 内存统计数据类
     */
    public static class MemoryStats {
        public final long heapUsed;
        public final long heapMax;
        public final long nonHeapUsed;
        public final long nonHeapMax;
        public final long totalAllocated;
        public final long totalReleased;
        public final long activeBuffers;
        
        public MemoryStats(long heapUsed, long heapMax, long nonHeapUsed, long nonHeapMax,
                          long totalAllocated, long totalReleased, long activeBuffers) {
            this.heapUsed = heapUsed;
            this.heapMax = heapMax;
            this.nonHeapUsed = nonHeapUsed;
            this.nonHeapMax = nonHeapMax;
            this.totalAllocated = totalAllocated;
            this.totalReleased = totalReleased;
            this.activeBuffers = activeBuffers;
        }
        
        public double getHeapUsagePercentage() {
            return (double) heapUsed / heapMax * 100.0;
        }
        
        public long getNetAllocated() {
            return totalAllocated - totalReleased;
        }
        
        public boolean hasMemoryLeak() {
            return activeBuffers > 1000 || getNetAllocated() > 100 * 1024 * 1024; // 100MB
        }
    }
}