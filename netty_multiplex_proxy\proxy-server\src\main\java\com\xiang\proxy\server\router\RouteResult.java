package com.xiang.proxy.server.router;

import java.util.HashMap;
import java.util.Map;

/**
 * 路由结果
 */
public class RouteResult {
    private final boolean success;
    private final String outboundId;
    private final String ruleId;
    private final String ruleName;
    private final String reason;
    private final Map<String, Object> attributes;
    private final long routeTime;

    private RouteResult(Builder builder) {
        this.success = builder.success;
        this.outboundId = builder.outboundId;
        this.ruleId = builder.ruleId;
        this.ruleName = builder.ruleName;
        this.reason = builder.reason;
        this.attributes = new HashMap<>(builder.attributes);
        this.routeTime = builder.routeTime > 0 ? builder.routeTime : System.currentTimeMillis();
    }

    // Getters
    public boolean isSuccess() {
        return success;
    }

    public String getOutboundId() {
        return outboundId;
    }

    public String getRuleId() {
        return ruleId;
    }

    public String getRuleName() {
        return ruleName;
    }

    public String getReason() {
        return reason;
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public long getRouteTime() {
        return routeTime;
    }

    // 便捷方法
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

    public <T> T getAttribute(String key, T defaultValue) {
        T value = getAttribute(key);
        return value != null ? value : defaultValue;
    }

    public boolean hasAttribute(String key) {
        return attributes.containsKey(key);
    }

    @Override
    public String toString() {
        return String.format("RouteResult{success=%s, outbound=%s, rule=%s(%s), reason=%s}",
                success, outboundId, ruleName, ruleId, reason);
    }

    // Builder模式
    public static class Builder {
        private boolean success;
        private String outboundId;
        private String ruleId;
        private String ruleName;
        private String reason;
        private Map<String, Object> attributes = new HashMap<>();
        private long routeTime;

        public Builder success(boolean success) {
            this.success = success;
            return this;
        }

        public Builder outboundId(String outboundId) {
            this.outboundId = outboundId;
            return this;
        }

        public Builder ruleId(String ruleId) {
            this.ruleId = ruleId;
            return this;
        }

        public Builder ruleName(String ruleName) {
            this.ruleName = ruleName;
            return this;
        }

        public Builder reason(String reason) {
            this.reason = reason;
            return this;
        }

        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }

        public Builder attributes(Map<String, Object> attributes) {
            this.attributes.putAll(attributes);
            return this;
        }

        public Builder routeTime(long routeTime) {
            this.routeTime = routeTime;
            return this;
        }

        public RouteResult build() {
            return new RouteResult(this);
        }
    }

    // 静态工厂方法
    public static Builder builder() {
        return new Builder();
    }

    public static RouteResult success(String outboundId, String ruleId, String ruleName) {
        return builder()
                .success(true)
                .outboundId(outboundId)
                .ruleId(ruleId)
                .ruleName(ruleName)
                .reason("Route matched")
                .build();
    }

    public static RouteResult failure(String reason) {
        return builder()
                .success(false)
                .reason(reason)
                .build();
    }

    public static RouteResult noMatch() {
        return failure("No matching route rule found");
    }

    public static RouteResult error(String reason) {
        return failure("Route error: " + reason);
    }
}