# Connection Lifecycle Management

## 🔄 Connection Lifecycle Overview

Understanding the proper connection lifecycle is crucial for effective error handling and connection pool management.

## 📊 Connection States and Transitions

### Connection States
1. **POOL** - Connection is available in the connection pool
2. **ACTIVE** - Connection is being used for data transmission
3. **INACTIVE** - Connection has failed or been marked for closure
4. **CLOSED** - Connection has been closed and resources released

### State Transitions
```
POOL → ACTIVE (getConnection)
ACTIVE → INACTIVE (sendData failure)
ACTIVE → POOL (closeConnection, if healthy)
INACTIVE → CLOSED (closeConnection)
```

## 🚫 Common Mistakes and Corrections

### ❌ Incorrect: Removing Active Connections from Pool

**Wrong Approach:**
```java
// This is WRONG - connection is not in pool when active
private void handleSendDataFailure(OutboundConnection connection, Throwable cause) {
    // Connection is already out of pool, this does nothing!
    ConnectionPool.getInstance().removeFailedConnection(poolKey, channel);
}
```

**Why it's wrong:**
- Connection was already removed from pool when acquired
- `removeFailedConnection` won't find the connection
- Unnecessary overhead and confusing logic

### ✅ Correct: Mark Connection as Inactive

**Right Approach:**
```java
private void handleSendDataFailure(OutboundConnection connection, Throwable cause) {
    // Mark connection as failed - it won't be returned to pool
    connection.markInactive();
    
    // Log appropriately based on error type
    if (isConnectionResetError(cause)) {
        logger.warn("连接被远程主机重置: {}", connection.getConnectionId());
    }
    
    // Connection will be closed (not returned to pool) in closeConnection()
}
```

## 🔧 Proper Connection Management

### 1. Connection Acquisition
```java
// Connection is removed from pool
Channel channel = ConnectionPool.getInstance().getConnection(poolKey);
// Connection state: POOL → ACTIVE
```

### 2. Data Transmission
```java
// If transmission fails:
connection.markInactive(); // ACTIVE → INACTIVE
// Connection will not be returned to pool
```

### 3. Connection Closure
```java
public CompletableFuture<Void> closeConnection(OutboundConnection connection) {
    boolean wasActive = connection.isActive();
    connection.markInactive();
    
    if (wasActive && connectionPoolEnabled) {
        // Return healthy connection to pool
        connectionPool.returnConnection(poolKey, channel);
    } else {
        // Close failed/inactive connection directly
        channel.close();
    }
}
```

## 📈 Benefits of Correct Lifecycle Management

### 1. Pool Health
- Only healthy connections are returned to pool
- Failed connections are properly disposed
- Pool maintains high quality connections

### 2. Resource Efficiency
- No unnecessary pool operations on active connections
- Clear separation of concerns
- Reduced overhead

### 3. Error Handling
- Failed connections don't pollute the pool
- Clear error classification and logging
- Proper resource cleanup

## 🎯 Key Principles

1. **Connection State Tracking**: Always track whether connection is active/inactive
2. **Pool Operations**: Only operate on pool when connection is actually in pool
3. **Failure Handling**: Mark failed connections as inactive, don't return to pool
4. **Resource Cleanup**: Ensure all connections are properly closed

## 🔍 Debugging Connection Issues

### Check Connection State
```java
logger.debug("Connection state: active={}, poolKey={}, connectionId={}", 
    connection.isActive(), poolKey, connection.getConnectionId());
```

### Monitor Pool Operations
```bash
# Check pool return operations
grep "连接已归还到连接池" logs/proxy-server.log

# Check direct closures
grep "连接状态异常，直接关闭" logs/proxy-server.log
```

### Validate Pool Health
```java
String poolStats = ConnectionPool.getInstance().getPoolStats();
logger.info("Pool health: {}", poolStats);
```

This proper lifecycle management ensures that connection errors are handled correctly without unnecessary pool operations or resource leaks.