package com.xiang.proxy.server.inbound;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Inbound处理器统计信息
 */
public class InboundHandlerStatistics {
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final AtomicLong totalBytesProcessed = new AtomicLong(0);
    private final long startTime;

    public InboundHandlerStatistics() {
        this.startTime = System.currentTimeMillis();
    }

    // 统计方法
    public void incrementTotalRequests() {
        totalRequests.incrementAndGet();
    }

    public void incrementSuccessfulRequests() {
        successfulRequests.incrementAndGet();
    }

    public void incrementFailedRequests() {
        failedRequests.incrementAndGet();
    }

    public void addBytesProcessed(long bytes) {
        totalBytesProcessed.addAndGet(bytes);
    }

    // Getters
    public long getTotalRequests() {
        return totalRequests.get();
    }

    public long getSuccessfulRequests() {
        return successfulRequests.get();
    }

    public long getFailedRequests() {
        return failedRequests.get();
    }

    public long getTotalBytesProcessed() {
        return totalBytesProcessed.get();
    }

    public long getStartTime() {
        return startTime;
    }

    public long getUptime() {
        return System.currentTimeMillis() - startTime;
    }

    // 计算统计指标
    public double getSuccessRate() {
        long total = getTotalRequests();
        return total > 0 ? (double) getSuccessfulRequests() / total : 0.0;
    }

    public double getFailureRate() {
        long total = getTotalRequests();
        return total > 0 ? (double) getFailedRequests() / total : 0.0;
    }

    public double getRequestsPerSecond() {
        long uptimeSeconds = getUptime() / 1000;
        return uptimeSeconds > 0 ? (double) getTotalRequests() / uptimeSeconds : 0.0;
    }

    public double getBytesPerSecond() {
        long uptimeSeconds = getUptime() / 1000;
        return uptimeSeconds > 0 ? (double) getTotalBytesProcessed() / uptimeSeconds : 0.0;
    }

    // 重置统计
    public void reset() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        totalBytesProcessed.set(0);
    }

    @Override
    public String toString() {
        return String.format("InboundHandlerStatistics{total=%d, success=%d, failed=%d, " +
                "bytes=%d, uptime=%dms, successRate=%.2f%%, rps=%.2f}",
                getTotalRequests(), getSuccessfulRequests(), getFailedRequests(),
                getTotalBytesProcessed(), getUptime(), getSuccessRate() * 100, getRequestsPerSecond());
    }
}