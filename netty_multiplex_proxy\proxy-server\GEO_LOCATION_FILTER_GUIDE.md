# 地理位置过滤功能指南

## 🎯 功能概述

地理位置过滤功能是proxy-server的一个重要安全特性，旨在基于地理位置和域名特征过滤访问请求，有效阻止非中国地区的不良网站访问。

## 🔧 核心功能

### 1. IP地理位置判断
- **中国IP识别**：基于最新APNIC数据，准确识别中国大陆IP地址
- **自动更新**：支持从在线数据源自动更新IP段数据
- **高性能缓存**：智能缓存IP判断结果，提高响应速度
- **IPv4支持**：完整支持IPv4地址判断（IPv6待扩展）

### 2. 恶意域名过滤
- **黑名单机制**：维护恶意域名黑名单，精确匹配和模式匹配
- **关键词检测**：检测域名中的可疑关键词（成人、赌博、恶意软件等）
- **模式匹配**：支持正则表达式模式匹配，灵活识别恶意域名
- **分类过滤**：按不同类型（成人内容、赌博、网络威胁等）分类过滤

### 3. 白名单机制
- **合法网站保护**：维护合法海外网站白名单，避免误杀
- **教育机构支持**：自动识别教育机构域名（.edu、.ac等）
- **知名企业支持**：包含Google、Microsoft、Amazon等知名企业域名
- **灵活配置**：支持精确匹配和模式匹配

### 4. 智能过滤策略
- **多层过滤**：白名单 -> 恶意域名 -> 关键词 -> 地理位置 -> 可疑特征
- **海外可疑检测**：针对海外IP的额外可疑特征检测
- **端口过滤**：特定端口的访问限制
- **域名特征分析**：基于域名长度、字符特征等进行分析

## 📊 监控统计

### 统计指标
- **总请求数**：所有过滤请求的总数
- **地理位置阻止数**：基于地理位置阻止的请求数
- **域名过滤阻止数**：恶意域名过滤阻止的请求数
- **关键词过滤阻止数**：关键词过滤阻止的请求数
- **海外可疑阻止数**：海外可疑网站阻止的请求数
- **允许访问数**：通过过滤检查的请求数

### 性能监控
- **缓存命中率**：DNS缓存和IP缓存的命中率
- **响应时间**：过滤检查的平均响应时间
- **内存使用**：缓存占用的内存大小
- **更新状态**：IP段数据的更新状态

## ⚙️ 配置说明

### 基本配置
```yaml
geo-location-filter:
  enable: true                          # 启用地理位置过滤
  block-overseas-suspicious: true       # 阻止海外可疑网站
  enable-domain-filter: true           # 启用域名过滤
  enable-keyword-filter: true          # 启用关键词过滤
  enable-whitelist: true               # 启用白名单
```

### 缓存配置
```yaml
geo-location-filter:
  dns-cache-timeout-minutes: 5         # DNS缓存超时（分钟）
  ip-cache-timeout-minutes: 60         # IP缓存超时（分钟）
  max-cache-size: 10000                # 最大缓存条目数
```

### 更新配置
```yaml
geo-location-filter:
  auto-update-ip-ranges: true          # 自动更新IP段
  update-interval-hours: 24            # 更新间隔（小时）
```

## 🚀 使用方法

### 1. 启用功能
在配置文件中设置 `geo-location-filter.enable: true`

### 2. 配置过滤策略
根据需要调整各项过滤开关：
- `enable-domain-filter`：恶意域名过滤
- `enable-keyword-filter`：关键词过滤
- `enable-whitelist`：白名单机制
- `block-overseas-suspicious`：海外可疑网站过滤

### 3. 优化性能
调整缓存配置以平衡性能和内存使用：
- 增大 `max-cache-size` 提高缓存命中率
- 调整超时时间平衡数据新鲜度和性能

### 4. 监控效果
通过日志和性能指标监控过滤效果：
```
地理位置过滤统计:
  总阻止数: 1234
  域名过滤: 567
  关键词过滤: 234
  海外可疑: 345
  其他地理位置: 88
```

## 🔍 过滤流程

```
请求 -> 地理位置过滤器
  |
  ├─ 1. 检查白名单 -> 允许访问
  |
  ├─ 2. 检查恶意域名 -> 阻止访问
  |
  ├─ 3. 检查恶意关键词 -> 阻止访问
  |
  ├─ 4. DNS解析获取IP
  |
  ├─ 5. 判断IP地理位置
  |    ├─ 中国IP -> 允许访问
  |    └─ 海外IP -> 检查可疑特征
  |         ├─ 可疑 -> 阻止访问
  |         └─ 正常 -> 允许访问
  |
  └─ 默认 -> 允许访问
```

## 📝 日志示例

```
2024-01-20 10:30:15 INFO  - 地理位置过滤器已初始化，恶意域名: 156, 恶意关键词: 45, 白名单域名: 89
2024-01-20 10:30:16 WARN  - 主机 malicious-site.com 被地理位置过滤器阻止，原因: 域名在恶意黑名单中
2024-01-20 10:30:17 DEBUG - 主机 google.com 通过地理位置过滤检查，原因: 域名在白名单中
2024-01-20 10:30:18 WARN  - 主机 suspicious-overseas.tk 被地理位置过滤器阻止，原因: 海外可疑网站
```

## 🛠️ 自定义配置

### 添加自定义恶意域名
修改 `GeoLocationFilter.java` 中的 `initializeMaliciousDomains()` 方法

### 添加自定义白名单
修改 `GeoLocationFilter.java` 中的 `initializeWhitelistDomains()` 方法

### 调整可疑特征检测
修改 `isSuspiciousOverseasSite()` 方法的检测逻辑

## 🔧 故障排除

### 常见问题

1. **过滤器未生效**
   - 检查配置文件中 `geo-location-filter.enable` 是否为 true
   - 查看日志确认过滤器是否正确初始化

2. **合法网站被误杀**
   - 将域名添加到白名单
   - 检查域名是否包含误判的关键词

3. **恶意网站未被拦截**
   - 检查域名是否在黑名单中
   - 考虑添加相关关键词或模式

4. **性能问题**
   - 增大缓存大小
   - 调整缓存超时时间
   - 监控内存使用情况

### 调试方法
- 启用DEBUG日志级别查看详细过滤过程
- 监控性能指标了解过滤效果
- 定期检查缓存命中率和内存使用

## 📈 性能优化建议

1. **合理设置缓存大小**：根据实际请求量调整 `max-cache-size`
2. **优化超时时间**：平衡数据新鲜度和性能
3. **定期清理缓存**：避免内存泄漏
4. **监控统计指标**：及时发现性能问题
5. **优化正则表达式**：避免复杂的正则表达式影响性能
