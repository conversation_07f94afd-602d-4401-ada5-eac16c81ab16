package com.xiang.proxy.server.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 线程池性能分析工具
 * 用于监控和分析线程池的性能表现
 */
public class ThreadPoolPerformanceAnalyzer {
    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolPerformanceAnalyzer.class);
    
    private static final ThreadPoolPerformanceAnalyzer INSTANCE = new ThreadPoolPerformanceAnalyzer();
    
    private final ThreadMXBean threadMXBean;
    private final AtomicLong lastCpuTime = new AtomicLong(0);
    private final AtomicLong lastMeasureTime = new AtomicLong(0);
    private volatile boolean monitoring = false;
    
    private ThreadPoolPerformanceAnalyzer() {
        this.threadMXBean = ManagementFactory.getThreadMXBean();
    }
    
    public static ThreadPoolPerformanceAnalyzer getInstance() {
        return INSTANCE;
    }
    
    /**
     * 开始性能监控
     */
    public void startMonitoring() {
        if (monitoring) {
            return;
        }
        
        monitoring = true;
        lastCpuTime.set(getTotalCpuTime());
        lastMeasureTime.set(System.nanoTime());
        
        logger.info("线程池性能监控已启动");
    }
    
    /**
     * 停止性能监控
     */
    public void stopMonitoring() {
        monitoring = false;
        logger.info("线程池性能监控已停止");
    }
    
    /**
     * 获取性能分析报告
     */
    public PerformanceReport getPerformanceReport() {
        if (!monitoring) {
            logger.warn("性能监控未启动，无法获取报告");
            return null;
        }
        
        long currentTime = System.nanoTime();
        long currentCpuTime = getTotalCpuTime();
        
        long timeDiff = currentTime - lastMeasureTime.get();
        long cpuTimeDiff = currentCpuTime - lastCpuTime.get();
        
        // 计算CPU使用率
        double cpuUsage = 0.0;
        if (timeDiff > 0) {
            cpuUsage = (double) cpuTimeDiff / timeDiff * 100.0;
        }
        
        // 获取线程信息
        int totalThreads = threadMXBean.getThreadCount();
        int daemonThreads = threadMXBean.getDaemonThreadCount();
        int activeThreads = totalThreads - daemonThreads;
        
        // 获取内存信息
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        // 更新测量基准
        lastCpuTime.set(currentCpuTime);
        lastMeasureTime.set(currentTime);
        
        return new PerformanceReport(
            cpuUsage,
            totalThreads,
            activeThreads,
            daemonThreads,
            usedMemory,
            totalMemory,
            maxMemory
        );
    }
    
    /**
     * 记录性能报告到日志
     */
    public void logPerformanceReport() {
        PerformanceReport report = getPerformanceReport();
        if (report != null) {
            logger.info("=== 线程池性能报告 ===");
            logger.info("CPU使用率: {}%", String.format("%.2f", report.cpuUsage));
            logger.info("线程统计: 总数={}, 活跃={}, 守护={}", 
                       report.totalThreads, report.activeThreads, report.daemonThreads);
            logger.info("内存使用: 已用={}MB, 总计={}MB, 最大={}MB, 使用率={}%", 
                       String.format("%.2f", report.usedMemory / 1024.0 / 1024.0),
                       String.format("%.2f", report.totalMemory / 1024.0 / 1024.0),
                       String.format("%.2f", report.maxMemory / 1024.0 / 1024.0),
                       String.format("%.2f", report.getMemoryUsagePercentage()));
        }
    }
    
    /**
     * 获取总CPU时间
     */
    private long getTotalCpuTime() {
        long totalCpuTime = 0;
        long[] threadIds = threadMXBean.getAllThreadIds();
        
        for (long threadId : threadIds) {
            long cpuTime = threadMXBean.getThreadCpuTime(threadId);
            if (cpuTime > 0) {
                totalCpuTime += cpuTime;
            }
        }
        
        return totalCpuTime;
    }
    
    /**
     * 性能报告数据类
     */
    public static class PerformanceReport {
        public final double cpuUsage;
        public final int totalThreads;
        public final int activeThreads;
        public final int daemonThreads;
        public final long usedMemory;
        public final long totalMemory;
        public final long maxMemory;
        
        public PerformanceReport(double cpuUsage, int totalThreads, int activeThreads, 
                               int daemonThreads, long usedMemory, long totalMemory, long maxMemory) {
            this.cpuUsage = cpuUsage;
            this.totalThreads = totalThreads;
            this.activeThreads = activeThreads;
            this.daemonThreads = daemonThreads;
            this.usedMemory = usedMemory;
            this.totalMemory = totalMemory;
            this.maxMemory = maxMemory;
        }
        
        public double getMemoryUsagePercentage() {
            return (double) usedMemory / maxMemory * 100.0;
        }
        
        public boolean isMemoryPressureHigh() {
            return getMemoryUsagePercentage() > 80.0;
        }
        
        public boolean isCpuUsageHigh() {
            return cpuUsage > 80.0;
        }
        
        public boolean isThreadCountHigh() {
            return totalThreads > Runtime.getRuntime().availableProcessors() * 10;
        }
    }
}