package com.xiang.proxy.server.controller;

import com.xiang.proxy.server.ProxyServerV2;
import com.xiang.proxy.server.inbound.InboundServerManager;
import com.xiang.proxy.server.service.ProxyServerV2Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * ProxyServerV2 管理控制器
 * 提供ProxyServerV2的状态查询和管理接口
 */
@RestController
@RequestMapping("/api/proxy-server")
public class ProxyServerV2Controller {
    
    private static final Logger logger = LoggerFactory.getLogger(ProxyServerV2Controller.class);
    
    @Autowired
    private ProxyServerV2Service proxyServerV2Service;
    
    /**
     * 获取ProxyServerV2状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            status.put("started", proxyServerV2Service.isStarted());
            status.put("status", proxyServerV2Service.getStatus());
            status.put("startup_failed", proxyServerV2Service.isStartupFailed());
            
            // 如果启动失败，包含异常信息
            if (proxyServerV2Service.isStartupFailed()) {
                Exception startupException = proxyServerV2Service.getStartupException();
                if (startupException != null) {
                    status.put("startup_error", startupException.getMessage());
                }
            }
            
            ProxyServerV2 proxyServer = proxyServerV2Service.getProxyServer();
            if (proxyServer != null) {
                status.put("running", proxyServer.isRunning());
                
                // 获取详细统计信息
                if (proxyServer.isRunning()) {
                    try {
                        InboundServerManager.ManagerStatistics stats = 
                                proxyServer.getInboundServerManager().getStatistics();
                        status.put("inbound_statistics", stats);
                        
                        status.put("active_connections", 
                                proxyServer.getProxyProcessor().getActiveConnectionCount());
                        
                        status.put("router_statistics", 
                                proxyServer.getRouter().getStatistics());
                    } catch (Exception e) {
                        logger.warn("获取详细统计信息失败", e);
                        status.put("statistics_error", e.getMessage());
                    }
                }
            }
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            logger.error("获取ProxyServerV2状态失败", e);
            status.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(status);
        }
    }
    
    /**
     * 获取Inbound服务器健康报告
     */
    @GetMapping("/health-report")
    public ResponseEntity<Object> getHealthReport() {
        try {
            ProxyServerV2 proxyServer = proxyServerV2Service.getProxyServer();
            if (proxyServer == null || !proxyServer.isRunning()) {
                return ResponseEntity.badRequest()
                        .body(Map.of("error", "ProxyServerV2 is not running"));
            }
            
            InboundServerManager.HealthReport healthReport = 
                    proxyServer.getInboundServerManager().getHealthReport();
            
            return ResponseEntity.ok(healthReport);
            
        } catch (Exception e) {
            logger.error("获取健康报告失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", e.getMessage()));
        }
    }
    
    /**
     * 获取配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Object> getConfig() {
        try {
            ProxyServerV2 proxyServer = proxyServerV2Service.getProxyServer();
            if (proxyServer == null) {
                return ResponseEntity.badRequest()
                        .body(Map.of("error", "ProxyServerV2 is not initialized"));
            }
            
            return ResponseEntity.ok(proxyServer.getConfig());
            
        } catch (Exception e) {
            logger.error("获取配置信息失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", e.getMessage()));
        }
    }
}