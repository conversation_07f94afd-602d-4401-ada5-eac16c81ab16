package com.xiang.proxy.server.outbound;

import io.netty.channel.Channel;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 出站连接模型
 */
public class OutboundConnection {
    private final String connectionId;
    private final Channel backendChannel;
    private final String targetHost;
    private final int targetPort;
    private final String protocol;
    private final long createTime;
    private final AtomicLong bytesTransferred;
    private final Map<String, Object> attributes;
    private volatile boolean active;
    private volatile long lastActiveTime;

    private OutboundConnection(Builder builder) {
        this.connectionId = builder.connectionId != null ? builder.connectionId : UUID.randomUUID().toString();
        this.backendChannel = builder.backendChannel;
        this.targetHost = builder.targetHost;
        this.targetPort = builder.targetPort;
        this.protocol = builder.protocol;
        this.createTime = builder.createTime > 0 ? builder.createTime : System.currentTimeMillis();
        this.bytesTransferred = new AtomicLong(0);
        this.attributes = new HashMap<>(builder.attributes);
        this.active = true;
        this.lastActiveTime = this.createTime;
    }

    // Getters
    public String getConnectionId() {
        return connectionId;
    }

    public Channel getBackendChannel() {
        return backendChannel;
    }

    public String getTargetHost() {
        return targetHost;
    }

    public int getTargetPort() {
        return targetPort;
    }

    public String getProtocol() {
        return protocol;
    }

    public long getCreateTime() {
        return createTime;
    }

    public long getBytesTransferred() {
        return bytesTransferred.get();
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public boolean isActive() {
        return active && backendChannel != null && backendChannel.isActive();
    }

    public long getLastActiveTime() {
        return lastActiveTime;
    }

    // 状态管理方法
    public void markActive() {
        this.lastActiveTime = System.currentTimeMillis();
    }

    public void markInactive() {
        this.active = false;
    }

    public void addBytesTransferred(long bytes) {
        this.bytesTransferred.addAndGet(bytes);
        markActive();
    }

    // 便捷方法
    public String getTarget() {
        return targetHost + ":" + targetPort;
    }

    public long getConnectionAge() {
        return System.currentTimeMillis() - createTime;
    }

    public long getIdleTime() {
        return System.currentTimeMillis() - lastActiveTime;
    }

    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

    public <T> T getAttribute(String key, T defaultValue) {
        T value = getAttribute(key);
        return value != null ? value : defaultValue;
    }

    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }

    public boolean hasAttribute(String key) {
        return attributes.containsKey(key);
    }

    @Override
    public String toString() {
        return String.format("OutboundConnection{id=%s, target=%s:%d, protocol=%s, active=%s, bytes=%d}",
                connectionId, targetHost, targetPort, protocol, isActive(), getBytesTransferred());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        OutboundConnection that = (OutboundConnection) obj;
        return connectionId.equals(that.connectionId);
    }

    @Override
    public int hashCode() {
        return connectionId.hashCode();
    }

    // Builder模式
    public static class Builder {
        private String connectionId;
        private Channel backendChannel;
        private String targetHost;
        private int targetPort;
        private String protocol;
        private long createTime;
        private Map<String, Object> attributes = new HashMap<>();

        public Builder connectionId(String connectionId) {
            this.connectionId = connectionId;
            return this;
        }

        public Builder backendChannel(Channel channel) {
            this.backendChannel = channel;
            return this;
        }

        public Builder target(String host, int port) {
            this.targetHost = host;
            this.targetPort = port;
            return this;
        }

        public Builder protocol(String protocol) {
            this.protocol = protocol;
            return this;
        }

        public Builder createTime(long createTime) {
            this.createTime = createTime;
            return this;
        }

        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }

        public Builder attributes(Map<String, Object> attributes) {
            this.attributes.putAll(attributes);
            return this;
        }

        public OutboundConnection build() {
            if (backendChannel == null) {
                throw new IllegalArgumentException("Backend channel is required");
            }
            if (targetHost == null || targetHost.trim().isEmpty()) {
                throw new IllegalArgumentException("Target host is required");
            }
            if (targetPort <= 0 || targetPort > 65535) {
                throw new IllegalArgumentException("Invalid target port: " + targetPort);
            }
            if (protocol == null || protocol.trim().isEmpty()) {
                throw new IllegalArgumentException("Protocol is required");
            }

            return new OutboundConnection(this);
        }
    }

    // 静态工厂方法
    public static Builder builder() {
        return new Builder();
    }

    // 属性键常量
    public static final class Attributes {
        public static final String OUTBOUND_ID = "outbound.id";
        public static final String ROUTE_RULE_ID = "route.rule.id";
        public static final String CLIENT_CONNECTION_ID = "client.connection.id";
        public static final String SESSION_ID = "session.id";
        public static final String CONNECTION_POOL_KEY = "pool.key";
        public static final String RETRY_COUNT = "retry.count";
        public static final String CONNECT_TIME = "connect.time";
        public static final String FIRST_BYTE_TIME = "first.byte.time";
    }
}