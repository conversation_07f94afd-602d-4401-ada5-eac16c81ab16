# 多路复用代理系统核心架构分析

## 🏗️ 整体架构

### 服务器端架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   ProxyServer   │───▶│ ProxyServerInit  │───▶│ ProtocolDetector│
│   (主服务器)     │    │   (初始化器)      │    │   (协议检测)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  HostBlacklist  │◀───│MultiplexProxyHand│───▶│ ConnectionPool  │
│   (黑名单系统)   │    │   (多路复用处理)  │    │   (连接池)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │PerformanceMetrics│    │MultiplexBackend │
                    │   (性能监控)     │    │   (后端处理)     │
                    └─────────────────┘    └─────────────────┘
```

### 客户端架构（多组件架构）
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   ProxyClient   │───▶│ProxyClientManager│───▶│ AddressFilter   │
│ (多组件主类)     │    │   (管理器)       │    │  (地址过滤)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │ConnectionManager│    │   GeoIPUtil     │
                    │   (连接管理)     │    │ (IP地理判断)    │
                    └─────────────────┘    └─────────────────┘
                                │
                                ▼
        ┌─────────────────────────────────────────────────────────┐
        │                  多协议接入层                            │
        ├─────────────────┬─────────────────┬─────────────────────┤
        │  Socks5Inbound  │   HttpInbound   │  其他协议接入...     │
        │   (SOCKS5)      │  (HTTP CONNECT) │                     │
        └─────────────────┴─────────────────┴─────────────────────┘
                                │
                                ▼
                    ┌─────────────────┐
                    │   UdpInbound    │
                    │   (UDP处理)     │
                    └─────────────────┘
                                │
                                ▼
                    ┌─────────────────┐
                    │DirectConnection │
                    │   Handler       │
                    └─────────────────┘
```

### 多组件架构详解
```
ProxyClient (主启动类)
    │
    ├── ProxyClientManager (管理器)
    │   ├── ConnectionManager (连接管理)
    │   ├── AddressFilter (地址过滤)
    │   └── List<ProxyInbound> (接入组件列表)
    │       ├── Socks5Inbound (SOCKS5接入)
    │       │   ├── Port: 1081
    │       │   └── MultiplexSocks5Handler
    │       ├── HttpInbound (HTTP接入)
    │       │   ├── Port: 1082
    │       │   └── HttpConnectHandler
    │       └── 其他协议接入...
    │
    ├── ConfigManager (配置管理)
    │   ├── YAML配置解析
    │   ├── Properties配置解析
    │   └── 配置属性绑定
    │
    └── SSL/TLS支持
        ├── ClientSslContextManager
        └── 证书管理
```

## 🎯 核心功能模块

### 1. 服务器端核心模块

#### ProxyServer (主服务器)
```java
// 核心职责：
- 监听指定端口 (默认8888)
- 接受多个客户端连接
- 管理EventLoopGroup线程池
- 启动连接池和黑名单系统
- 启动性能监控系统
- 优雅关闭处理
```

#### MultiplexProxyHandler (连接处理器)
```java
// 每个客户端连接的独立处理器实例
- 支持最多200个并发会话/客户端
- 协议解析和数据包处理(TCP/UDP)
- 会话生命周期管理
- 与连接池和黑名单系统集成
- 性能指标收集和统计
- UDP数据包转发处理
```

#### PerformanceMetrics (性能监控)
```java
// 实时性能监控系统
- 连接数、会话数统计
- 数据传输量监控
- 连接池使用率统计
- 黑名单命中率分析
- 定期性能报告输出
```

### 2. 客户端核心模块

#### ProxyClient (多组件客户端主类)
```java
// 核心职责：
- 启动多协议代理服务
- 解析命令行参数和配置文件
- 初始化ProxyClientManager
- 管理客户端生命周期
- 支持配置文件和命令行两种启动方式
```

#### ProxyClientManager (客户端管理器)
```java
// 核心职责：
- 管理多个ProxyInbound组件
- 统一的ConnectionManager和AddressFilter
- 组件生命周期管理
- 状态监控和报告
- 优雅关闭处理
```

#### ProxyInbound (接入组件接口)
```java
// 接入组件抽象：
- 定义协议接入标准接口
- 支持SOCKS5、HTTP CONNECT等协议
- 独立端口监听
- 统一的配置和管理接口
```

#### Socks5Inbound (SOCKS5接入组件)
```java
// SOCKS5协议实现：
- 完整的SOCKS5协议支持
- 与ConnectionManager集成
- 地址过滤集成
- 多路复用会话管理
- UDP ASSOCIATE命令支持
```

#### HttpInbound (HTTP接入组件)
```java
// HTTP CONNECT协议实现：
- HTTP隧道代理协议
- CONNECT方法处理
- 与SOCKS5共享连接管理
- 统一的地址过滤机制
```

#### AddressFilter (地址过滤器)
```java
// 智能分流决策：
- 三种过滤模式支持
- 与GeoIPUtil集成
- 动态模式切换
- 连接方式决策
```

#### GeoIPUtil (IP地理位置判断)
```java
// IP地理位置服务：
- 基于APNIC官方数据
- 支持在线数据更新
- 高精度中国IP判断
- 私有地址识别
- IP段缓存管理
```

### 3. 连接复用系统

#### ConnectionPool (连接池)
```java
// 核心特性：
- 每个主机最多20个连接（优化后）
- FIFO连接获取策略
- 连接健康状态检查
- 60秒空闲超时（优化后）
- 15秒定期清理
- 线程安全的并发访问
- 批量连接清理优化
```

**连接复用流程：**
1. **获取连接**: 优先从池中获取可用连接
2. **状态验证**: 检查连接active/writable/open状态
3. **连接准备**: 清理旧的处理器，设置新的处理器
4. **连接归还**: 会话结束后清理状态并归还到池

### 4. 黑名单功能

#### HostBlacklist (黑名单系统)
```java
// 智能屏蔽机制：
- 失败阈值：连续失败3次
- 缓存时间：5分钟自动清除
- 最大条目：1000个主机
- 自动恢复：成功连接立即清除
- 定期清理：每分钟清理过期条目
- 时间缓存优化：减少系统调用
- 命中统计：详细的使用效果分析
```

**黑名单工作流程：**
1. **连接检查**: 请求前检查主机是否在黑名单
2. **失败记录**: 连接失败时增加失败计数
3. **自动加入**: 达到阈值后加入黑名单
4. **快速拒绝**: 黑名单主机直接返回HOST_UNREACHABLE
5. **自动清除**: 成功连接或超时后清除记录
6. **统计分析**: 记录命中次数和效果评估

## 🔄 核心工作流程

### 服务器端连接处理流程
```
客户端连接 → 协议检测 → 创建MultiplexProxyHandler → 处理数据包
    ↓
连接请求 → 黑名单检查 → 连接池获取 → 后端连接 → 响应客户端
    ↓
数据传输 → 会话管理 → 连接复用 → 连接归还 → 性能统计
```

### 客户端地址过滤流程
```
SOCKS5请求 → 解析目标地址 → AddressFilter判断 → 连接方式决策
    ↓
CHINA_DIRECT模式 → GeoIPUtil判断 → 中国IP直连/海外IP代理
    ↓
直连模式 → DirectConnectionHandler → 直接连接目标
代理模式 → ConnectionManager → 多路复用连接服务器
```

### 连接池管理流程
```
连接请求 → 检查池中连接 → 验证连接状态 → 准备连接重用(TCP)
    ↓
UDP请求 → 创建UDP通道 → 短暂缓存UDP通道
    ↓
无可用连接 → 创建新连接 → 连接成功 → 加入统计
    ↓
会话结束 → 清理连接状态 → 归还到连接池 → 等待下次复用
```

### 黑名单管理流程
```
连接请求 → 检查黑名单 → 直接拒绝(如果在黑名单) → 统计命中
    ↓
连接失败 → 记录失败 → 检查阈值 → 加入黑名单
    ↓
连接成功 → 清除失败记录 → 从黑名单移除
```

### GeoIP数据管理流程
```
系统启动 → 加载配置文件 → 在线数据更新 → 内置数据备用
    ↓
IP判断请求 → 私有地址检查 → CIDR匹配算法 → 返回判断结果
    ↓
定期更新 → 在线数据源 → 数据验证 → 缓存更新
```

## ⚙️ 配置参数

### 服务器端配置参数

#### ConnectionPoolConfig
```java
// 连接池配置（优化后）
ENABLE_CONNECTION_POOL = true          // 启用连接池
MAX_CONNECTIONS_PER_HOST = 20          // 每主机最大连接数（提升）
CONNECTION_IDLE_TIMEOUT = 60000        // 连接空闲超时(60秒，优化)
CONNECTION_MAX_LIFETIME = 1800000      // 连接最大存活时间(30分钟，新增)
CONNECTION_TIMEOUT = 5000              // 连接建立超时(5秒，优化)
POOL_CLEANUP_INTERVAL = 15000          // 清理间隔(15秒)

// 黑名单配置
BLACKLIST_CACHE_TIME = 300000          // 黑名单缓存时间(5分钟)
MAX_BLACKLIST_SIZE = 1000              // 最大黑名单条目数
```

#### MultiplexProxyHandler
```java
MAX_SESSIONS_PER_CLIENT = 200          // 每客户端最大会话数
FAILURE_THRESHOLD = 3                  // 黑名单失败阈值
```

### 客户端配置参数

#### ProxyClientConfig
```java
// 地址过滤配置
filter.mode = CHINA_DIRECT             // 过滤模式
proxy.server.host = localhost          // 代理服务器地址
proxy.server.port = 8888               // 代理服务器端口
local.port = 1080                      // 本地监听端口
```

#### GeoIPUtil
```java
// IP段数据配置
CHINA_IP_RANGES_FILE = "china-ip-ranges.txt"  // 配置文件名
ONLINE_DATA_SOURCES = [...]            // 在线数据源URL列表
UPDATE_TIMEOUT = 30000                 // 在线更新超时时间
```

## 🚀 性能特性

### 多客户端支持
- 每个客户端独立的处理器实例
- 支持数百个并发客户端连接
- 每客户端最多200个并发会话

### 连接复用优化
- 连接复用率可达80%以上
- 避免频繁TCP握手，延迟降低100-200ms
- 大幅减少文件描述符使用

### 黑名单优化
- 无法访问的主机从2秒超时减少到几毫秒拒绝
- 避免重复尝试连接已知无法访问的域名
- 自动恢复机制防止永久屏蔽

## 🔧 核心类职责

### 服务器端核心类

| 类名 | 核心职责 | 关键特性 |
|------|----------|----------|
| ProxyServer | 主服务器启动和管理 | 多线程、优雅关闭、系统初始化 |
| MultiplexProxyHandler | 客户端连接处理 | 会话管理、协议处理、性能统计 |
| ConnectionPool | 连接池管理 | 连接复用、健康检查、批量清理 |
| HostBlacklist | 黑名单管理 | 智能屏蔽、自动恢复、时间优化 |
| MultiplexProtocol | 协议定义 | V2协议、数据包编解码 |
| PerformanceMetrics | 性能监控 | 实时统计、定期报告 |
| MultiplexBackendHandler | 后端连接处理 | 数据转发、连接管理 |

### 客户端核心类

| 类名 | 核心职责 | 关键特性 |
|------|----------|----------|
| Socks5ProxyClient | 客户端主类 | 服务启动、配置加载 |
| MultiplexSocks5Handler | SOCKS5协议处理 | 协议解析、地址过滤集成 |
| AddressFilter | 地址过滤决策 | 三种模式、动态切换 |
| GeoIPUtil | IP地理位置判断 | APNIC数据、在线更新 |
| ConnectionManager | 连接管理 | 多路复用连接、会话管理 |
| DirectConnectionHandler | 直连处理 | 直接连接、数据转发 |
| ProxyClientConfig | 配置管理 | 配置加载、参数管理 |

## 📊 监控指标

### 服务器端监控指标

#### 连接池指标
- 各主机的池中连接数
- 连接创建和复用统计
- 连接健康状态监控
- 连接复用率和命中率
- 连接池清理统计

#### 黑名单指标
- 黑名单命中率和次数
- 失败主机统计
- 自动恢复次数
- 黑名单大小和清理统计

#### 性能指标
- 平均连接建立时间
- 会话并发数和峰值
- 数据传输吞吐量
- 客户端连接数统计
- 系统资源使用情况

### 客户端监控指标

#### 地址过滤指标
- 各过滤模式使用统计
- 中国IP判断准确率
- 直连/代理连接比例
- GeoIP数据更新状态

#### 连接统计
- 直连连接成功率
- 代理连接成功率
- 平均连接延迟
- 数据传输统计

#### 系统指标
- IP段数据加载状态
- 配置文件加载情况
- 内存使用和性能
