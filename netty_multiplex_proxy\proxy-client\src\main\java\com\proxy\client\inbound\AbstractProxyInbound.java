package com.proxy.client.inbound;

import com.proxy.client.connection.ConnectionManager;
import com.proxy.client.connection.IConnectionManager;
import com.proxy.client.queue.QueuedConnectionManager;
import com.proxy.client.filter.AddressFilter;
import com.proxy.client.config.ProxyClientConfigManager;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 代理接入组件抽象基类
 * 提供通用的启动、停止、状态管理功能
 */
public abstract class AbstractProxyInbound implements ProxyInbound {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    protected final String name;
    protected final ProxyProtocol protocol;
    protected final int port;
    protected final IConnectionManager connectionManager;
    protected final AddressFilter addressFilter;
    protected final ProxyClientConfigManager configManager;
    
    // Netty组件
    protected EventLoopGroup bossGroup;
    protected EventLoopGroup workerGroup;
    protected Channel serverChannel;
    
    // 状态管理
    protected volatile boolean running = false;
    protected volatile long startTime = 0;
    protected volatile String lastError = null;
    protected final AtomicLong activeConnections = new AtomicLong(0);
    protected final AtomicLong totalConnections = new AtomicLong(0);
    
    public AbstractProxyInbound(String name, ProxyProtocol protocol, int port,
                               IConnectionManager connectionManager,
                               AddressFilter addressFilter,
                               ProxyClientConfigManager configManager) {
        this.name = name;
        this.protocol = protocol;
        this.port = port;
        this.connectionManager = connectionManager;
        this.addressFilter = addressFilter;
        this.configManager = configManager;
    }
    
    /**
     * 获取连接管理器
     */
    protected IConnectionManager getConnectionManager() {
        return connectionManager;
    }
    
    /**
     * 获取队列化连接管理器（如果是的话）
     */
    protected QueuedConnectionManager getQueuedConnectionManager() {
        return (connectionManager instanceof QueuedConnectionManager) 
            ? (QueuedConnectionManager) connectionManager 
            : null;
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public ProxyProtocol getProtocol() {
        return protocol;
    }
    
    @Override
    public int getPort() {
        return port;
    }
    
    @Override
    public ChannelFuture start() {
        if (running) {
            logger.warn("{} 组件已经在运行中", name);
            return null;
        }
        
        // Calculate worker thread count
        int workerThreadCount = configManager.getProperties().getPerformance().getWorkerThreads();
        if (workerThreadCount <= 0) {
            // Automatically calculate thread count based on CPU cores
            workerThreadCount = Math.max(2, Runtime.getRuntime().availableProcessors() * 2);
        }
        
        final int finalWorkerThreadCount = workerThreadCount;
        logger.info("启动 {} 组件，监听端口: {}", name, port);
        
        try {
            // Create thread factories with meaningful names
            ThreadFactory bossThreadFactory = new DefaultThreadFactory("proxy-client-boss-" + port);
            ThreadFactory workerThreadFactory = new DefaultThreadFactory("proxy-client-worker-" + port);
            
            // Create event loop groups with configurable thread counts
            // Boss group for accepting connections (1 thread is usually sufficient)
            bossGroup = new NioEventLoopGroup(1, bossThreadFactory);
            
            // Worker group for handling I/O operations
            workerGroup = new NioEventLoopGroup(finalWorkerThreadCount, workerThreadFactory);
            
            // Create server bootstrap
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(createChannelInitializer())
                    .option(ChannelOption.SO_BACKLOG, 128)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.TCP_NODELAY, true);
            
            // Bind port and start
            ChannelFuture future = bootstrap.bind(port);
            future.addListener((ChannelFutureListener) channelFuture -> {
                if (channelFuture.isSuccess()) {
                    serverChannel = channelFuture.channel();
                    running = true;
                    startTime = System.currentTimeMillis();
                    lastError = null;
                    logger.info("{} 组件启动成功，监听端口: {}，工作线程数: {}", name, port, finalWorkerThreadCount);
                } else {
                    lastError = channelFuture.cause().getMessage();
                    logger.error("{} 组件启动失败: {}", name, lastError, channelFuture.cause());
                    cleanup();
                }
            });
            
            return future;
            
        } catch (Exception e) {
            lastError = e.getMessage();
            logger.error("{} 组件启动异常: {}", name, lastError, e);
            cleanup();
            return null;
        }
    }
    
    @Override
    public void stop() {
        if (!running) {
            logger.warn("{} 组件未在运行", name);
            return;
        }
        
        logger.info("停止 {} 组件", name);
        running = false;
        
        try {
            if (serverChannel != null) {
                serverChannel.close().sync();
            }
        } catch (InterruptedException e) {
            logger.warn("{} 组件停止时被中断", name);
            Thread.currentThread().interrupt();
        } finally {
            cleanup();
        }
        
        logger.info("{} 组件已停止", name);
    }
    
    @Override
    public boolean isRunning() {
        return running;
    }
    
    @Override
    public InboundStatus getStatus() {
        return new InboundStatus(
            running,
            port,
            startTime,
            activeConnections.get(),
            totalConnections.get(),
            lastError
        );
    }
    
    /**
     * 创建通道初始化器（由子类实现）
     */
    protected abstract ChannelInitializer<Channel> createChannelInitializer();
    
    /**
     * 连接建立时调用
     */
    protected void onConnectionEstablished() {
        activeConnections.incrementAndGet();
        totalConnections.incrementAndGet();
    }
    
    /**
     * 连接关闭时调用
     */
    protected void onConnectionClosed() {
        activeConnections.decrementAndGet();
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
        }
        if (bossGroup != null) {
            bossGroup.shutdownGracefully();
        }
    }
}
