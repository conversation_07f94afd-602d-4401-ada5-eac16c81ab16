[{"name": "com.proxy.client.Socks5ProxyClient", "methods": [{"name": "<init>", "parameterTypes": ["int", "java.lang.String", "int"]}, {"name": "main", "parameterTypes": ["java.lang.String[]"]}]}, {"name": "com.proxy.client.filter.AddressFilter$FilterMode", "methods": [{"name": "valueOf", "parameterTypes": ["java.lang.String"]}, {"name": "values", "parameterTypes": []}]}, {"name": "io.netty.channel.socket.nio.NioServerSocketChannel", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "io.netty.channel.socket.nio.NioSocketChannel", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "io.netty.channel.nio.NioEventLoopGroup", "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "<init>", "parameterTypes": ["int"]}]}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.BaseMpscLinkedArrayQueueProducerFields", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.BaseMpscLinkedArrayQueueConsumerFields", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.BaseMpscLinkedArrayQueueColdProducerFields", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.MpscArrayQueueProducerIndexField", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.MpscArrayQueueProducerLimitField", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.MpscArrayQueueConsumerIndexField", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.internal.shaded.org.jctools.util.UnsafeAccess", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.internal.PlatformDependent0", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.internal.PlatformDependent", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "sun.misc.Unsafe", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.buffer.ByteBufAllocator", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.buffer.UnpooledByteBufAllocator", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "<init>", "parameterTypes": ["boolean"]}, {"name": "<init>", "parameterTypes": ["boolean", "boolean"]}, {"name": "<init>", "parameterTypes": ["boolean", "boolean", "boolean"]}]}, {"name": "io.netty.buffer.PooledByteBufAllocator", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "<init>", "parameterTypes": ["boolean"]}, {"name": "<init>", "parameterTypes": ["boolean", "int", "int", "int", "int"]}]}, {"name": "io.netty.buffer.UnpooledByteBufAllocator$InstrumentedUnpooledUnsafeHeapByteBuf", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.buffer.UnpooledByteBufAllocator$InstrumentedUnpooledUnsafeDirectByteBuf", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.buffer.AbstractByteBufAllocator", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.internal.PlatformDependent$Mpsc", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.ResourceLeakDetector", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.ReferenceCountUtil", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "touch", "parameterTypes": ["java.lang.Object"]}, {"name": "touch", "parameterTypes": ["java.lang.Object", "java.lang.String"]}, {"name": "release", "parameterTypes": ["java.lang.Object"]}, {"name": "release", "parameterTypes": ["java.lang.Object", "int"]}, {"name": "retain", "parameterTypes": ["java.lang.Object"]}, {"name": "retain", "parameterTypes": ["java.lang.Object", "int"]}]}, {"name": "io.netty.channel.DefaultChannelPipeline", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.channel.AbstractChannelHandlerContext", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.ResourceLeakDetector$Level", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "valueOf", "parameterTypes": ["java.lang.String"]}, {"name": "values", "parameterTypes": []}]}, {"name": "io.netty.util.ResourceLeakDetectorFactory", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "io.netty.util.internal.logging.InternalLoggerFactory", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "com.proxy.client.config.properties.ProxyClientProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$FilterProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$ProxyProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$ProxyProperties$ServerProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$LocalProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$AuthProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$AuthProperties$TimeoutProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$InboundProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$InboundItemProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$SslProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$OnlineDataSourcesProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$OnlineDataSourcesProperties$DataSourceProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$PerformanceProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$QueueProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$QueueProperties$RetryProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$QueueProperties$MonitoringProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.properties.ProxyClientProperties$NacosProperties", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.proxy.client.config.binder.ConfigurationBinder", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "com.proxy.client.config.annotation.ConfigurationProperties", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "com.proxy.client.config.ProxyClientConfigManager", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "getInstance", "parameterTypes": []}]}, {"name": "java.util.ArrayList", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "<init>", "parameterTypes": ["int"]}, {"name": "<init>", "parameterTypes": ["java.util.Collection"]}]}, {"name": "java.util.Arrays", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "java.util.List", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "java.lang.String", "allDeclaredFields": true, "allDeclaredMethods": true}, {"name": "java.lang.Integer", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "valueOf", "parameterTypes": ["int"]}, {"name": "valueOf", "parameterTypes": ["java.lang.String"]}, {"name": "intValue", "parameterTypes": []}]}, {"name": "java.lang.Bo<PERSON>an", "allDeclaredFields": true, "allDeclaredMethods": true, "methods": [{"name": "valueOf", "parameterTypes": ["boolean"]}, {"name": "valueOf", "parameterTypes": ["java.lang.String"]}, {"name": "booleanValue", "parameterTypes": []}]}]