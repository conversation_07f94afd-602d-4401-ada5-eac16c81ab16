package com.xiang.proxy.server.core;

import com.xiang.proxy.server.config.ProxyProcessorConfig;
import com.xiang.proxy.server.router.Router;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 代理处理器工厂类
 * 提供不同类型处理器的创建方法
 */
public class ProxyProcessorFactory {
    private static final Logger logger = LoggerFactory.getLogger(ProxyProcessorFactory.class);

    /**
     * 处理器类型枚举
     */
    public enum ProcessorType {
        STANDARD,           // 标准处理器
        BATCH,             // 批量处理器
        CONNECTION_POOLED, // 连接池处理器
        HIGH_PERFORMANCE   // 高性能处理器（批量+连接池+自适应）
    }

    /**
     * 创建标准处理器
     */
    public static ProxyProcessor createStandardProcessor(Router router) {
        ProxyProcessorConfig config = ProxyProcessorConfig.defaultConfig();
        return new ProxyProcessor(router, config);
    }

    /**
     * 创建批量处理器
     */
    public static BatchProxyProcessor createBatchProcessor(Router router) {
        ProxyProcessorConfig config = ProxyProcessorConfig.defaultConfig()
                .setBatchSize(15)
                .setBatchTimeoutMs(50);
        return new BatchProxyProcessor(router, config);
    }

    /**
     * 创建高性能处理器
     */
    public static BatchProxyProcessor createHighPerformanceProcessor(Router router) {
        ProxyProcessorConfig config = ProxyProcessorConfig.highPerformanceConfig();
        return new BatchProxyProcessor(router, config);
    }

    /**
     * 创建低资源处理器
     */
    public static ProxyProcessor createLowResourceProcessor(Router router) {
        ProxyProcessorConfig config = ProxyProcessorConfig.lowResourceConfig();
        return new ProxyProcessor(router, config);
    }

    /**
     * 根据类型创建处理器
     */
    public static ProxyProcessor createProcessor(ProcessorType type, Router router) {
        return createProcessor(type, router, null);
    }

    /**
     * 根据类型和配置创建处理器
     */
    public static ProxyProcessor createProcessor(ProcessorType type, Router router, ProxyProcessorConfig config) {
        switch (type) {
            case STANDARD:
                if (config == null) {
                    config = ProxyProcessorConfig.defaultConfig();
                }
                logger.info("创建标准处理器: {}", config);
                return new ProxyProcessor(router, config);

            case BATCH:
                if (config == null) {
                    config = ProxyProcessorConfig.defaultConfig()
                            .setBatchSize(10)
                            .setBatchTimeoutMs(50);
                }
                logger.info("创建批量处理器: {}", config);
                return new BatchProxyProcessor(router, config);

            case CONNECTION_POOLED:
                if (config == null) {
                    config = ProxyProcessorConfig.connectionPoolOptimizedConfig();
                }
                logger.info("创建连接池处理器: {}", config);
                return new BatchProxyProcessor(router, config);

            case HIGH_PERFORMANCE:
                if (config == null) {
                    config = ProxyProcessorConfig.highPerformanceConfig()
                            .setEnableAdaptiveAdjustment(true); // 高性能模式默认启用自适应调整
                }
                logger.info("创建高性能处理器: {}", config);
                return new BatchProxyProcessor(router, config);

            default:
                throw new IllegalArgumentException("Unsupported processor type: " + type);
        }
    }

    /**
     * 根据系统资源自动选择处理器类型
     */
    public static ProxyProcessor createAutoProcessor(Router router) {
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        long maxMemory = Runtime.getRuntime().maxMemory();
        
        ProcessorType recommendedType;
        ProxyProcessorConfig config;

        if (availableProcessors >= 8 && maxMemory > 2L * 1024 * 1024 * 1024) { // 8核+2GB
            // 高性能环境
            recommendedType = ProcessorType.HIGH_PERFORMANCE;
            config = ProxyProcessorConfig.highPerformanceConfig()
                    .setQueueCount(availableProcessors * 2)
                    .setBatchSize(20);
        } else if (availableProcessors >= 4 && maxMemory > 1L * 1024 * 1024 * 1024) { // 4核+1GB
            // 中等性能环境
            recommendedType = ProcessorType.BATCH;
            config = ProxyProcessorConfig.defaultConfig()
                    .setQueueCount(availableProcessors)
                    .setBatchSize(10);
        } else {
            // 低资源环境
            recommendedType = ProcessorType.STANDARD;
            config = ProxyProcessorConfig.lowResourceConfig();
        }

        logger.info("自动选择处理器类型: {} (CPU核心: {}, 最大内存: {}MB)", 
                recommendedType, availableProcessors, maxMemory / 1024 / 1024);

        return createProcessor(recommendedType, router, config);
    }

    /**
     * 创建处理器构建器
     */
    public static ProcessorBuilder builder() {
        return new ProcessorBuilder();
    }

    /**
     * 处理器构建器
     */
    public static class ProcessorBuilder {
        private Router router;
        private ProcessorType type = ProcessorType.STANDARD;
        private ProxyProcessorConfig config;
        private boolean autoStart = false;

        public ProcessorBuilder router(Router router) {
            this.router = router;
            return this;
        }

        public ProcessorBuilder type(ProcessorType type) {
            this.type = type;
            return this;
        }

        public ProcessorBuilder config(ProxyProcessorConfig config) {
            this.config = config;
            return this;
        }

        public ProcessorBuilder autoStart(boolean autoStart) {
            this.autoStart = autoStart;
            return this;
        }

        public ProcessorBuilder batchSize(int batchSize) {
            ensureConfig();
            this.config.setBatchSize(batchSize);
            return this;
        }

        public ProcessorBuilder queueCount(int queueCount) {
            ensureConfig();
            this.config.setQueueCount(queueCount);
            return this;
        }

        public ProcessorBuilder queueCapacity(int queueCapacity) {
            ensureConfig();
            this.config.setQueueCapacity(queueCapacity);
            return this;
        }

        private void ensureConfig() {
            if (this.config == null) {
                this.config = ProxyProcessorConfig.defaultConfig();
            }
        }

        public ProxyProcessor build() {
            if (router == null) {
                throw new IllegalStateException("Router is required");
            }

            ProxyProcessor processor = createProcessor(type, router, config);
            
            if (autoStart) {
                processor.start();
                logger.info("处理器已自动启动");
            }

            return processor;
        }
    }

    /**
     * 获取处理器推荐配置
     */
    public static ProcessorRecommendation getRecommendation(int expectedQPS, int avgRequestSize) {
        ProcessorType recommendedType;
        ProxyProcessorConfig config;
        String reason;

        if (expectedQPS > 10000) {
            // 高QPS场景
            recommendedType = ProcessorType.HIGH_PERFORMANCE;
            config = ProxyProcessorConfig.highPerformanceConfig()
                    .setQueueCount(Runtime.getRuntime().availableProcessors() * 4)
                    .setBatchSize(Math.min(50, expectedQPS / 1000))
                    .setQueueCapacity(expectedQPS / 10);
            reason = "高QPS场景，推荐使用高性能处理器";
        } else if (expectedQPS > 1000) {
            // 中等QPS场景
            recommendedType = ProcessorType.BATCH;
            config = ProxyProcessorConfig.defaultConfig()
                    .setBatchSize(Math.min(20, expectedQPS / 100))
                    .setQueueCapacity(expectedQPS / 5);
            reason = "中等QPS场景，推荐使用批量处理器";
        } else {
            // 低QPS场景
            recommendedType = ProcessorType.STANDARD;
            config = ProxyProcessorConfig.defaultConfig()
                    .setQueueCount(Math.max(2, Runtime.getRuntime().availableProcessors() / 2));
            reason = "低QPS场景，标准处理器即可满足需求";
        }

        return new ProcessorRecommendation(recommendedType, config, reason);
    }

    /**
     * 处理器推荐结果
     */
    public static class ProcessorRecommendation {
        private final ProcessorType type;
        private final ProxyProcessorConfig config;
        private final String reason;

        public ProcessorRecommendation(ProcessorType type, ProxyProcessorConfig config, String reason) {
            this.type = type;
            this.config = config;
            this.reason = reason;
        }

        public ProcessorType getType() { return type; }
        public ProxyProcessorConfig getConfig() { return config; }
        public String getReason() { return reason; }

        @Override
        public String toString() {
            return String.format("ProcessorRecommendation{type=%s, reason='%s', config=%s}",
                    type, reason, config);
        }
    }
}