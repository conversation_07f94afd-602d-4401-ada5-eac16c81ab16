package com.xiang.proxy.server.outbound;

import com.xiang.proxy.server.core.ProxyRequest;
import io.netty.buffer.ByteBuf;

import java.util.concurrent.CompletableFuture;

/**
 * Outbound处理器接口
 * 负责将请求转发到目标服务器，并处理响应回写
 */
public interface OutboundHandler {
    
    /**
     * 建立到目标服务器的连接
     * @param request 代理请求
     * @return 连接结果的Future
     */
    CompletableFuture<OutboundConnection> connect(ProxyRequest request);
    
    /**
     * 发送数据到目标服务器
     * @param connection 出站连接
     * @param data 要发送的数据
     * @return 发送结果的Future
     */
    CompletableFuture<Void> sendData(OutboundConnection connection, ByteBuf data);
    
    /**
     * 关闭连接
     * @param connection 出站连接
     * @return 关闭结果的Future
     */
    CompletableFuture<Void> closeConnection(OutboundConnection connection);
    
    /**
     * 获取Outbound ID
     * @return Outbound标识符
     */
    String getOutboundId();
    
    /**
     * 获取Outbound配置
     * @return 配置对象
     */
    OutboundConfig getConfig();
    
    /**
     * 检查Outbound是否可用
     * @return 是否可用
     */
    default boolean isAvailable() {
        return true;
    }
    
    /**
     * 获取Outbound类型
     * @return 类型名称
     */
    default String getType() {
        return "unknown";
    }
    
    /**
     * 获取优先级
     * 数字越小优先级越高
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
    
    /**
     * 初始化Outbound
     */
    default void initialize() {
        // 默认空实现
    }
    
    /**
     * 销毁Outbound
     */
    default void destroy() {
        // 默认空实现
    }
    
    /**
     * 获取健康状态
     * @return 健康状态
     */
    default HealthStatus getHealthStatus() {
        return HealthStatus.HEALTHY;
    }
    
    /**
     * 执行健康检查
     * @return 健康检查结果的Future
     */
    default CompletableFuture<HealthStatus> healthCheck() {
        return CompletableFuture.completedFuture(getHealthStatus());
    }
    
    /**
     * 获取统计信息
     * @return 统计信息
     */
    default OutboundStatistics getStatistics() {
        return new OutboundStatistics();
    }
    
    /**
     * 重置统计信息
     */
    default void resetStatistics() {
        // 默认空实现
    }
    
    /**
     * 健康状态枚举
     */
    enum HealthStatus {
        HEALTHY,    // 健康
        DEGRADED,   // 降级
        UNHEALTHY   // 不健康
    }
}