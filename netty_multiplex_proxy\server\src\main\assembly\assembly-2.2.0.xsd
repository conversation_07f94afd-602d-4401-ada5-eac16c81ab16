<?xml version="1.0"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<!-- =================== DO NOT EDIT THIS FILE ====================         -->
<!-- Generated by Modello 2.0.0,                                            -->
<!-- any modifications will be overwritten.                                 -->
<!-- ==============================================================         -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" xmlns="http://maven.apache.org/ASSEMBLY/2.2.0" targetNamespace="http://maven.apache.org/ASSEMBLY/2.2.0">
  <xs:element name="assembly" type="Assembly">
    <xs:annotation>
      <xs:documentation source="version">1.0.0+</xs:documentation>
      <xs:documentation source="description">
        
        An assembly defines a collection of files usually distributed in an
        archive format such as zip, tar, or tar.gz that is generated from a
        project.  For example, a project could produce a ZIP assembly which
        contains a project&apos;s JAR artifact in the root directory, the
        runtime dependencies in a lib/ directory, and a shell script to launch
        a stand-alone application.
        
      </xs:documentation>
    </xs:annotation>
  </xs:element>
  <xs:complexType name="Assembly">
    <xs:annotation>
      <xs:documentation source="version">1.0.0+</xs:documentation>
      <xs:documentation source="description">
        
        An assembly defines a collection of files usually distributed in an
        archive format such as zip, tar, or tar.gz that is generated from a
        project.  For example, a project could produce a ZIP assembly which
        contains a project&apos;s JAR artifact in the root directory, the
        runtime dependencies in a lib/ directory, and a shell script to launch
        a stand-alone application.
        
      </xs:documentation>
    </xs:annotation>
    <xs:all>
      <xs:element minOccurs="0" name="id" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the id of this assembly. This is a symbolic name for a
            particular assembly of files from this project. Also, aside from
            being used to distinctly name the assembled package by attaching
            its value to the generated archive, the id is used as your
            artifact&apos;s classifier when deploying.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="formats">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Specifies the formats of the assembly.

            It is often better to specify the formats via the goal parameter rather
            than here. For example, that allows different profiles to generate
            different types of archives.

            Multiple formats can be
            supplied and the Assembly Plugin will generate an archive for each
            of the desired formats. When deploying your project, all file formats
            specified will also be deployed. A format is specified by supplying
            one of the following values in a &amp;lt;format&amp;gt; subelement:
            &lt;ul&gt;
              &lt;li&gt;&lt;b&gt;&quot;zip&quot;&lt;/b&gt; - Creates a ZIP file format&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;tar&quot;&lt;/b&gt; - Creates a TAR format&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;tar.gz&quot;&lt;/b&gt; or &lt;b&gt;&quot;tgz&quot;&lt;/b&gt; - Creates a gzip&apos;d TAR format&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;tar.bz2&quot;&lt;/b&gt; or &lt;b&gt;&quot;tbz2&quot;&lt;/b&gt; - Creates a bzip&apos;d TAR format&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;tar.snappy&quot;&lt;/b&gt; - Creates a snappy&apos;d TAR format&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;tar.xz&quot;&lt;/b&gt; or &lt;b&gt;&quot;txz&quot;&lt;/b&gt; - Creates a xz&apos;d TAR format&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;tar.zst&quot;&lt;/b&gt; or &lt;b&gt;&quot;tzst&quot;&lt;/b&gt; - Creates a zst&apos;d TAR format&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;jar&quot;&lt;/b&gt; - Creates a JAR format&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;dir&quot;&lt;/b&gt; - Creates an exploded directory format&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;war&quot;&lt;/b&gt; - Creates a WAR format&lt;/li&gt;
            &lt;/ul&gt;
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="format" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="includeBaseDirectory" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">0.0.0+</xs:documentation>
          <xs:documentation source="description">
            Includes a base directory in the final archive. For example,
            if you are creating an assembly named &quot;your-app&quot;, setting
            includeBaseDirectory to true will create an archive that
            includes this base directory. If this option is set to false
            the archive created will unzip its content to the current
            directory.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="baseDirectory" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the base directory of the resulting assembly archive. If this is not
            set and includeBaseDirectory == true, ${project.build.finalName} will be used instead.
            (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="includeSiteDirectory" type="xs:boolean" default="false">
        <xs:annotation>
          <xs:documentation source="version">0.0.0+</xs:documentation>
          <xs:documentation source="description">
            Includes a site directory in the final archive. The site directory
            location of a project is determined by the siteDirectory parameter
            of the Assembly Plugin.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="containerDescriptorHandlers">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            
            Set of components which filter various container descriptors out of
            the normal archive stream, so they can be aggregated then added.
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="containerDescriptorHandler" minOccurs="0" maxOccurs="unbounded" type="ContainerDescriptorHandlerConfig"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="moduleSets">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Specifies which module files to include in the assembly. A moduleSet
            is specified by providing one or more of &amp;lt;moduleSet&amp;gt;
            subelements.
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="moduleSet" minOccurs="0" maxOccurs="unbounded" type="ModuleSet"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="fileSets">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Specifies which groups of files to include in the assembly. A
            fileSet is specified by providing one or more of &amp;lt;fileSet&amp;gt;
            subelements.
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="fileSet" minOccurs="0" maxOccurs="unbounded" type="FileSet"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="files">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Specifies which single files to include in the assembly. A file
            is specified by providing one or more of &amp;lt;file&amp;gt;
            subelements.
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="file" minOccurs="0" maxOccurs="unbounded" type="FileItem"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="dependencySets">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Specifies which dependencies to include in the assembly. A
            dependencySet is specified by providing one or more of
            &amp;lt;dependencySet&amp;gt; subelements.
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="dependencySet" minOccurs="0" maxOccurs="unbounded" type="DependencySet"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="componentDescriptors">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Specifies the shared components xml file locations to include in the
            assembly. The locations specified must be relative to the base location
            of the descriptor. If the descriptor was found via a &amp;lt;descriptorRef/&amp;gt;
            element in the
            classpath, any components it specifies will also be found on the classpath.
            If it is found by pathname via a &amp;lt;descriptor/&amp;gt; element
            the value here will be interpreted
            as a path relative to the project basedir.
            When multiple componentDescriptors are found, their
            contents are merged. Check out the &lt;a href=&quot;assembly-component.html&quot;&gt;
            descriptor components&lt;/a&gt; for more information. A
            componentDescriptor is specified by providing one or more of
            &amp;lt;componentDescriptor&amp;gt; subelements.
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="componentDescriptor" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="FileItem">
    <xs:annotation>
      <xs:documentation source="version">1.0.0+</xs:documentation>
      <xs:documentation source="description">
        A file allows individual file inclusion with the option to change
        the destination filename not supported by fileSets.
        Note: either source or sources is required
      </xs:documentation>
    </xs:annotation>
    <xs:all>
      <xs:element minOccurs="0" name="source" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the absolute or relative path from the module&apos;s directory
            of the file to be included in the assembly.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="sources">
        <xs:annotation>
          <xs:documentation source="version">2.1.0+</xs:documentation>
          <xs:documentation source="description">
            Set of absolute or relative paths from the module&apos;s directory
            of the files be combined and included in the assembly.
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="source" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="outputDirectory" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the output directory relative to the root
            of the root directory of the assembly. For example,
            &quot;log&quot; will put the specified files in the log directory.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="destName" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the destination filename in the outputDirectory.
            Default is the same name as the source&apos;s file.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="fileMode" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Similar to a UNIX permission, sets the file mode of the files included.
            THIS IS AN OCTAL VALUE.
            Format: (User)(Group)(Other) where each component is a sum of Read = 4,
            Write = 2, and Execute = 1.  For example, the value 0644
            translates to User read-write, Group and Other read-only.  The default value is 0644
            &lt;a href=&quot;http://www.onlamp.com/pub/a/bsd/2000/09/06/FreeBSD_Basics.html&quot;&gt;(more on unix-style permissions)&lt;/a&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="lineEnding" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Sets the line-endings of the files in this file.
            Valid values are:
            &lt;ul&gt;
              &lt;li&gt;&lt;b&gt;&quot;keep&quot;&lt;/b&gt; - Preserve all line endings&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;unix&quot;&lt;/b&gt; - Use Unix-style line endings (i.e. &quot;\n&quot;)&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;lf&quot;&lt;/b&gt; - Use a single line-feed line endings (i.e. &quot;\n&quot;)&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;dos&quot;&lt;/b&gt; - Use DOS-/Windows-style line endings (i.e. &quot;\r\n&quot;)&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;windows&quot;&lt;/b&gt; - Use DOS-/Windows-style line endings (i.e. &quot;\r\n&quot;)&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;crlf&quot;&lt;/b&gt; - Use carriage-return, line-feed line endings (i.e. &quot;\r\n&quot;)&lt;/li&gt;
            &lt;/ul&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="filtered" type="xs:boolean" default="false">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets whether to determine if the file is filtered.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="ContainerDescriptorHandlerConfig">
    <xs:annotation>
      <xs:documentation source="version">1.1.0+</xs:documentation>
      <xs:documentation source="description">
        Configures a filter for files headed into the assembly archive, to enable
        aggregation of various types of descriptor fragments, such as components.xml,
        web.xml, etc.
      </xs:documentation>
    </xs:annotation>
    <xs:all>
      <xs:element minOccurs="0" name="handlerName" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            The handler&apos;s plexus role-hint, for lookup from the container.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="configuration">
        <xs:annotation>
          <xs:documentation source="version">1.1.1+</xs:documentation>
          <xs:documentation source="description">
            Configuration options for the handler.
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:any minOccurs="0" maxOccurs="unbounded" processContents="skip"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="FileSet">
    <xs:annotation>
      <xs:documentation source="version">1.0.0+</xs:documentation>
      <xs:documentation source="description">
        A fileSet allows the inclusion of groups of files into the assembly.
      </xs:documentation>
    </xs:annotation>
    <xs:all>
      <xs:element minOccurs="0" name="useDefaultExcludes" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            Whether standard exclusion patterns, such as those matching CVS and Subversion
            metadata files, should be used when calculating the files affected by this set.
            For backward compatibility, the default value is true. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="outputDirectory" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the output directory relative to the root
            of the root directory of the assembly. For example,
            &quot;log&quot; will put the specified files in the log directory.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="includes">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            When &amp;lt;include&amp;gt; subelements are present, they define a set of
            files and directory to include. If none is present, then
            &amp;lt;includes&amp;gt; represents all valid values.
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="include" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="excludes">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            When &amp;lt;exclude&amp;gt; subelements are present, they define a set of
            files and directory to exclude. If none is present, then
            &amp;lt;excludes&amp;gt; represents no exclusions.
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="exclude" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="fileMode" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Similar to a UNIX permission, sets the file mode of the files included.
            THIS IS AN OCTAL VALUE.
            Format: (User)(Group)(Other) where each component is a sum of Read = 4,
            Write = 2, and Execute = 1.  For example, the value 0644
            translates to User read-write, Group and Other read-only. The default value is 0644.
            &lt;a href=&quot;http://www.onlamp.com/pub/a/bsd/2000/09/06/FreeBSD_Basics.html&quot;&gt;(more on unix-style permissions)&lt;/a&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="directoryMode" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Similar to a UNIX permission, sets the directory mode of the directories
            included.
            THIS IS AN OCTAL VALUE.
            Format: (User)(Group)(Other) where each component is a sum of
            Read = 4, Write = 2, and Execute = 1.  For example, the value
            0755 translates to User read-write, Group and Other read-only.  The default value is 0755.
            &lt;a href=&quot;http://www.onlamp.com/pub/a/bsd/2000/09/06/FreeBSD_Basics.html&quot;&gt;(more on unix-style permissions)&lt;/a&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="directory" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the absolute or relative location from the module&apos;s
            directory. For example, &quot;src/main/bin&quot; would select this
            subdirectory of the project in which this dependency is defined.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="lineEnding" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Sets the line-endings of the files in this fileSet.
            Valid values:
            &lt;ul&gt;
              &lt;li&gt;&lt;b&gt;&quot;keep&quot;&lt;/b&gt; - Preserve all line endings&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;unix&quot;&lt;/b&gt; - Use Unix-style line endings (i.e. &quot;\n&quot;)&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;lf&quot;&lt;/b&gt; - Use a single line-feed line endings (i.e. &quot;\n&quot;)&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;dos&quot;&lt;/b&gt; - Use DOS-/Windows-style line endings (i.e. &quot;\r\n&quot;)&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;windows&quot;&lt;/b&gt; - Use DOS-/Windows-style line endings (i.e. &quot;\r\n&quot;)&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;crlf&quot;&lt;/b&gt; - Use carriage-return, line-feed line endings (i.e. &quot;\r\n&quot;)&lt;/li&gt;
            &lt;/ul&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="filtered" type="xs:boolean" default="false">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
          Whether to filter symbols in the files as they are copied, using
          properties from the build configuration. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="nonFilteredFileExtensions">
        <xs:annotation>
          <xs:documentation source="version">2.1.0+</xs:documentation>
          <xs:documentation source="description">
             Additional file extensions to not apply filtering (Since 3.2.0)
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="nonFilteredFileExtension" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="ModuleSet">
    <xs:annotation>
      <xs:documentation source="version">1.0.0+</xs:documentation>
      <xs:documentation source="description">
        
        A moduleSet represent one or more project &amp;lt;module&amp;gt; present inside
        a project&apos;s pom.xml. This allows you to include sources or binaries
        belonging to a project&apos;s &amp;lt;modules&amp;gt;.

        &lt;p&gt;&lt;b&gt;NOTE:&lt;/b&gt; When using &amp;lt;moduleSets&amp;gt; from the command-line, it
        is required to pass first the package phase by doing: &quot;mvn package
        assembly:assembly&quot;. This bug/issue is scheduled to be addressed by Maven 2.1.&lt;/p&gt;
        
      </xs:documentation>
    </xs:annotation>
    <xs:all>
      <xs:element minOccurs="0" name="useAllReactorProjects" type="xs:boolean" default="false">
        <xs:annotation>
          <xs:documentation source="version">1.1.2+</xs:documentation>
          <xs:documentation source="description">
          If set to true, the plugin will include all projects in the current reactor for processing
          in this ModuleSet. These will be subject to include/exclude rules. (Since 2.2)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="includeSubModules" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
          If set to false, the plugin will exclude sub-modules from processing in this ModuleSet.
          Otherwise, it will process all sub-modules, each subject to include/exclude rules. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="includes">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            When &amp;lt;include&amp;gt; subelements are present, they define a set of
            project coordinates to include. If none is present, then
            &amp;lt;includes&amp;gt; represents all valid values.

            Artifact coordinates may be given in simple groupId:artifactId form,
            or they may be fully qualified in the form groupId:artifactId:type[:classifier]:version.
            Additionally, wildcards can be used, as in *:maven-*
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="include" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="excludes">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            When &amp;lt;exclude&amp;gt; subelements are present, they define a set of
            project artifact coordinates to exclude. If none is present, then
            &amp;lt;excludes&amp;gt; represents no exclusions.

            Artifact coordinates may be given in simple groupId:artifactId form,
            or they may be fully qualified in the form groupId:artifactId:type[:classifier]:version.
            Additionally, wildcards can be used, as in *:maven-*
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="exclude" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="sources" type="ModuleSources">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            When this is present, the plugin will include the source files of
            the included modules from this set in the resulting assembly.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="binaries" type="ModuleBinaries">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            When this is present, the plugin will include the binaries of the
            included modules from this set in the resulting assembly.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="ModuleSources">
    <xs:annotation>
      <xs:documentation source="version">1.0.0+</xs:documentation>
      <xs:documentation source="description">
        Contains configuration options for including the source files of a
        project module in an assembly.
      </xs:documentation>
    </xs:annotation>
    <xs:all>
      <xs:element minOccurs="0" name="useDefaultExcludes" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            Whether standard exclusion patterns, such as those matching CVS and Subversion
            metadata files, should be used when calculating the files affected by this set.
            For backward compatibility, the default value is true. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="outputDirectory" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the output directory relative to the root
            of the root directory of the assembly. For example,
            &quot;log&quot; will put the specified files in the log directory.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="includes">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            When &amp;lt;include&amp;gt; subelements are present, they define a set of
            files and directory to include. If none is present, then
            &amp;lt;includes&amp;gt; represents all valid values.
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="include" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="excludes">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            When &amp;lt;exclude&amp;gt; subelements are present, they define a set of
            files and directory to exclude. If none is present, then
            &amp;lt;excludes&amp;gt; represents no exclusions.
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="exclude" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="fileMode" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Similar to a UNIX permission, sets the file mode of the files included.
            THIS IS AN OCTAL VALUE.
            Format: (User)(Group)(Other) where each component is a sum of Read = 4,
            Write = 2, and Execute = 1.  For example, the value 0644
            translates to User read-write, Group and Other read-only. The default value is 0644
            &lt;a href=&quot;http://www.onlamp.com/pub/a/bsd/2000/09/06/FreeBSD_Basics.html&quot;&gt;(more on unix-style permissions)&lt;/a&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="directoryMode" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Similar to a UNIX permission, sets the directory mode of the directories
            included.
            THIS IS AN OCTAL VALUE.
            Format: (User)(Group)(Other) where each component is a sum of
            Read = 4, Write = 2, and Execute = 1.  For example, the value
            0755 translates to User read-write, Group and Other read-only. The default value is 0755.
            &lt;a href=&quot;http://www.onlamp.com/pub/a/bsd/2000/09/06/FreeBSD_Basics.html&quot;&gt;(more on unix-style permissions)&lt;/a&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="fileSets">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            
            Specifies which groups of files from each included module to include in the assembly. A
            fileSet is specified by providing one or more of &amp;lt;fileSet&amp;gt; subelements. (Since 2.2-beta-1)
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="fileSet" minOccurs="0" maxOccurs="unbounded" type="FileSet"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="includeModuleDirectory" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            
            Specifies whether the module&apos;s finalName should be prepended to the outputDirectory
            values of any fileSets applied to it. (Since 2.2-beta-1)
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="excludeSubModuleDirectories" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            
            Specifies whether sub-module directories below the current module should be excluded
            from fileSets applied to that module. This might be useful if you only mean to copy
            the sources for the exact module list matched by this ModuleSet, ignoring (or processing
            separately) the modules which exist in directories below the current one. (Since 2.2-beta-1)
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="outputDirectoryMapping" type="xs:string" default="${module.artifactId}">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the mapping pattern for all module base-directories included in this assembly.
            NOTE: This field is only used if includeModuleDirectory == true.
            Default is the module&apos;s ${artifactId} in 2.2-beta-1, and ${module.artifactId} in subsequent versions. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="ModuleBinaries">
    <xs:annotation>
      <xs:documentation source="version">1.0.0+</xs:documentation>
      <xs:documentation source="description">
        Contains configuration options for including the binary files of a
        project module in an assembly.
      </xs:documentation>
    </xs:annotation>
    <xs:all>
      <xs:element minOccurs="0" name="outputDirectory" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the output directory relative to the root
            of the root directory of the assembly. For example,
            &quot;log&quot; will put the specified files in the log directory,
            directly beneath the root of the archive.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="includes">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            When &amp;lt;include&amp;gt; subelements are present, they define a set of
            artifact coordinates to include. If none is present, then
            &amp;lt;includes&amp;gt; represents all valid values.

            Artifact coordinates may be given in simple groupId:artifactId form,
            or they may be fully qualified in the form groupId:artifactId:type[:classifier]:version.
            Additionally, wildcards can be used, as in *:maven-*
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="include" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="excludes">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            When &amp;lt;exclude&amp;gt; subelements are present, they define a set of
            dependency artifact coordinates to exclude. If none is present, then
            &amp;lt;excludes&amp;gt; represents no exclusions.

            Artifact coordinates may be given in simple groupId:artifactId form,
            or they may be fully qualified in the form groupId:artifactId:type[:classifier]:version.
            Additionally, wildcards can be used, as in *:maven-*
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="exclude" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="fileMode" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Similar to a UNIX permission, sets the file mode of the files included.
            THIS IS AN OCTAL VALUE.
            Format: (User)(Group)(Other) where each component is a sum of Read = 4,
            Write = 2, and Execute = 1.  For example, the value 0644
            translates to User read-write, Group and Other read-only. The default value is 0644
            &lt;a href=&quot;http://www.onlamp.com/pub/a/bsd/2000/09/06/FreeBSD_Basics.html&quot;&gt;(more on unix-style permissions)&lt;/a&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="directoryMode" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Similar to a UNIX permission, sets the directory mode of the directories
            included.
            THIS IS AN OCTAL VALUE.
            Format: (User)(Group)(Other) where each component is a sum of
            Read = 4, Write = 2, and Execute = 1.  For example, the value
            0755 translates to User read-write, Group and Other read-only. The default value is 0755.
            &lt;a href=&quot;http://www.onlamp.com/pub/a/bsd/2000/09/06/FreeBSD_Basics.html&quot;&gt;(more on unix-style permissions)&lt;/a&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="attachmentClassifier" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
          When specified, the attachmentClassifier will cause the assembler to look at artifacts
          attached to the module instead of the main project artifact. If it can find an attached
          artifact matching the specified classifier, it will use it; otherwise, it will throw an
          exception. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="includeDependencies" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
          If set to true, the plugin will include the direct and transitive dependencies of
          of the project modules included here.  Otherwise, it will only include the module
          packages only.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="dependencySets">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            
            Specifies which dependencies of the module to include in the assembly. A
            dependencySet is specified by providing one or more of
            &amp;lt;dependencySet&amp;gt; subelements. (Since 2.2-beta-1)
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="dependencySet" minOccurs="0" maxOccurs="unbounded" type="DependencySet"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="unpack" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            If set to true, this property will unpack all module packages
            into the specified output directory. When set to false
            module packages will be included as archives (jars).
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="unpackOptions" type="UnpackOptions">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
          Allows the specification of includes and excludes, along with filtering options, for items
          unpacked from a module artifact. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="outputFileNameMapping" type="xs:string" default="${module.artifactId}-${module.version}${dashClassifier?}.${module.extension}">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the mapping pattern for all NON-UNPACKED dependencies included
            in this assembly.
            (Since 2.2-beta-2; 2.2-beta-1 uses ${artifactId}-${version}${dashClassifier?}.${extension} as default value)
            NOTE: If the dependencySet specifies unpack == true, outputFileNameMapping WILL NOT BE USED; in these cases,
            use outputDirectory.
            See the plugin FAQ for more details about entries usable in the outputFileNameMapping parameter.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="UnpackOptions">
    <xs:annotation>
      <xs:documentation source="version">1.1.0+</xs:documentation>
      <xs:documentation source="description">
      Specifies options for including/excluding/filtering items extracted from an archive. (Since 2.2-beta-1)
      </xs:documentation>
    </xs:annotation>
    <xs:all>
      <xs:element minOccurs="0" name="includes">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            
            Set of file and/or directory patterns for matching items to be included from an archive as it is unpacked.
            Each item is specified as &amp;lt;include&amp;gt;some/path&amp;lt;/include&amp;gt; (Since 2.2-beta-1)
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="include" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="excludes">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
            
            Set of file and/or directory patterns for matching items to be excluded from an archive as it is unpacked.
            Each item is specified as &amp;lt;exclude&amp;gt;some/path&amp;lt;/exclude&amp;gt; (Since 2.2-beta-1)
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="exclude" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="filtered" type="xs:boolean" default="false">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
          Whether to filter symbols in the files as they are unpacked from the archive, using
          properties from the build configuration. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="nonFilteredFileExtensions">
        <xs:annotation>
          <xs:documentation source="version">2.1.0+</xs:documentation>
          <xs:documentation source="description">
             Additional file extensions to not apply filtering (Since 3.2.0)
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="nonFilteredFileExtension" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="lineEnding" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.1.2+</xs:documentation>
          <xs:documentation source="description">
            
            Sets the line-endings of the files. (Since 2.2)
            Valid values:
            &lt;ul&gt;
              &lt;li&gt;&lt;b&gt;&quot;keep&quot;&lt;/b&gt; - Preserve all line endings&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;unix&quot;&lt;/b&gt; - Use Unix-style line endings&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;lf&quot;&lt;/b&gt; - Use a single line-feed line endings&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;dos&quot;&lt;/b&gt; - Use DOS-style line endings&lt;/li&gt;
              &lt;li&gt;&lt;b&gt;&quot;crlf&quot;&lt;/b&gt; - Use Carraige-return, line-feed line endings&lt;/li&gt;
            &lt;/ul&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="useDefaultExcludes" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">1.1.2+</xs:documentation>
          <xs:documentation source="description">
            Whether standard exclusion patterns, such as those matching CVS and Subversion
            metadata files, should be used when calculating the files affected by this set.
            For backward compatibility, the default value is true. (Since 2.2)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="encoding" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.1.3+</xs:documentation>
          <xs:documentation source="description">
            
            Allows to specify the encoding to use when unpacking archives, for unarchivers
            that support specifying encoding. If unspecified, archiver default will be used.
            Archiver defaults generally represent sane (modern) values.
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:all>
  </xs:complexType>
  <xs:complexType name="DependencySet">
    <xs:annotation>
      <xs:documentation source="version">1.0.0+</xs:documentation>
      <xs:documentation source="description">
        A dependencySet allows inclusion and exclusion of project dependencies
        in the assembly.
      </xs:documentation>
    </xs:annotation>
    <xs:all>
      <xs:element minOccurs="0" name="outputDirectory" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the output directory relative to the root
            of the root directory of the assembly. For example,
            &quot;log&quot; will put the specified files in the log directory,
            directly beneath the root of the archive.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="includes">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            When &amp;lt;include&amp;gt; subelements are present, they define a set of
            artifact coordinates to include. If none is present, then
            &amp;lt;includes&amp;gt; represents all valid values.

            Artifact coordinates may be given in simple groupId:artifactId form,
            or they may be fully qualified in the form groupId:artifactId:type[:classifier]:version.
            Additionally, wildcards can be used, as in *:maven-*
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="include" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="excludes">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            When &amp;lt;exclude&amp;gt; subelements are present, they define a set of
            dependency artifact coordinates to exclude. If none is present, then
            &amp;lt;excludes&amp;gt; represents no exclusions.

            Artifact coordinates may be given in simple groupId:artifactId form,
            or they may be fully qualified in the form groupId:artifactId:type[:classifier]:version.
            Additionally, wildcards can be used, as in *:maven-*
            
          </xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:sequence>
            <xs:element name="exclude" minOccurs="0" maxOccurs="unbounded" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element minOccurs="0" name="fileMode" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Similar to a UNIX permission, sets the file mode of the files included.
            THIS IS AN OCTAL VALUE.
            Format: (User)(Group)(Other) where each component is a sum of Read = 4,
            Write = 2, and Execute = 1.  For example, the value 0644
            translates to User read-write, Group and Other read-only.  The default value is 0644
            &lt;a href=&quot;http://www.onlamp.com/pub/a/bsd/2000/09/06/FreeBSD_Basics.html&quot;&gt;(more on unix-style permissions)&lt;/a&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="directoryMode" type="xs:string">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            
            Similar to a UNIX permission, sets the directory mode of the directories
            included.
            THIS IS AN OCTAL VALUE.
            Format: (User)(Group)(Other) where each component is a sum of
            Read = 4, Write = 2, and Execute = 1.  For example, the value
            0755 translates to User read-write, Group and Other read-only. The default value is 0755.
            &lt;a href=&quot;http://www.onlamp.com/pub/a/bsd/2000/09/06/FreeBSD_Basics.html&quot;&gt;(more on unix-style permissions)&lt;/a&gt;
            
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="useStrictFiltering" type="xs:boolean" default="false">
        <xs:annotation>
          <xs:documentation source="version">1.1.2+</xs:documentation>
          <xs:documentation source="description">
          When specified as true, any include/exclude patterns which aren&apos;t used to filter an actual
          artifact during assembly creation will cause the build to fail with an error. This is meant
          to highlight obsolete inclusions or exclusions, or else signal that the assembly descriptor
          is incorrectly configured. (Since 2.2)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="outputFileNameMapping" type="xs:string" default="${artifact.artifactId}-${artifact.version}${dashClassifier?}.${artifact.extension}">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the mapping pattern for all dependencies included in this
            assembly. (Since 2.2-beta-2; 2.2-beta-1 uses ${artifactId}-${version}${dashClassifier?}.${extension}
            as default value).
            See the plugin FAQ for more details about entries usable in the outputFileNameMapping parameter.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="unpack" type="xs:boolean" default="false">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            If set to true, this property will unpack all dependencies
            into the specified output directory. When set to false
            dependencies will be includes as archives (jars). Can only unpack
            jar, zip, tar.gz, and tar.bz archives.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="unpackOptions" type="UnpackOptions">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
          Allows the specification of includes and excludes, along with filtering options, for items
          unpacked from a dependency artifact. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="scope" type="xs:string" default="runtime">
        <xs:annotation>
          <xs:documentation source="version">1.0.0+</xs:documentation>
          <xs:documentation source="description">
            Sets the dependency scope for this dependencySet.
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="useProjectArtifact" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
          Determines whether the artifact produced during the current project&apos;s
          build should be included in this dependency set. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="useProjectAttachments" type="xs:boolean" default="false">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
          Determines whether the attached artifacts produced during the current project&apos;s
          build should be included in this dependency set. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="useTransitiveDependencies" type="xs:boolean" default="true">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
          Determines whether transitive dependencies will be included in the processing of
          the current dependency set. If true, includes/excludes/useTransitiveFiltering
          will apply to transitive dependency artifacts in addition to the main project
          dependency artifacts. If false, useTransitiveFiltering is meaningless, and
          includes/excludes only affect the immediate dependencies of the project.
          By default, this value is true. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element minOccurs="0" name="useTransitiveFiltering" type="xs:boolean" default="false">
        <xs:annotation>
          <xs:documentation source="version">1.1.0+</xs:documentation>
          <xs:documentation source="description">
          Determines whether the include/exclude patterns in this dependency set will be applied to
          the transitive path of a given artifact. If true, and the current artifact is a transitive
          dependency brought in by another artifact which matches an inclusion or exclusion pattern,
          then the current artifact has the same inclusion/exclusion logic applied to it as well. By
          default, this value is false, in order to preserve backward compatibility with version 2.1.
          This means that includes/excludes only apply directly to the current artifact, and not to
          the transitive set of artifacts which brought it in. (Since 2.2-beta-1)
          </xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:all>
  </xs:complexType>
</xs:schema>