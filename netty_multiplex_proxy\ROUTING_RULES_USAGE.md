## 路由规则使用文档

### 概述
- **目标**: 通过可配置的路由规则，将请求按条件分发到不同的 `outbound` 处理器（如 `tcpDirect`, `udpDirect` 等）。
- **位置**: 路由规则统一放在服务器 YAML 配置的 `routing` 节点下，支持多条规则与优先级排序。
- **匹配逻辑**: 规则按优先级从小到大依次匹配；单条规则内的多个匹配器为 AND 关系（全部满足才命中）。命中首条规则后即停止匹配。

### 配置结构
```yaml
routing:
  default_outbound: "tcpDirect"       # 可选，未命中时的默认 outbound（建议同时配置兜底规则）
  rules:
    - id: "tcp-direct"
      name: "tcp直连"
      priority: 10                     # 数字越小优先级越高，默认 100
      outbound: "tcpDirect"           # 命中后使用的 outbound ID
      enabled: true                    # 可选，默认 true
      description: "仅匹配TCP请求"      # 可选
      # config: {}                     # 可选，附加属性（将随 RouteResult 返回）
      matchers:                        # 匹配器列表（AND 关系）
        - type: "protocol"
          operator: "regex"
          value: "^tcp$"
          caseSensitive: false         # 可选，默认 true

    - id: "udp-direct"
      name: "udp直连"
      priority: 10
      outbound: "udpDirect"
      matchers:
        - type: "protocol"
          operator: "regex"
          value: "^udp$"
          caseSensitive: false

    # 兜底示例：最后一条规则全量匹配，避免未命中
    - id: "catch-all"
      name: "默认直连"
      priority: 9999
      outbound: "tcpDirect"
      matchers: []                     # 空表示匹配所有
```

### 规则字段说明
- **id**: 规则唯一 ID（必填）。
- **name**: 规则名称（建议填写）。
- **priority**: 优先级，数值越小越优先（默认 100）。
- **outbound**: 命中时选择的 `outbound` 组件 ID（必填）。
- **enabled**: 是否启用，默认 true。
- **description**: 规则说明，便于维护。
- **config**: 可选的键值对，作为路由结果的 `attributes` 返回（便于后续扩展）。
- **matchers**: 匹配器数组，全部满足才命中。留空表示“匹配所有”。

### 匹配器说明
- 通用字段：
  - **type**: 匹配维度。
  - **operator**: 匹配操作符。
  - **value**: 目标值（字符串形式）。
  - **caseSensitive**: 是否区分大小写（默认 true）。

- 可用 `type`：
  - `host`: 目标主机名（如 `example.com`）
  - `port`: 目标端口（如 `443`）
  - `protocol`: 协议（`tcp`/`udp`/`multiplex`）
  - `client_ip`: 客户端 IP（从连接的远端地址提取）
  - `client_id`: 客户端连接 ID（短文本）
  - `session_id`: 会话 ID（整数）
  - `target`: 目标 `host:port`
  - `time`: 当前毫秒时间戳（用于范围/比较）
  - `attribute.<key>`: 从请求属性读取自定义键（如 `attribute.geo`）

- 可用 `operator`：
  - 相等/不等：`equals`, `not_equals`
  - 包含/不包含：`contains`, `not_contains`
  - 前缀/后缀：`starts_with`, `ends_with`
  - 正则匹配：`regex`, `not_regex`
  - 集合包含：`in`, `not_in`（逗号分隔，如 `80,443,8080`）
  - 数值范围：`range`（`min-max`，闭区间，如 `1000-2000`）
  - 比较：`gt`, `gte`, `lt`, `lte`（优先按数值比较，失败则按字符串比较）

### 常见示例
- 按域名后缀直连
```yaml
- id: "local-domain"
  name: "本地域名"
  priority: 20
  outbound: "tcpDirect"
  matchers:
    - type: "host"
      operator: "ends_with"
      value: ".local"
      caseSensitive: false
```

- 指定端口列表
```yaml
- id: "https-and-alt"
  name: "HTTPS与常见备用端口"
  priority: 30
  outbound: "tcpDirect"
  matchers:
    - type: "port"
      operator: "in"
      value: "443,8443,9443"
```

- 内网 IP 直连（IPv4 正则）
```yaml
- id: "internal-direct"
  name: "内网直连"
  priority: 10
  outbound: "tcpDirect"
  matchers:
    - type: "host"
      operator: "regex"
      value: "^(192\\.168\\.|10\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"
      caseSensitive: false
```

- 基于客户端 IP 路由
```yaml
- id: "office-subnet"
  name: "办公网段定向"
  priority: 15
  outbound: "tcpDirect"
  matchers:
    - type: "client_ip"
      operator: "regex"
      value: "^10\\.0\\.\\d+\\.\\d+$"
```

- 使用自定义属性（例如在 `ProxyRequest` 上设置了 `geo=CN`）
```yaml
- id: "geo-cn"
  name: "中国区路由"
  priority: 25
  outbound: "tcpDirect"
  matchers:
    - type: "attribute.geo"
      operator: "equals"
      value: "CN"
```

### 动态管理（编程方式）
```java
// 添加规则
RouteRule rule = new RouteRule("api-route", "API路由", 15, "tcpDirect");
rule.addMatcher(new RouteMatcher(RouteMatcher.Type.HOST, RouteMatcher.Operator.STARTS_WITH, "api."));
router.addRoute(rule);

// 启用/禁用
router.setRouteEnabled("api-route", false);

// 更新规则（同 ID 覆盖）
router.updateRoute(rule);

// 验证规则
ValidationResult vr = router.validateRule(rule);
if (!vr.isValid()) {
    logger.warn("路由规则无效: {}", vr.getMessage());
}

// 统计信息
RouteStatistics stats = router.getStatistics();
logger.info("路由统计: total={}, matched={}, unmatched={}, rules={}",
        stats.getTotalRoutes(), stats.getMatchedRoutes(), stats.getUnmatchedRoutes(), stats.getRuleCount());
```

### 优先级与兜底
- 建议永远提供一条“兜底规则”（优先级尽量大，如 `9999`），以防规则未命中导致请求失败。
- 如果使用 `routing.default_outbound` 作为全局默认，仍推荐保留兜底规则以明确行为。

### 故障排查
- **现象：日志提示“请求没有匹配的路由规则”**
  - 检查是否存在兜底规则或 `default_outbound`。
  - 提升路由日志级别到 DEBUG，核对每条规则的匹配结果。
- **匹配不生效**
  - 确认 `caseSensitive` 设置是否合适（域名/协议一般建议 `false`）。
  - `in` 操作符需逗号分隔；`range` 必须是 `min-max`；正则需转义特殊字符（在 YAML 中注意 `\\`）。
  - 规则优先级可能被更高优先级规则“截获”。
- **正则异常**
  - 路由器会在验证阶段编译正则；若无效会返回 `Invalid regex pattern` 的验证错误。

### 最佳实践
- **小而清晰的规则**：按业务维度拆分，减少复杂正则。
- **显式大小写**：对 `host`、`protocol` 推荐设置 `caseSensitive: false`。
- **顺序管理**：优先级低值先匹配；将高命中率/明确的规则放前面。
- **提供兜底**：最后一条规则匹配所有，保证稳定性。

### 参考示例
- 见仓库示例配置：`configs/development/server/proxy-server-v2.yml` 下的 `routing.rules`（包含 TCP/UDP 分流示例）。

### 备注
- 路由命中后，系统会将 `ruleId` 与 `outboundId` 写入请求属性；`RouteRule.config` 会出现在 `RouteResult.attributes` 中（便于后续扩展）。
- 当前实现中，若没有任何规则命中且未提供兜底，路由将返回未命中，导致请求失败。务必配置兜底策略。 