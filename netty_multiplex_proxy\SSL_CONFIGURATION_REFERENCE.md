# SSL/TLS 配置参考手册

本文档详细说明了proxy-server和proxy-client的SSL/TLS配置选项。

## 📋 服务器端配置 (proxy-server.yml)

### 基础SSL配置

```yaml
ssl:
  enable: true                    # 是否启用SSL/TLS
  key-store-path: "server.p12"    # 服务器证书路径
  key-store-password: "xiang1"    # 证书密码
  key-store-type: "PKCS12"        # 证书格式
```

### 完整SSL配置

```yaml
ssl:
  enable: true
  key-store-path: "server.p12"
  key-store-password: "xiang1"
  key-store-type: "PKCS12"
  trust-store-path: "truststore.p12"      # 信任库（双向认证）
  trust-store-password: "xiang1"
  trust-store-type: "PKCS12"
  protocols:                              # 支持的协议版本
    - "TLSv1.2"
    - "TLSv1.3"
  cipher-suites: []                       # 密码套件（空=默认）
  client-auth: false                      # 是否启用客户端认证
  need-client-auth: false                 # 强制客户端认证
  want-client-auth: false                 # 可选客户端认证
  handshake-timeout-seconds: 30           # 握手超时时间
```

### 服务器配置选项说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enable` | boolean | false | 是否启用SSL/TLS |
| `key-store-path` | string | "server.p12" | 服务器证书文件路径 |
| `key-store-password` | string | "changeit" | 证书密码 |
| `key-store-type` | string | "PKCS12" | 证书格式 (PKCS12/JKS) |
| `trust-store-path` | string | "" | 信任库路径（双向认证） |
| `trust-store-password` | string | "changeit" | 信任库密码 |
| `protocols` | array | ["TLSv1.2", "TLSv1.3"] | 支持的协议版本 |
| `cipher-suites` | array | [] | 密码套件列表 |
| `client-auth` | boolean | false | 是否启用客户端认证 |
| `need-client-auth` | boolean | false | 强制客户端认证 |
| `want-client-auth` | boolean | false | 可选客户端认证 |
| `handshake-timeout-seconds` | int | 30 | SSL握手超时时间 |

## 📋 客户端配置 (proxy-client.yml)

### 基础SSL配置

```yaml
ssl:
  enable: true                    # 是否启用SSL/TLS
  trust-all: true                 # 信任所有证书（开发测试）
  verify-hostname: false          # 是否验证主机名
```

### 完整SSL配置

```yaml
ssl:
  enable: true
  trust-all: false                        # 是否信任所有证书
  trust-store-path: "truststore.p12"      # 信任库路径
  trust-store-password: "xiang1"
  trust-store-type: "PKCS12"
  key-store-path: "client.p12"            # 客户端证书（双向认证）
  key-store-password: "xiang1"
  key-store-type: "PKCS12"
  protocols:                              # 支持的协议版本
    - "TLSv1.2"
    - "TLSv1.3"
  cipher-suites: []                       # 密码套件
  verify-hostname: false                  # 主机名验证
  handshake-timeout-seconds: 30           # 握手超时时间
```

### 客户端配置选项说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enable` | boolean | false | 是否启用SSL/TLS |
| `trust-all` | boolean | false | 是否信任所有证书 |
| `trust-store-path` | string | "" | 信任库路径 |
| `trust-store-password` | string | "changeit" | 信任库密码 |
| `key-store-path` | string | "" | 客户端证书路径 |
| `key-store-password` | string | "changeit" | 客户端证书密码 |
| `protocols` | array | ["TLSv1.2", "TLSv1.3"] | 支持的协议版本 |
| `cipher-suites` | array | [] | 密码套件列表 |
| `verify-hostname` | boolean | true | 是否验证服务器主机名 |
| `handshake-timeout-seconds` | int | 30 | SSL握手超时时间 |

## 🔧 配置模式

### 开发测试模式

**服务器配置:**
```yaml
ssl:
  enable: true
  key-store-path: "server.p12"
  key-store-password: "xiang1"
  client-auth: false              # 单向认证
```

**客户端配置:**
```yaml
ssl:
  enable: true
  trust-all: true                 # 信任所有证书
  verify-hostname: false          # 禁用主机名验证
```

**特点:**
- 使用自签名证书
- 单向SSL认证
- 信任所有证书
- 禁用主机名验证
- 适用于localhost测试

### 生产环境模式

**服务器配置:**
```yaml
ssl:
  enable: true
  key-store-path: "/etc/ssl/server.p12"
  key-store-password: "${SSL_PASSWORD}"
  trust-store-path: "/etc/ssl/truststore.p12"
  client-auth: true               # 双向认证
  need-client-auth: true
  protocols: ["TLSv1.3"]         # 只使用最新协议
```

**客户端配置:**
```yaml
ssl:
  enable: true
  trust-all: false                # 严格证书验证
  trust-store-path: "/etc/ssl/truststore.p12"
  key-store-path: "/etc/ssl/client.p12"
  verify-hostname: true           # 启用主机名验证
  protocols: ["TLSv1.3"]
```

**特点:**
- 使用CA签名证书
- 双向SSL认证
- 严格证书验证
- 启用主机名验证
- 环境变量配置密码

## 🛡️ 安全配置建议

### 协议版本
```yaml
# 推荐配置
protocols:
  - "TLSv1.3"                    # 生产环境
  
# 兼容配置
protocols:
  - "TLSv1.2"
  - "TLSv1.3"                    # 开发环境
```

### 密码套件
```yaml
# 高安全级别
cipher-suites:
  - "TLS_AES_256_GCM_SHA384"
  - "TLS_CHACHA20_POLY1305_SHA256"
  
# 默认配置（推荐）
cipher-suites: []                # 使用JVM默认安全套件
```

### 证书验证
```yaml
# 生产环境（严格）
trust-all: false
verify-hostname: true

# 开发环境（宽松）
trust-all: true
verify-hostname: false
```

## 📁 证书文件结构

```
proxy-server/src/main/resources/
├── server.p12          # 服务器证书
├── truststore.p12      # 信任库
└── proxy-server.yml    # 配置文件

proxy-client/src/main/resources/
├── client.p12          # 客户端证书
├── truststore.p12      # 信任库
└── proxy-client.yml    # 配置文件
```

## 🔍 配置验证

### 检查SSL是否启用
```bash
# 查看服务器启动日志
grep "SSL配置" logs/proxy-server.log

# 查看客户端启动日志
grep "Client SSL" logs/proxy-client.log
```

### 测试SSL连接
```bash
# 使用OpenSSL测试服务器
openssl s_client -connect localhost:8888 -servername localhost

# 查看证书信息
keytool -list -v -keystore server.p12 -storetype PKCS12
```

## ⚠️ 常见配置错误

1. **证书路径错误**
   - 确保证书文件在classpath中
   - 使用相对路径或绝对路径

2. **密码不匹配**
   - 检查配置文件中的密码
   - 确保证书密码正确

3. **协议不兼容**
   - 确保客户端和服务器协议版本匹配
   - 检查JVM支持的协议版本

4. **主机名验证失败**
   - 开发环境设置 `verify-hostname: false`
   - 生产环境确保证书CN或SAN正确
