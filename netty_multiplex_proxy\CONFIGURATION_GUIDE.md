# 配置文件指南

本文档详细说明了代理系统的配置文件管理机制，包括外部配置文件的使用方法和最新的配置结构。

## 📋 配置文件架构

代理系统采用分层配置架构：

```
配置优先级（从高到低）：
1. 命令行参数
2. 系统属性 (-D参数)
3. 环境变量
4. YAML配置文件
5. Properties配置文件
6. 默认配置
```

## 🔧 配置文件类型

### 1. YAML配置文件（推荐）
现代化的配置格式，支持复杂的嵌套结构和类型安全。

**proxy-client配置示例**：
```yaml
# 代理服务器配置
proxy:
  server:
    host: "localhost"
    port: 8888

# 地址过滤配置
filter:
  mode: "CHINA_DIRECT"  # ALL_PROXY, CHINA_DIRECT, ALL_DIRECT

# 接入器配置 - 支持多个同类型接入器
inbound:
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
      description: "主要SOCKS5代理"
  http:
    - name: "http-main"
      port: 1082
      enabled: true
      description: "主要HTTP代理"

# 认证配置
auth:
  enable: false
  username: "admin"
  password: "password"

# SSL配置
ssl:
  enable: false
  trust-all: false
  trust-store-path: "truststore.p12"
  trust-store-password: "password"
```

### 2. Properties配置文件（兼容性）
位置：`src/main/resources/application.properties`

**proxy-client配置**：
```properties
# 配置文件目录路径 - 自动从此目录加载配置文件
config.ext.dir=configs/development/client/

# 基础配置（向后兼容）
filter.mode=CHINA_DIRECT
proxy.server.host=localhost
proxy.server.port=8888
local.port=1080
```

**配置文件自动发现规则**：
- **proxy-client**: 按优先级查找 `proxy-client.yml` > `proxy-client-dev-*.yml` > 包含`proxy-client`的其他yml文件 > 目录中第一个yml文件

### 3. 配置属性绑定
系统使用自定义的配置绑定机制，类似Spring Boot的@ConfigurationProperties：

```java
@ConfigurationProperties
public class ProxyClientProperties {
    private FilterProperties filter = new FilterProperties();
    private ProxyProperties proxy = new ProxyProperties();
    private InboundProperties inbound = new InboundProperties();
    private AuthProperties auth = new AuthProperties();
    private SslProperties ssl = new SslProperties();
}
```

## 📁 配置文件路径指定

### 方式1：配置目录指定（推荐）
在`application.properties`中指定配置文件目录，系统会自动发现并加载合适的配置文件：
```properties
# proxy-client配置
config.ext.dir=configs/development/client/

# proxy-server配置
config.ext.dir=configs/development/server/
```

### 方式2：命令行参数
```bash
# proxy-client启动
java -jar proxy-client.jar --config=configs/production/proxy-client.yml
java -jar proxy-client.jar -c configs/production/proxy-client.yml

# 多组件模式启动
java -jar proxy-client.jar both 1080 --config=configs/production/proxy-client.yml

# Maven运行
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="--config=configs/production/proxy-client.yml"
```

### 方式3：系统属性
```bash
# proxy-client
java -Dproxy.client.config=configs/production/proxy-client.yml -jar proxy-client.jar

# Native Image
./proxy-client -Dproxy.client.config=configs/production/proxy-client.yml
```

### 方式4：环境变量
```bash
# Windows
set PROXY_CLIENT_CONFIG=configs/production/proxy-client.yml

# Linux/macOS
export PROXY_CLIENT_CONFIG=configs/production/proxy-client.yml
```

## 🌐 多组件配置详解

### 接入器配置结构
```yaml
inbound:
  # SOCKS5接入器列表
  socks5:
    - name: "socks5-main"        # 接入器名称
      port: 1081                 # 监听端口
      enabled: true              # 是否启用
      description: "主要SOCKS5代理"  # 描述信息
    - name: "socks5-backup"
      port: 1083
      enabled: false
      description: "备用SOCKS5代理"

  # HTTP接入器列表
  http:
    - name: "http-main"
      port: 1082
      enabled: true
      description: "主要HTTP代理"
    - name: "http-backup"
      port: 1085
      enabled: false
      description: "备用HTTP代理"
```

### 兼容性配置（旧版本）
```yaml
# 旧版本配置格式（仍然支持）
legacy:
  local-port: 1080             # 本地监听端口
  enabled-protocols: "socks5"  # 启用的协议
```

## 🗂️ 配置文件组织

### 推荐目录结构
```
netty_proxy_Multiplex/
├── configs/
│   ├── development/
│   │   ├── server/
│   │   │   ├── proxy-server.yml      # 开发环境服务器配置
│   │   │   ├── china-ip-ranges.txt   # 中国IP段数据
│   │   │   ├── malicious-domains.txt # 恶意域名黑名单
│   │   │   └── whitelist-domains.txt # 白名单域名
│   │   └── client/
│   │       ├── proxy-client.yml      # 开发环境客户端配置
│   │       └── china-ip-ranges.txt   # 中国IP段数据
│   └── production/
│       ├── server/
│       │   └── proxy-server.yml      # 生产环境服务器配置
│       └── client/
│           └── proxy-client.yml      # 生产环境客户端配置
├── proxy-server.yml                  # 默认服务器配置（与程序同级）
├── proxy-client.yml                  # 默认客户端配置（与程序同级）
├── proxy-server/
└── proxy-client/
```

### 配置文件路径解析规则

1. **相对路径**：相对于程序执行目录
   ```properties
   config.ext.dir=configs/development/client/           # 相对子目录
   config.file.path=proxy-client.yml                    # 程序同级目录
   ```

2. **绝对路径**：完整的文件系统路径
   ```properties
   config.ext.dir=/opt/proxy/config/client/             # Linux
   config.ext.dir=C:\proxy\config\client\               # Windows
   ```

## 🔧 完整配置示例

### proxy-client.yml 完整配置
```yaml
# 代理服务器配置
proxy:
  server:
    host: "localhost"
    port: 8888

# 地址过滤配置
filter:
  mode: "CHINA_DIRECT"  # ALL_PROXY, CHINA_DIRECT, ALL_DIRECT

# 本地配置（兼容性）
local:
  port: 1080

# 接入器配置
inbound:
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
      description: "主要SOCKS5代理"
    - name: "socks5-backup"
      port: 1083
      enabled: false
      description: "备用SOCKS5代理"
  http:
    - name: "http-main"
      port: 1082
      enabled: true
      description: "主要HTTP代理"

# 认证配置
auth:
  enable: false
  username: "admin"
  password: "secure_password"
  timeout-seconds: 30

# SSL/TLS配置
ssl:
  enable: false
  trust-all: false
  trust-store-path: "truststore.p12"
  trust-store-password: "password"
  trust-store-type: "PKCS12"
  key-store-path: "client.p12"
  key-store-password: "password"
  key-store-type: "PKCS12"
  protocols:
    - "TLSv1.2"
    - "TLSv1.3"
  cipher-suites: []
  verify-hostname: true
  handshake-timeout-seconds: 30

# 在线数据源配置
online-data-sources:
  china-ip-ranges:
    - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
    - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"

# 兼容性配置
legacy:
  local-port: 1080
  enabled-protocols: "socks5,http"
```

## 🚀 使用示例

### 开发环境启动
```bash
# 使用默认开发配置
cd netty_proxy_Multiplex
mvn -f proxy-server/pom.xml exec:java -Dexec.mainClass="com.proxy.server.ProxyServer"
mvn -f proxy-client/pom.xml exec:java -Dexec.mainClass="com.proxy.client.ProxyClient"
```

### 生产环境启动
```bash
# 方式1：修改properties文件
# 编辑 proxy-server/src/main/resources/app.properties
# config.file.path=configs/production/proxy-server.yml

# 方式2：使用命令行参数
mvn -f proxy-server/pom.xml exec:java -Dexec.mainClass="com.proxy.server.ProxyServer" -Dexec.args="--config=configs/production/proxy-server.yml"

# 方式3：使用系统属性
mvn -f proxy-server/pom.xml exec:java -Dexec.mainClass="com.proxy.server.ProxyServer" -Dproxy.server.config=configs/production/proxy-server.yml
```

### 自定义配置文件
```bash
# 创建自定义配置文件
cp configs/development/proxy-server.yml my-custom-config.yml

# 使用自定义配置启动
mvn -f proxy-server/pom.xml exec:java -Dexec.mainClass="com.proxy.server.ProxyServer" -Dexec.args="--config=my-custom-config.yml"
```

## 🔍 配置加载日志

系统会输出详细的配置加载日志，帮助调试配置问题：

```
INFO  - 使用properties文件指定的配置文件: configs/development/proxy-server.yml
INFO  - 成功加载外部 YAML 配置文件: /path/to/configs/development/proxy-server.yml
INFO  - 配置加载完成 - 服务器端口: 8888, 认证: 启用, 连接池: 启用, 性能监控: 启用, 黑名单: 启用
```

## ⚠️ 注意事项

1. **文件路径**：确保配置文件路径正确，支持相对路径和绝对路径
2. **文件权限**：确保程序有读取配置文件的权限
3. **配置优先级**：命令行参数优先级最高，会覆盖其他配置方式
4. **路径分隔符**：Windows使用`\`或`/`，Linux/macOS使用`/`
5. **配置验证**：启动时会验证配置文件格式，错误时会使用默认配置

## 🌍 地理位置过滤配置

地理位置过滤功能可以根据IP地址的地理位置和域名特征来控制访问。

### 配置项说明

```yaml
geo-location-filter:
  # 启用地理位置过滤
  enable: true

  # 严格阻止海外可疑网站
  block-overseas-suspicious: true

  # 启用域名过滤
  enable-domain-filter: true

  # 启用关键词过滤
  enable-keyword-filter: true

  # 启用白名单（允许合法海外网站）
  enable-whitelist: true

  # DNS缓存超时时间（分钟）
  dns-cache-timeout-minutes: 10

  # IP地理位置判断缓存超时时间（分钟）
  ip-cache-timeout-minutes: 120

  # 最大缓存条目数
  max-cache-size: 50000

  # 自动更新IP段数据
  auto-update-ip-ranges: true

  # IP段数据更新间隔（小时）
  update-interval-hours: 24
```

### 功能说明

1. **总开关** (`enable`): 控制整个地理位置过滤功能的开启/关闭
2. **海外可疑网站阻止** (`block-overseas-suspicious`): 阻止访问海外的可疑网站
3. **域名过滤** (`enable-domain-filter`): 基于恶意域名黑名单进行过滤
4. **关键词过滤** (`enable-keyword-filter`): 基于域名中的恶意关键词进行过滤
5. **白名单** (`enable-whitelist`): 允许访问白名单中的合法海外网站
6. **缓存配置**: 控制DNS和IP地理位置判断的缓存行为
7. **自动更新**: 定期从在线数据源更新IP段数据

## 🛡️ 恶意内容过滤配置

系统支持多种恶意内容过滤机制，所有配置文件都从 `config.ext.dir` 目录加载。

### 配置文件列表

#### 1. 恶意域名黑名单
**文件**: `malicious-domains.txt`
```
# 恶意域名配置文件
# 每行一个域名，支持注释
example-malicious.com
phishing-site.net
malware-host.org
```

#### 2. 恶意关键词列表
**文件**: `malicious-keywords.txt`
```
# 恶意关键词配置文件
# 用于检测域名中的可疑关键词
porn
gambling
malware
phishing
```

#### 3. 白名单域名
**文件**: `whitelist-domains.txt`
```
# 白名单域名配置文件 - 中国境外合法网站
# 总计: 160 个合法域名

# 搜索引擎
google.com
bing.com
duckduckgo.com

# 教育机构
mit.edu
stanford.edu
harvard.edu
```

#### 4. 中国IP段数据
**文件**: `china-ip-ranges.txt`
```
# 中国IP段数据
# 用于地理位置判断
*******/8
********/8
********/8
```

### 在线数据源配置

系统支持从配置文件中指定在线数据源URL，实现灵活的数据源管理：

#### 服务器端在线数据源配置
```yaml
geo-location-filter:
  online-data-sources:
    # 恶意域名数据源
    malicious-domains:
      - "https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts"
      - "https://someonewhocares.org/hosts/zero/hosts"
      - "https://raw.githubusercontent.com/AdguardTeam/AdguardFilters/master/BaseFilter/sections/adservers.txt"

    # 恶意关键词数据源
    malicious-keywords:
      - "https://raw.githubusercontent.com/crazy-max/WindowsSpyBlocker/master/data/hosts/spy.txt"

    # 中国IP段数据源
    china-ip-ranges:
      - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
      - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
```

#### 客户端在线数据源配置
```yaml
online-data-sources:
  # 中国IP段数据源
  china-ip-ranges:
    - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
    - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
```

#### 数据源特点

1. **恶意域名数据源**:
   - 自动从多个在线威胁情报源获取最新的恶意域名列表
   - 支持hosts文件格式和纯域名列表格式
   - 可配置多个备用数据源

2. **恶意关键词数据源**:
   - 从恶意域名中自动提取可疑关键词
   - 智能过滤和验证关键词的有效性
   - 支持自定义关键词数据源

3. **中国IP段数据源**:
   - 定期从APNIC等权威机构获取最新的中国IP段数据
   - 支持多个备用数据源确保可用性
   - 客户端和服务器端都支持配置

#### 配置优势
- **灵活性**: 可以根据需要添加或更换数据源
- **可靠性**: 支持多个备用数据源，确保数据更新的可用性
- **可维护性**: 无需修改代码即可更新数据源URL
- **容错性**: 配置加载失败时自动使用内置默认数据源

### 启动时初始化

系统在启动时会自动初始化所有配置文件和组件：

1. **配置文件加载**: 优先从配置目录加载，失败时使用内置默认配置
2. **在线数据更新**: 异步执行在线数据源更新，不阻塞启动过程
3. **缓存预热**: 预加载常用的DNS解析和IP地理位置判断结果
4. **定时任务**: 启动定期更新和缓存清理任务

## 🔗 相关文档

- [README.md](README.md) - 项目主文档
- [SSL_CONFIGURATION_REFERENCE.md](SSL_CONFIGURATION_REFERENCE.md) - SSL配置参考
- [README-BASIC-AUTH.md](README-BASIC-AUTH.md) - 认证配置说明
