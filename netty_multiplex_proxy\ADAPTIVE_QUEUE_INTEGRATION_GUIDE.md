# 自适应队列管理集成指南

## 概述

`AdaptiveQueueManager` 已成功集成到 `ProxyProcessor` 和 `BatchProxyProcessor` 中，提供智能的队列管理和性能优化能力。系统会根据实时负载自动调整处理策略，无需人工干预。

## 集成架构

```
ProxyProcessor
├── ProxyMetrics (性能指标收集)
├── AdaptiveQueueManager (自适应调整)
└── BatchProxyProcessor (批量处理 + 自适应)
```

## 启用自适应管理

### 1. 通过配置启用

```java
// 创建启用自适应调整的配置
ProxyProcessorConfig config = ProxyProcessorConfig.defaultConfig()
    .setEnableAdaptiveAdjustment(true)           // 启用自适应调整
    .setAdaptiveAdjustmentIntervalMs(10000)      // 调整间隔10秒
    .setBatchSize(15)                            // 基础批次大小
    .setBatchTimeoutMs(50);                      // 基础批次超时

// 创建处理器
BatchProxyProcessor processor = new BatchProxyProcessor(router, config);
processor.start(); // 自动启动自适应管理器
```

### 2. 使用工厂类

```java
// 高性能处理器默认启用自适应调整
BatchProxyProcessor processor = ProxyProcessorFactory.createHighPerformanceProcessor(router);

// 或使用构建器
ProxyProcessor processor = ProxyProcessorFactory.builder()
    .router(router)
    .type(ProcessorType.HIGH_PERFORMANCE)
    .build(); // 自动启用自适应调整
```

### 3. 自动推荐配置

```java
// 根据预期负载获取推荐配置（自动启用自适应）
ProcessorRecommendation recommendation = 
    ProxyProcessorFactory.getRecommendation(5000, 1024);

ProxyProcessor processor = ProxyProcessorFactory.createProcessor(
    recommendation.getType(), 
    router, 
    recommendation.getConfig()
);
```

## 自适应调整机制

### 1. 监控指标

系统持续监控以下关键指标：

- **队列负载率**: 队列使用情况
- **请求处理速率**: QPS 和吞吐量
- **平均处理时间**: 延迟指标
- **成功率**: 请求处理成功比例
- **拒绝率**: 队列满载导致的拒绝比例

### 2. 自动调整策略

```java
// 高负载时：增加批次大小，提高吞吐量
if (queueLoadRatio > 0.8) {
    batchSize = Math.min(currentSize + 5, maxBatchSize);
}

// 低负载时：减少批次大小，降低延迟
if (queueLoadRatio < 0.2) {
    batchSize = Math.max(currentSize - 2, minBatchSize);
}

// 高请求率时：减少轮询超时，更频繁检查
if (requestRate > 100) {
    pollTimeout = Math.max(currentTimeout - 200, minTimeout);
}
```

### 3. 调整参数

| 参数 | 调整范围 | 调整策略 |
|------|----------|----------|
| 批次大小 | 1-50 | 根据队列负载动态调整 |
| 轮询超时 | 100-5000ms | 根据请求频率调整 |
| 调整频率 | 5-60秒 | 可配置的调整间隔 |

## 性能监控

### 1. 实时指标获取

```java
// 获取性能报告
ProxyMetrics.MetricsReport report = processor.getMetricsReport();
System.out.println("QPS: " + report.getRequestsPerSecond());
System.out.println("平均延迟: " + report.getAvgProcessingTime() + "ms");
System.out.println("成功率: " + report.getSuccessRate() + "%");

// 获取队列状态
ProxyProcessor.QueueStats queueStats = processor.getQueueStats();
System.out.println("队列负载: " + queueStats.getTotalQueueSize());

// 获取批处理状态（BatchProxyProcessor）
BatchProxyProcessor.BatchStats batchStats = processor.getBatchStats();
System.out.println("当前批次大小: " + batchStats.getBatchSize());
```

### 2. 调整建议

```java
// 获取系统调整建议
String recommendations = processor.getAdjustmentRecommendations();
System.out.println("系统建议:\n" + recommendations);
```

### 3. 持续监控

```java
// 设置定期监控
ScheduledExecutorService monitor = Executors.newSingleThreadScheduledExecutor();
monitor.scheduleAtFixedRate(() -> {
    ProxyMetrics.MetricsReport report = processor.getMetricsReport();
    
    logger.info("=== 性能监控 ===");
    logger.info("QPS: {:.2f}", report.getRequestsPerSecond());
    logger.info("平均延迟: {:.2f}ms", report.getAvgProcessingTime());
    logger.info("成功率: {:.2f}%", report.getSuccessRate());
    
    // 检查是否需要人工干预
    if (report.getSuccessRate() < 95.0) {
        logger.warn("成功率过低，建议检查系统状态");
    }
    
}, 0, 30, TimeUnit.SECONDS);
```

## 配置参数详解

### 自适应相关配置

```java
ProxyProcessorConfig config = new ProxyProcessorConfig()
    // 基础配置
    .setQueueCount(8)                           // 队列数量
    .setQueueCapacity(20000)                    // 队列容量
    
    // 批处理配置
    .setBatchSize(15)                           // 基础批次大小
    .setBatchTimeoutMs(50)                      // 基础批次超时
    
    // 自适应配置
    .setEnableAdaptiveAdjustment(true)          // 启用自适应调整
    .setAdaptiveAdjustmentIntervalMs(10000);    // 调整间隔(毫秒)
```

### 预设配置

```java
// 高性能配置（自动启用自适应）
ProxyProcessorConfig.highPerformanceConfig()
    .setQueueCount(16)
    .setQueueCapacity(50000)
    .setBatchSize(20)
    .setBatchTimeoutMs(30)
    .setEnableAdaptiveAdjustment(true);

// 低资源配置（禁用自适应）
ProxyProcessorConfig.lowResourceConfig()
    .setQueueCount(2)
    .setQueueCapacity(1000)
    .setBatchSize(5)
    .setEnableAdaptiveAdjustment(false);
```

## 使用场景

### 1. 高并发Web代理

```java
// 适用于高并发Web代理场景
ProxyProcessorConfig webProxyConfig = ProxyProcessorConfig.highPerformanceConfig()
    .setQueueCount(Runtime.getRuntime().availableProcessors() * 2)
    .setBatchSize(25)
    .setBatchTimeoutMs(30)
    .setEnableAdaptiveAdjustment(true);

BatchProxyProcessor webProxy = new BatchProxyProcessor(router, webProxyConfig);
webProxy.start();
```

### 2. API网关

```java
// 适用于API网关场景
ProxyProcessorConfig apiGatewayConfig = ProxyProcessorConfig.defaultConfig()
    .setBatchSize(10)
    .setBatchTimeoutMs(20)  // 低延迟要求
    .setEnableAdaptiveAdjustment(true);

BatchProxyProcessor apiGateway = new BatchProxyProcessor(router, apiGatewayConfig);
```

### 3. 负载均衡器

```java
// 适用于负载均衡器场景
ProxyProcessorConfig lbConfig = ProxyProcessorConfig.connectionPoolOptimizedConfig()
    .setBatchSize(30)
    .setEnableAdaptiveAdjustment(true);

BatchProxyProcessor loadBalancer = new BatchProxyProcessor(router, lbConfig);
```

## 性能优化建议

### 1. 调整间隔优化

```java
// 高频调整（适用于负载变化快的场景）
config.setAdaptiveAdjustmentIntervalMs(5000);  // 5秒

// 低频调整（适用于负载相对稳定的场景）
config.setAdaptiveAdjustmentIntervalMs(30000); // 30秒
```

### 2. 批次大小优化

```java
// 延迟敏感场景
config.setBatchSize(5).setBatchTimeoutMs(10);

// 吞吐量优先场景
config.setBatchSize(50).setBatchTimeoutMs(100);

// 平衡场景（推荐）
config.setBatchSize(15).setBatchTimeoutMs(50);
```

### 3. 队列配置优化

```java
// 根据内存和负载调整
long maxMemory = Runtime.getRuntime().maxMemory();
int recommendedCapacity = (int) Math.min(50000, maxMemory / (1024 * 1024) * 100);

config.setQueueCapacity(recommendedCapacity);
```

## 故障排查

### 1. 自适应调整不生效

```java
// 检查配置
if (!processor.getConfig().isEnableAdaptiveAdjustment()) {
    logger.warn("自适应调整未启用");
}

// 检查管理器状态
AdaptiveQueueManager manager = processor.getAdaptiveManager();
if (manager == null) {
    logger.warn("自适应管理器未初始化");
}
```

### 2. 性能下降

```java
// 获取详细指标
ProxyMetrics.MetricsReport report = processor.getMetricsReport();
if (report.getAvgProcessingTime() > 1000) {
    logger.warn("平均处理时间过长: {}ms", report.getAvgProcessingTime());
}

// 检查队列状态
ProxyProcessor.QueueStats stats = processor.getQueueStats();
double loadRatio = (double) stats.getTotalQueueSize() / 
    (processor.getQueueCount() * processor.getConfig().getQueueCapacity());
if (loadRatio > 0.9) {
    logger.warn("队列负载过高: {:.2f}%", loadRatio * 100);
}
```

### 3. 调整建议

```java
// 获取系统建议
String recommendations = processor.getAdjustmentRecommendations();
logger.info("系统建议:\n{}", recommendations);

// 常见建议处理
if (recommendations.contains("队列负载过高")) {
    // 考虑增加队列容量或处理器数量
}
if (recommendations.contains("平均处理时间过长")) {
    // 检查网络连接和下游服务
}
```

## 最佳实践

### 1. 渐进式启用

```java
// 第一阶段：启用监控
ProxyProcessorConfig phase1 = config.setEnableAdaptiveAdjustment(false);
// 观察基准性能

// 第二阶段：启用自适应
ProxyProcessorConfig phase2 = config.setEnableAdaptiveAdjustment(true);
// 观察自适应效果
```

### 2. 监控告警

```java
// 设置关键指标告警
monitor.scheduleAtFixedRate(() -> {
    ProxyMetrics.MetricsReport report = processor.getMetricsReport();
    
    if (report.getSuccessRate() < 95.0) {
        alertService.sendAlert("成功率过低: " + report.getSuccessRate());
    }
    
    if (report.getRequestsPerSecond() < expectedQPS * 0.8) {
        alertService.sendAlert("QPS过低: " + report.getRequestsPerSecond());
    }
    
}, 0, 60, TimeUnit.SECONDS);
```

### 3. 配置调优

```java
// 根据实际负载调优
ProcessorRecommendation recommendation = 
    ProxyProcessorFactory.getRecommendation(actualQPS, avgRequestSize);

logger.info("推荐配置: {}", recommendation);
// 根据推荐调整配置
```

## 总结

自适应队列管理的集成为代理处理器带来了：

1. **智能调优**: 自动根据负载调整处理策略
2. **性能提升**: 在不同负载下保持最优性能
3. **运维简化**: 减少人工调优工作量
4. **监控完善**: 提供丰富的性能指标和建议

通过合理配置和监控，可以显著提升系统在动态负载下的表现。
</text>