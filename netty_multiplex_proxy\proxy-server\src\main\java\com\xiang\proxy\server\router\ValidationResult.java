package com.xiang.proxy.server.router;

/**
 * 验证结果
 */
public class ValidationResult {
    private final boolean valid;
    private final String message;
    private final String errorCode;

    private ValidationResult(boolean valid, String message, String errorCode) {
        this.valid = valid;
        this.message = message;
        this.errorCode = errorCode;
    }

    public boolean isValid() {
        return valid;
    }

    public String getMessage() {
        return message;
    }

    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String toString() {
        return String.format("ValidationResult{valid=%s, message=%s, errorCode=%s}",
                valid, message, errorCode);
    }

    // 静态工厂方法
    public static ValidationResult success() {
        return new ValidationResult(true, "Validation successful", null);
    }

    public static ValidationResult success(String message) {
        return new ValidationResult(true, message, null);
    }

    public static ValidationResult failure(String message) {
        return new ValidationResult(false, message, null);
    }

    public static ValidationResult failure(String message, String errorCode) {
        return new ValidationResult(false, message, errorCode);
    }
}