package com.xiang.proxy.server.service;

import com.xiang.proxy.server.ProxyServerV2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * ProxyServerV2 Spring Boot 服务管理类
 * 负责将ProxyServerV2的生命周期整合到Spring Boot应用中
 */
@Service
public class ProxyServerV2Service implements ApplicationRunner, DisposableBean {
    
    private static final Logger logger = LoggerFactory.getLogger(ProxyServerV2Service.class);
    
    private ProxyServerV2 proxyServer;
    private volatile boolean started = false;
    private volatile boolean startupFailed = false;
    private Exception startupException;
    
    /**
     * Spring Boot应用启动后自动执行
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("Spring Boot应用启动完成，开始启动ProxyServerV2...");
        try {
            ProxyServerV2.parseCommandLineArgs(args.getSourceArgs());
            // 创建ProxyServerV2实例
            proxyServer = new ProxyServerV2();
            
            // 异步启动代理服务器
            CompletableFuture<Void> startFuture = proxyServer.start();
            
            // 等待启动完成，设置超时时间
            startFuture.get(60, TimeUnit.SECONDS);
            
            started = true;
            startupFailed = false;
            startupException = null;
            
            logger.info("ProxyServerV2启动成功，已整合到Spring Boot生命周期中");
            
        } catch (Exception e) {
            started = false;
            startupFailed = true;
            startupException = e;
            
            logger.error("启动ProxyServerV2失败", e);
            
            // 不抛出异常，让Spring Boot继续运行，但标记启动失败
            // 这样可以通过健康检查接口查看失败原因
            logger.warn("ProxyServerV2启动失败，但Spring Boot应用将继续运行");
        }
    }
    
    /**
     * Spring Boot应用关闭时自动执行
     */
    @Override
    public void destroy() throws Exception {
        if (started && proxyServer != null) {
            logger.info("Spring Boot应用正在关闭，开始停止ProxyServerV2...");
            
            try {
                // 异步停止代理服务器
                CompletableFuture<Void> stopFuture = proxyServer.stop();
                
                // 等待停止完成，设置超时时间
                stopFuture.get(30, TimeUnit.SECONDS);
                
                started = false;
                logger.info("ProxyServerV2已成功停止");
                
            } catch (Exception e) {
                logger.error("停止ProxyServerV2时发生异常", e);
                // 不抛出异常，避免影响Spring Boot的正常关闭流程
            }
        }
    }
    
    /**
     * 获取ProxyServerV2实例
     */
    public ProxyServerV2 getProxyServer() {
        return proxyServer;
    }
    
    /**
     * 检查服务是否已启动
     */
    public boolean isStarted() {
        return started && proxyServer != null && proxyServer.isRunning();
    }
    
    /**
     * 获取服务状态信息
     */
    public String getStatus() {
        if (startupFailed) {
            return "FAILED";
        }
        
        if (!started || proxyServer == null) {
            return "STOPPED";
        }
        
        if (proxyServer.isRunning()) {
            return "RUNNING";
        } else {
            return "STARTING";
        }
    }
    
    /**
     * 获取启动异常信息
     */
    public Exception getStartupException() {
        return startupException;
    }
    
    /**
     * 检查启动是否失败
     */
    public boolean isStartupFailed() {
        return startupFailed;
    }
}