package com.xiang.proxy.server.inbound;

import java.util.HashMap;
import java.util.Map;

/**
 * Inbound服务器配置
 */
public class InboundServerConfig {
    private int port;
    private String bindAddress = "0.0.0.0";
    private int bossThreads = 1;
    private int workerThreads = 0; // 0表示使用默认值
    private int backlog = 1024;
    private boolean keepAlive = true;
    private boolean tcpNoDelay = true;
    private int soTimeout = 30000;
    private int connectTimeout = 5000;
    private int receiveBufferSize = 65536;
    private int sendBufferSize = 65536;
    private boolean reuseAddress = true;
    private int maxConnections = 10000;
    private Map<String, Object> properties = new HashMap<>();
    private boolean enableSsl;

    public InboundServerConfig() {
    }

    public InboundServerConfig(int port) {
        this.port = port;
    }

    // Getters and Setters
    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getBindAddress() {
        return bindAddress;
    }

    public void setBindAddress(String bindAddress) {
        this.bindAddress = bindAddress;
    }

    public int getBossThreads() {
        return bossThreads;
    }

    public void setBossThreads(int bossThreads) {
        this.bossThreads = bossThreads;
    }

    public int getWorkerThreads() {
        return workerThreads;
    }

    public void setWorkerThreads(int workerThreads) {
        this.workerThreads = workerThreads;
    }

    public int getBacklog() {
        return backlog;
    }

    public void setBacklog(int backlog) {
        this.backlog = backlog;
    }

    public boolean isKeepAlive() {
        return keepAlive;
    }

    public void setKeepAlive(boolean keepAlive) {
        this.keepAlive = keepAlive;
    }

    public boolean isTcpNoDelay() {
        return tcpNoDelay;
    }

    public void setTcpNoDelay(boolean tcpNoDelay) {
        this.tcpNoDelay = tcpNoDelay;
    }

    public int getSoTimeout() {
        return soTimeout;
    }

    public void setSoTimeout(int soTimeout) {
        this.soTimeout = soTimeout;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReceiveBufferSize() {
        return receiveBufferSize;
    }

    public void setReceiveBufferSize(int receiveBufferSize) {
        this.receiveBufferSize = receiveBufferSize;
    }

    public int getSendBufferSize() {
        return sendBufferSize;
    }

    public void setSendBufferSize(int sendBufferSize) {
        this.sendBufferSize = sendBufferSize;
    }

    public boolean isReuseAddress() {
        return reuseAddress;
    }

    public void setReuseAddress(boolean reuseAddress) {
        this.reuseAddress = reuseAddress;
    }

    public int getMaxConnections() {
        return maxConnections;
    }

    public void setMaxConnections(int maxConnections) {
        this.maxConnections = maxConnections;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties != null ? properties : new HashMap<>();
    }

    // 便捷方法
    public void setProperty(String key, Object value) {
        this.properties.put(key, value);
    }

    @SuppressWarnings("unchecked")
    public <T> T getProperty(String key) {
        return (T) this.properties.get(key);
    }

    public <T> T getProperty(String key, T defaultValue) {
        T value = getProperty(key);
        return value != null ? value : defaultValue;
    }

    public boolean hasProperty(String key) {
        return this.properties.containsKey(key);
    }

    @Override
    public String toString() {
        return String.format("InboundServerConfig{port=%d, bindAddress=%s, bossThreads=%d, " +
                        "workerThreads=%d, backlog=%d, maxConnections=%d}",
                port, bindAddress, bossThreads, workerThreads, backlog, maxConnections);
    }

    // 复制方法
    public InboundServerConfig copy() {
        InboundServerConfig copy = new InboundServerConfig();
        copy.port = this.port;
        copy.bindAddress = this.bindAddress;
        copy.bossThreads = this.bossThreads;
        copy.workerThreads = this.workerThreads;
        copy.backlog = this.backlog;
        copy.keepAlive = this.keepAlive;
        copy.tcpNoDelay = this.tcpNoDelay;
        copy.soTimeout = this.soTimeout;
        copy.connectTimeout = this.connectTimeout;
        copy.receiveBufferSize = this.receiveBufferSize;
        copy.sendBufferSize = this.sendBufferSize;
        copy.reuseAddress = this.reuseAddress;
        copy.maxConnections = this.maxConnections;
        copy.enableSsl = this.enableSsl;
        copy.properties = new HashMap<>(this.properties);
        return copy;
    }

    // 预定义配置
    public static InboundServerConfig defaultConfig(int port) {
        return new InboundServerConfig(port);
    }

    public void setEnableSsl(boolean enableSsl) {
        this.enableSsl = enableSsl;
    }

    public boolean isEnableSsl() {
        return enableSsl;
    }
}