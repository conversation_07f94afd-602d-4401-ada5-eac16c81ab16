package com.xiang.proxy.server.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 恶意内容加载器
 * 从配置文件和在线数据源加载最新的恶意域名和关键词
 */
public class MaliciousContentLoader {
    private static final Logger logger = LoggerFactory.getLogger(MaliciousContentLoader.class);
    
    // 配置文件名
    private static final String MALICIOUS_DOMAINS_FILE = "malicious-domains.txt";
    private static final String MALICIOUS_KEYWORDS_FILE = "malicious-keywords.txt";
    private static final String WHITELIST_DOMAINS_FILE = "whitelist-domains.txt";
    
    // 在线数据源URL - 从配置文件加载
    private static java.util.List<String> getMaliciousDomainsUrls() {
        try {
            com.xiang.proxy.server.config.ProxyServerConfigManager configManager =
                com.xiang.proxy.server.config.ProxyServerConfigManager.getInstance();
            return configManager.getMaliciousDomainsUrls();
        } catch (Exception e) {
            // 如果配置加载失败，使用默认值
            return java.util.Arrays.asList(
                "https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts",
                "https://someonewhocares.org/hosts/zero/hosts",
                "https://raw.githubusercontent.com/AdguardTeam/AdguardFilters/master/BaseFilter/sections/adservers.txt"
            );
        }
    }

    private static java.util.List<String> getMaliciousKeywordsUrls() {
        try {
            com.xiang.proxy.server.config.ProxyServerConfigManager configManager =
                com.xiang.proxy.server.config.ProxyServerConfigManager.getInstance();
            return configManager.getMaliciousKeywordsUrls();
        } catch (Exception e) {
            // 如果配置加载失败，使用默认值
            return java.util.Arrays.asList(
                "https://raw.githubusercontent.com/crazy-max/WindowsSpyBlocker/master/data/hosts/spy.txt"
            );
        }
    }
    
    /**
     * 加载恶意域名列表
     */
    public static Set<String> loadMaliciousDomains() {
        Set<String> domains = new HashSet<>();
        
        // 1. 从本地配置文件加载
        loadDomainsFromFile(MALICIOUS_DOMAINS_FILE, domains);
        
        // 2. 从在线数据源加载（异步）
        loadDomainsFromOnlineAsync(domains);
        
        // 3. 添加内置的恶意域名
        addBuiltinMaliciousDomains(domains);
        
        logger.info("加载恶意域名完成，总数: {}", domains.size());
        return domains;
    }
    
    /**
     * 加载恶意关键词列表
     */
    public static Set<String> loadMaliciousKeywords() {
        Set<String> keywords = new HashSet<>();

        // 1. 从本地配置文件加载
        loadKeywordsFromFile(MALICIOUS_KEYWORDS_FILE, keywords);

        // 2. 从在线数据源加载（异步）
        loadKeywordsFromOnlineAsync(keywords);

        // 3. 添加内置的恶意关键词
        addBuiltinMaliciousKeywords(keywords);

        logger.info("加载恶意关键词完成，总数: {}", keywords.size());
        return keywords;
    }
    
    /**
     * 加载白名单域名列表
     */
    public static Set<String> loadWhitelistDomains() {
        Set<String> domains = new HashSet<>();
        
        // 1. 从本地配置文件加载
        loadDomainsFromFile(WHITELIST_DOMAINS_FILE, domains);
        
        // 2. 添加内置的白名单域名
        addBuiltinWhitelistDomains(domains);
        
        logger.info("加载白名单域名完成，总数: {}", domains.size());
        return domains;
    }
    
    /**
     * 从文件加载域名列表
     */
    private static void loadDomainsFromFile(String filename, Set<String> domains) {
        // 首先尝试从外部文件加载
        if (loadFromExternalFile(filename, domains)) {
            return;
        }
        
        // 然后尝试从classpath加载
        loadFromClasspath(filename, domains);
    }
    
    /**
     * 从文件加载关键词列表
     */
    private static void loadKeywordsFromFile(String filename, Set<String> keywords) {
        // 首先尝试从外部文件加载
        if (loadKeywordsFromExternalFile(filename, keywords)) {
            return;
        }
        
        // 然后尝试从classpath加载
        loadKeywordsFromClasspath(filename, keywords);
    }
    
    /**
     * 从外部文件加载域名
     */
    private static boolean loadFromExternalFile(String filename, Set<String> domains) {
        // 1. 首先尝试从配置目录加载
        if (loadFromConfigDirectory(filename, domains)) {
            return true;
        }

        // 2. 然后尝试从当前目录加载
        File file = new File(filename);
        if (!file.exists() || !file.isFile()) {
            return false;
        }

        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int count = 0;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty() && !line.startsWith("#")) {
                    String domain = extractDomain(line);
                    if (isValidDomain(domain)) {
                        domains.add(domain.toLowerCase());
                        count++;
                    }
                }
            }
            logger.info("从外部文件 {} 加载了 {} 个域名", filename, count);
            return count > 0;
        } catch (IOException e) {
            logger.warn("从外部文件加载域名失败: {}", filename, e);
            return false;
        }
    }
    
    /**
     * 从classpath加载域名
     */
    private static boolean loadFromClasspath(String filename, Set<String> domains) {
        try (InputStream is = MaliciousContentLoader.class.getClassLoader().getResourceAsStream(filename)) {
            if (is != null) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {
                    String line;
                    int count = 0;
                    while ((line = reader.readLine()) != null) {
                        line = line.trim();
                        if (!line.isEmpty() && !line.startsWith("#")) {
                            String domain = extractDomain(line);
                            if (isValidDomain(domain)) {
                                domains.add(domain.toLowerCase());
                                count++;
                            }
                        }
                    }
                    logger.info("从classpath {} 加载了 {} 个域名", filename, count);
                    return count > 0;
                }
            }
        } catch (IOException e) {
            logger.warn("从classpath加载域名失败: {}", filename, e);
        }
        return false;
    }
    
    /**
     * 从外部文件加载关键词
     */
    private static boolean loadKeywordsFromExternalFile(String filename, Set<String> keywords) {
        // 1. 首先尝试从配置目录加载
        if (loadKeywordsFromConfigDirectory(filename, keywords)) {
            return true;
        }

        // 2. 然后尝试从当前目录加载
        File file = new File(filename);
        if (!file.exists() || !file.isFile()) {
            return false;
        }

        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            int count = 0;
            while ((line = reader.readLine()) != null) {
                line = line.trim().toLowerCase();
                if (!line.isEmpty() && !line.startsWith("#")) {
                    keywords.add(line);
                    count++;
                }
            }
            logger.info("从外部文件 {} 加载了 {} 个关键词", filename, count);
            return count > 0;
        } catch (IOException e) {
            logger.warn("从外部文件加载关键词失败: {}", filename, e);
            return false;
        }
    }
    
    /**
     * 从classpath加载关键词
     */
    private static boolean loadKeywordsFromClasspath(String filename, Set<String> keywords) {
        try (InputStream is = MaliciousContentLoader.class.getClassLoader().getResourceAsStream(filename)) {
            if (is != null) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {
                    String line;
                    int count = 0;
                    while ((line = reader.readLine()) != null) {
                        line = line.trim().toLowerCase();
                        if (!line.isEmpty() && !line.startsWith("#")) {
                            keywords.add(line);
                            count++;
                        }
                    }
                    logger.info("从classpath {} 加载了 {} 个关键词", filename, count);
                    return count > 0;
                }
            }
        } catch (IOException e) {
            logger.warn("从classpath加载关键词失败: {}", filename, e);
        }
        return false;
    }
    
    /**
     * 异步从在线数据源加载域名
     */
    private static void loadDomainsFromOnlineAsync(Set<String> domains) {
        CompletableFuture.runAsync(() -> {
            Set<String> onlineDomains = new HashSet<>();
            int successCount = 0;

            // 获取配置的在线数据源URL
            java.util.List<String> maliciousDomainsUrls = getMaliciousDomainsUrls();

            // 加载所有在线数据源的总量
            for (String url : maliciousDomainsUrls) {
                try {
                    Set<String> urlDomains = new HashSet<>();
                    loadDomainsFromUrl(url, urlDomains);
                    onlineDomains.addAll(urlDomains);
                    successCount++;
                    logger.info("成功从在线数据源加载 {} 个域名: {}", urlDomains.size(), url);
                } catch (Exception e) {
                    logger.warn("从在线数据源加载失败: {}, 错误: {}", url, e.getMessage());
                }
            }

            // 如果成功从在线加载了数据，添加到主集合并保存到文件
            if (!onlineDomains.isEmpty()) {
                domains.addAll(onlineDomains);
                saveMaliciousDomainsToFile(onlineDomains);
                logger.info("在线恶意域名加载完成，成功源: {}/{}, 总域名数: {}",
                           successCount, maliciousDomainsUrls.size(), onlineDomains.size());
            } else {
                logger.warn("所有在线恶意域名数据源都加载失败");
            }
        }).orTimeout(60, TimeUnit.SECONDS).exceptionally(throwable -> {
            logger.warn("在线加载恶意域名超时或失败: {}", throwable.getMessage());
            return null;
        });
    }

    /**
     * 异步从在线数据源加载关键词
     */
    private static void loadKeywordsFromOnlineAsync(Set<String> keywords) {
        CompletableFuture.runAsync(() -> {
            Set<String> onlineKeywords = new HashSet<>();
            int successCount = 0;

            // 获取配置的在线数据源URL
            java.util.List<String> maliciousKeywordsUrls = getMaliciousKeywordsUrls();

            // 加载所有在线数据源的总量
            for (String url : maliciousKeywordsUrls) {
                try {
                    Set<String> urlKeywords = new HashSet<>();
                    loadKeywordsFromUrl(url, urlKeywords);
                    onlineKeywords.addAll(urlKeywords);
                    successCount++;
                    logger.info("成功从在线数据源加载 {} 个关键词: {}", urlKeywords.size(), url);
                } catch (Exception e) {
                    logger.warn("从在线关键词数据源加载失败: {}, 错误: {}", url, e.getMessage());
                }
            }

            // 如果成功从在线加载了数据，添加到主集合并保存到文件
            if (!onlineKeywords.isEmpty()) {
                keywords.addAll(onlineKeywords);
                saveMaliciousKeywordsToFile(onlineKeywords);
                logger.info("在线恶意关键词加载完成，成功源: {}/{}, 总关键词数: {}",
                           successCount, maliciousKeywordsUrls.size(), onlineKeywords.size());
            } else {
                logger.warn("所有在线恶意关键词数据源都加载失败");
            }
        }).orTimeout(60, TimeUnit.SECONDS).exceptionally(throwable -> {
            logger.warn("在线加载恶意关键词超时或失败: {}", throwable.getMessage());
            return null;
        });
    }
    
    /**
     * 从URL加载域名
     */
    private static void loadDomainsFromUrl(String urlString, Set<String> domains) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(10000);
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Java Malicious Content Loader)");

        if (connection.getResponseCode() == 200) {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()))) {
                String line;
                int count = 0;
                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    if (!line.isEmpty() && !line.startsWith("#")) {
                        String domain = extractDomain(line);
                        if (isValidDomain(domain)) {
                            domains.add(domain.toLowerCase());
                            count++;
                        }
                    }
                }
                logger.info("从在线数据源 {} 加载了 {} 个域名", urlString, count);
            }
        }
    }

    /**
     * 从URL加载关键词
     */
    private static void loadKeywordsFromUrl(String urlString, Set<String> keywords) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(10000);
        connection.setReadTimeout(20000);
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Java Malicious Content Loader)");

        if (connection.getResponseCode() == 200) {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()))) {
                String line;
                int count = 0;
                while ((line = reader.readLine()) != null) {
                    line = line.trim().toLowerCase();
                    if (!line.isEmpty() && !line.startsWith("#")) {
                        // 检查是否是hosts文件格式 (IP + 域名)
                        if (line.startsWith("0.0.0.0 ") || line.startsWith("127.0.0.1 ")) {
                            // 提取域名部分
                            String[] parts = line.split("\\s+");
                            if (parts.length >= 2) {
                                String domain = parts[1];
                                // 1. 将完整域名作为关键词添加
                                if (isValidKeyword(domain)) {
                                    keywords.add(domain);
                                    count++;
                                }
                                // 2. 从域名中提取可能的关键词片段
                                Set<String> extractedKeywords = new HashSet<>();
                                extractKeywordsFromDomain(domain, extractedKeywords);
                                for (String keyword : extractedKeywords) {
                                    if (isValidKeyword(keyword)) {
                                        keywords.add(keyword);
                                        count++;
                                    }
                                }
                            }
                        } else if (line.contains(".") && !line.contains(" ")) {
                            // 可能是单独的域名行
                            if (isValidKeyword(line)) {
                                keywords.add(line);
                                count++;
                            }
                            Set<String> extractedKeywords = new HashSet<>();
                            extractKeywordsFromDomain(line, extractedKeywords);
                            for (String keyword : extractedKeywords) {
                                if (isValidKeyword(keyword)) {
                                    keywords.add(keyword);
                                    count++;
                                }
                            }
                        } else {
                            // 直接作为关键词添加
                            if (isValidKeyword(line)) {
                                keywords.add(line);
                                count++;
                            }
                        }
                    }
                }
                logger.info("从在线关键词数据源 {} 加载了 {} 个关键词", urlString, count);
            }
        }
    }
    
    /**
     * 从hosts文件格式的行中提取域名
     */
    private static String extractDomain(String line) {
        // 处理hosts文件格式: 0.0.0.0 example.com
        // 或者简单的域名列表格式
        String[] parts = line.split("\\s+");
        
        for (String part : parts) {
            // 跳过IP地址和注释
            if (part.matches("\\d+\\.\\d+\\.\\d+\\.\\d+") || part.startsWith("#")) {
                continue;
            }
            
            // 找到看起来像域名的部分
            if (part.contains(".") && !part.startsWith(".") && !part.endsWith(".")) {
                return part;
            }
        }
        
        return line; // 如果没有找到特殊格式，返回原始行
    }
    
    /**
     * 验证域名格式是否有效
     */
    private static boolean isValidDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        
        domain = domain.trim().toLowerCase();
        
        // 基本格式检查
        if (domain.length() < 3 || domain.length() > 253) {
            return false;
        }
        
        // 不能以点开始或结束
        if (domain.startsWith(".") || domain.endsWith(".")) {
            return false;
        }
        
        // 必须包含至少一个点
        if (!domain.contains(".")) {
            return false;
        }
        
        // 简单的域名格式验证
        return domain.matches("^[a-z0-9.-]+\\.[a-z]{2,}$");
    }
    
    /**
     * 添加内置的恶意域名
     */
    private static void addBuiltinMaliciousDomains(Set<String> domains) {
        domains.addAll(Arrays.asList(
            // 已知恶意域名示例
            "malware-site.com", "phishing-bank.com", "fake-paypal.com",
            "virus-download.net", "scam-lottery.org", "illegal-casino.biz",
            "fake-microsoft.com", "phishing-amazon.com", "malicious-update.net"
        ));
    }
    
    /**
     * 添加内置的恶意关键词
     */
    private static void addBuiltinMaliciousKeywords(Set<String> keywords) {
        keywords.addAll(Arrays.asList(
            // 成人内容相关
            "porn", "xxx", "sex", "adult", "nude", "naked", "erotic", "sexy", "cam", "live",
            "escort", "dating", "hookup", "milf", "teen", "amateur", "fetish", "bdsm",
            
            // 赌博相关
            "casino", "gambling", "bet", "poker", "lottery", "slots", "jackpot", "roulette",
            "blackjack", "baccarat", "dice", "sports-bet", "online-casino",
            
            // 网络安全威胁
            "phishing", "scam", "fraud", "fake", "malware", "virus", "trojan", "ransomware",
            "botnet", "keylogger", "spyware", "adware", "rootkit",
            
            // 盗版内容
            "torrent", "pirate", "crack", "warez", "keygen", "serial", "patch", "hack",
            "download", "free-movie", "free-music", "piratebay",
            
            // 其他可疑内容
            "drugs", "pharmacy", "pills", "viagra", "cialis", "steroids", "marijuana",
            "weapon", "gun", "explosive", "bomb", "terror"
        ));
    }
    
    /**
     * 添加内置的白名单域名
     */
    private static void addBuiltinWhitelistDomains(Set<String> domains) {
        domains.addAll(Arrays.asList(
            // 搜索引擎和门户
            "google.com", "bing.com", "yahoo.com", "duckduckgo.com",
            
            // 社交媒体
            "youtube.com", "facebook.com", "twitter.com", "instagram.com",
            "linkedin.com", "reddit.com", "discord.com", "telegram.org",
            "whatsapp.com", "snapchat.com", "tiktok.com",
            
            // 技术和开发
            "github.com", "gitlab.com", "stackoverflow.com", "stackexchange.com",
            "mozilla.org", "w3.org", "apache.org", "nodejs.org", "python.org",
            "docker.com", "kubernetes.io", "jenkins.io",
            
            // 云服务和企业
            "microsoft.com", "apple.com", "amazon.com", "aws.amazon.com",
            "azure.microsoft.com", "cloud.google.com", "ibm.com", "oracle.com",
            "salesforce.com", "dropbox.com", "box.com", "slack.com",
            
            // 媒体和娱乐
            "netflix.com", "hulu.com", "disney.com", "spotify.com",
            "twitch.tv", "vimeo.com", "soundcloud.com",
            
            // 新闻和信息
            "wikipedia.org", "bbc.com", "cnn.com", "reuters.com",
            "nytimes.com", "wsj.com", "bloomberg.com",
            
            // 电商和支付
            "paypal.com", "stripe.com", "shopify.com", "ebay.com",
            "etsy.com", "aliexpress.com",
            
            // 教育和学术
            "coursera.org", "edx.org", "khanacademy.org", "mit.edu",
            "stanford.edu", "harvard.edu", "berkeley.edu"
        ));
    }

    /**
     * 从配置目录加载域名文件
     */
    private static boolean loadFromConfigDirectory(String filename, Set<String> domains) {
        try {
            // 获取配置目录路径
            String configDir = getConfigDirectory();
            if (configDir == null || configDir.trim().isEmpty()) {
                return false;
            }

            File configFile = new File(configDir, filename);
            if (!configFile.exists() || !configFile.isFile()) {
                logger.debug("配置目录中未找到文件: {}", configFile.getAbsolutePath());
                return false;
            }

            try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
                String line;
                int count = 0;
                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    if (!line.isEmpty() && !line.startsWith("#")) {
                        String domain = extractDomain(line);
                        if (isValidDomain(domain)) {
                            domains.add(domain.toLowerCase());
                            count++;
                        }
                    }
                }

                if (count > 0) {
                    logger.info("成功从配置目录加载域名文件: {} ({} 个域名)", configFile.getAbsolutePath(), count);
                    return true;
                }
            }
        } catch (Exception e) {
            logger.warn("从配置目录加载域名文件失败: {}", filename, e);
        }
        return false;
    }

    /**
     * 从配置目录加载关键词文件
     */
    private static boolean loadKeywordsFromConfigDirectory(String filename, Set<String> keywords) {
        try {
            // 获取配置目录路径
            String configDir = getConfigDirectory();
            if (configDir == null || configDir.trim().isEmpty()) {
                return false;
            }

            File configFile = new File(configDir, filename);
            if (!configFile.exists() || !configFile.isFile()) {
                logger.debug("配置目录中未找到文件: {}", configFile.getAbsolutePath());
                return false;
            }

            try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
                String line;
                int count = 0;
                while ((line = reader.readLine()) != null) {
                    line = line.trim().toLowerCase();
                    if (!line.isEmpty() && !line.startsWith("#")) {
                        // 检查是否是hosts文件格式 (IP + 域名)
                        if (line.startsWith("0.0.0.0 ") || line.startsWith("127.0.0.1 ")) {
                            // 提取域名部分
                            String[] parts = line.split("\\s+");
                            if (parts.length >= 2) {
                                String domain = parts[1];
                                // 1. 将完整域名作为关键词添加
                                keywords.add(domain);
                                // 2. 从域名中提取可能的关键词片段
                                extractKeywordsFromDomain(domain, keywords);
                                count++;
                            }
                        } else if (line.contains(".") && !line.contains(" ")) {
                            // 可能是单独的域名行
                            keywords.add(line);
                            extractKeywordsFromDomain(line, keywords);
                            count++;
                        } else {
                            // 直接作为关键词添加
                            keywords.add(line);
                            count++;
                        }
                    }
                }

                if (count > 0) {
                    logger.info("成功从配置目录加载关键词文件: {} ({} 个关键词)", configFile.getAbsolutePath(), count);
                    return true;
                }
            }
        } catch (Exception e) {
            logger.warn("从配置目录加载关键词文件失败: {}", filename, e);
        }
        return false;
    }

    /**
     * 获取配置目录路径
     */
    private static String getConfigDirectory() {
        try {
            // 从 app.properties 读取配置目录
            Properties props = new Properties();
            try (InputStream is = MaliciousContentLoader.class.getClassLoader().getResourceAsStream("app.properties")) {
                if (is != null) {
                    props.load(is);
                    String configDir = props.getProperty("config.ext.dir");
                    if (configDir != null && !configDir.trim().isEmpty()) {
                        return configDir.trim();
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("获取配置目录失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从行中提取关键词
     */
    private static String extractKeywordFromLine(String line) {
        // 如果是hosts文件格式，提取域名中的关键词
        String domain = extractDomain(line);
        if (domain != null && !domain.isEmpty()) {
            // 从域名中提取可能的恶意关键词
            String[] parts = domain.split("[.-]");
            for (String part : parts) {
                if (part.length() > 2 && isSuspiciousKeyword(part)) {
                    return part.toLowerCase();
                }
            }
        }

        // 如果不是域名格式，直接作为关键词处理
        line = line.trim().toLowerCase();
        if (line.length() > 2 && !line.contains(".")) {
            return line;
        }

        return null;
    }

    /**
     * 检查是否是可疑关键词
     */
    private static boolean isSuspiciousKeyword(String word) {
        if (word == null || word.length() < 3) {
            return false;
        }

        // 检查是否包含常见的恶意关键词模式
        String[] suspiciousPatterns = {
            "porn", "xxx", "sex", "adult", "casino", "gambling", "bet", "poker",
            "malware", "virus", "trojan", "phishing", "scam", "fraud", "fake",
            "crack", "warez", "torrent", "pirate", "hack", "drugs", "weapon"
        };

        for (String pattern : suspiciousPatterns) {
            if (word.contains(pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 验证关键词是否有效
     */
    private static boolean isValidKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return false;
        }

        keyword = keyword.trim().toLowerCase();

        // 长度检查
        if (keyword.length() < 3 || keyword.length() > 50) {
            return false;
        }

        // 不能包含特殊字符（除了连字符）
        if (!keyword.matches("^[a-z0-9-]+$")) {
            return false;
        }

        // 不能是纯数字
        if (keyword.matches("^\\d+$")) {
            return false;
        }

        // 排除一些常见的无意义词汇
        String[] excludeWords = {"www", "com", "net", "org", "http", "https", "ftp"};
        for (String exclude : excludeWords) {
            if (keyword.equals(exclude)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 保存恶意域名到配置目录
     */
    private static void saveMaliciousDomainsToFile(Set<String> domains) {
        saveToConfigDirectory(MALICIOUS_DOMAINS_FILE, domains, "恶意域名");
    }

    /**
     * 保存恶意关键词到配置目录
     */
    private static void saveMaliciousKeywordsToFile(Set<String> keywords) {
        saveToConfigDirectory(MALICIOUS_KEYWORDS_FILE, keywords, "恶意关键词");
    }

    /**
     * 保存白名单域名到配置目录
     */
    private static void saveWhitelistDomainsToFile(Set<String> domains) {
        saveToConfigDirectory(WHITELIST_DOMAINS_FILE, domains, "白名单域名");
    }

    /**
     * 通用的保存方法 - 保存数据到配置目录
     */
    private static void saveToConfigDirectory(String filename, Set<String> data, String dataType) {
        try {
            String configDir = getConfigDirectory();
            if (configDir == null || configDir.trim().isEmpty()) {
                logger.debug("配置目录未设置，跳过保存{}", dataType);
                return;
            }

            File configDirFile = new File(configDir);
            if (!configDirFile.exists()) {
                if (!configDirFile.mkdirs()) {
                    logger.warn("无法创建配置目录: {}", configDir);
                    return;
                }
            }

            File configFile = new File(configDirFile, filename);
            try (PrintWriter writer = new PrintWriter(new FileWriter(configFile))) {
                writer.println("# " + dataType + "配置文件");
                writer.println("# 自动生成于: " + new java.util.Date());
                writer.println("# 总计: " + data.size() + " 个条目");
                writer.println();

                for (String item : data) {
                    writer.println(item);
                }

                logger.info("成功将 {} 个{}保存到配置目录: {}",
                           data.size(), dataType, configFile.getAbsolutePath());

            }
        } catch (Exception e) {
            logger.warn("保存{}到配置目录失败: {}", dataType, filename, e);
        }
    }

    /**
     * 从域名中提取关键词
     */
    private static void extractKeywordsFromDomain(String domain, Set<String> keywords) {
        if (domain == null || domain.isEmpty()) {
            return;
        }

        // 移除常见的前缀和后缀
        domain = domain.toLowerCase()
                      .replaceAll("^(www\\.|m\\.|mobile\\.|api\\.|cdn\\.|static\\.)", "")
                      .replaceAll("\\.(com|net|org|edu|gov|mil|int|co|uk|de|fr|jp|cn|ru)$", "");

        // 按点和连字符分割域名
        String[] parts = domain.split("[.\\-_]");

        for (String part : parts) {
            part = part.trim();
            if (part.length() >= 3 && !isCommonWord(part)) {
                // 检查是否包含已知的恶意关键词模式
                if (containsSuspiciousPattern(part)) {
                    keywords.add(part);
                }
            }
        }
    }

    /**
     * 检查是否是常见词汇（避免误判）
     */
    private static boolean isCommonWord(String word) {
        Set<String> commonWords = Set.of(
            "www", "mail", "ftp", "blog", "news", "shop", "store", "home", "page",
            "site", "web", "online", "service", "support", "help", "info", "data",
            "server", "host", "cloud", "app", "mobile", "api", "cdn", "static",
            "secure", "login", "account", "user", "admin", "test", "demo", "beta"
        );
        return commonWords.contains(word);
    }

    /**
     * 检查是否包含可疑模式
     */
    private static boolean containsSuspiciousPattern(String word) {
        // 检查是否包含已知的恶意关键词
        String[] suspiciousKeywords = {
            "ads", "ad", "advert", "banner", "popup", "click", "track", "analytics",
            "telemetry", "spy", "monitor", "collect", "gather", "report", "metric",
            "porn", "xxx", "sex", "adult", "casino", "gambling", "bet", "poker",
            "phishing", "scam", "fraud", "fake", "malware", "virus", "trojan",
            "torrent", "pirate", "crack", "warez", "drug", "weapon"
        };

        for (String keyword : suspiciousKeywords) {
            if (word.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

}
