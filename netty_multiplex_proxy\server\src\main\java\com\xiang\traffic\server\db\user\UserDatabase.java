package com.xiang.traffic.server.db.user;

import com.xiang.traffic.server.db.vo.DbUserVo;

import java.util.Map;


/**
 * 用于实现用户认证方式
 */
public interface UserDatabase {

    /**
     * 执行认证
     *
     * @param group    用户组名
     * @param username 用户名
     * @param password 密码
     * @return 是否认证成功
     */
    boolean doAuth(String group, String username, String password);

    /**
     * 注册用户
     *
     * @param group  用户组名
     * @param userVo 用户vo
     * @return 是否认证成功
     */
    boolean register(String group, DbUserVo userVo);

    /**
     * 删除用户
     *
     * @param group    用户组名
     * @param username 用户名
     * @return 是否删除成功
     */
    boolean delete(String group, String username);

    /**
     * 修改用户密码
     *
     * @param group       用户组名
     * @param username    用户名
     * @param newPassword 新密码
     * @return 是否修改成功
     */
    boolean changePassword(String group, String username, String newPassword);

    public UserGroup getUserGroup(String groupName);


    /**
     * 用户组
     */
    interface UserGroup {

        /**
         * @return 用户组名
         */
        String name();

        public Map<String, DbUserVo> getUserMap();

        int getDefaultWriteLimit();

        int getDefaultReadLimit();

        void setUserMap(Map<String, DbUserVo> userMap);

        public void setDefaultWriteLimit(int defaultWriteLimit);

        public void setDefaultReadLimit(int defaultReadLimit);
    }


    /**
     * 获取用户下行流量总量
     *
     * @param username
     * @return
     */
    public long getUserTrafficCapacity(String username);

    /**
     * 获取用户当前下行流量使用总量
     *
     * @param username
     * @return
     */
    public long getUserTrafficUsage(String username);

    long getUserTrafficRemainingCapacity(String username);
}
