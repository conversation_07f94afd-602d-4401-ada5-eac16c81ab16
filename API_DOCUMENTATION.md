# 📡 API接口文档

## 📋 概述

本文档详细描述了多路复用代理系统的所有API接口，包括认证服务、网关服务和代理服务的完整API规范。

---

## 🔐 认证服务 API (auth-service)

### 基础信息
- **服务名称**: auth-service
- **默认端口**: 8081
- **基础路径**: `/api/auth`
- **协议**: HTTP/HTTPS

### 1. 用户注册

#### 接口信息
```http
POST /api/auth/register
Content-Type: application/json
```

#### 请求参数
```json
{
  "username": "string",     // 用户名，3-20字符，必填
  "password": "string",     // 密码，6-20字符，必填
  "email": "string",        // 邮箱地址，必填
  "nickname": "string"      // 昵称，可选
}
```

#### 响应示例
```json
// 成功响应
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1001,
    "username": "user123",
    "nickname": "用户昵称",
    "email": "<EMAIL>"
  },
  "timestamp": 1704672000000
}

// 失败响应
{
  "code": 400,
  "message": "用户名已存在",
  "data": null,
  "timestamp": 1704672000000
}
```

#### 错误码
| 错误码 | 说明 |
|--------|------|
| 400 | 参数验证失败 |
| 409 | 用户名或邮箱已存在 |
| 500 | 服务器内部错误 |

### 2. 用户名检查

#### 接口信息
```http
GET /api/auth/check-username?username={username}
```

#### 请求参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| username | string | 是 | 要检查的用户名 |

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": true,  // true: 可用, false: 已存在
  "timestamp": 1704672000000
}
```

### 3. 邮箱检查

#### 接口信息
```http
GET /api/auth/check-email?email={email}
```

#### 请求参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| email | string | 是 | 要检查的邮箱地址 |

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": true,  // true: 可用, false: 已存在
  "timestamp": 1704672000000
}
```

### 4. 令牌验证

#### 接口信息
```http
GET /api/auth/validate?token={token}
```

#### 请求参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| token | string | 是 | JWT访问令牌 |

#### 响应示例
```json
// 令牌有效
{
  "code": 200,
  "message": "success",
  "data": {
    "valid": true,
    "userId": 1001,
    "username": "user123",
    "authorities": ["USER"],
    "expiresAt": 1704675600000
  },
  "timestamp": 1704672000000
}

// 令牌无效
{
  "code": 401,
  "message": "令牌无效或已过期",
  "data": {
    "valid": false
  },
  "timestamp": 1704672000000
}
```

### 5. 获取当前用户信息

#### 接口信息
```http
GET /api/auth/current-user
Authorization: Bearer {token}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1001,
    "username": "user123",
    "nickname": "用户昵称",
    "email": "<EMAIL>",
    "createdTime": "2024-01-08T10:00:00Z"
  },
  "timestamp": 1704672000000
}
```

### 6. 根据ID查询用户

#### 接口信息
```http
GET /api/auth/user/{userId}
Authorization: Bearer {token}
```

#### 路径参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | long | 是 | 用户ID |

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1001,
    "username": "user123",
    "nickname": "用户昵称",
    "email": "<EMAIL>",
    "createdTime": "2024-01-08T10:00:00Z"
  },
  "timestamp": 1704672000000
}
```

### 7. 根据用户名查询用户

#### 接口信息
```http
GET /api/auth/user/username/{username}
Authorization: Bearer {token}
```

#### 路径参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| username | string | 是 | 用户名 |

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1001,
    "username": "user123",
    "nickname": "用户昵称",
    "email": "<EMAIL>",
    "createdTime": "2024-01-08T10:00:00Z"
  },
  "timestamp": 1704672000000
}
```

### 8. 批量查询用户信息

#### 接口信息
```http
POST /api/auth/user/batch
Content-Type: application/json
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "userIds": [1001, 1002, 1003],  // 用户ID列表
  "usernames": ["user1", "user2"] // 用户名列表（可选）
}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1001,
      "username": "user123",
      "nickname": "用户昵称",
      "email": "<EMAIL>",
      "createdTime": "2024-01-08T10:00:00Z"
    },
    {
      "id": 1002,
      "username": "user456",
      "nickname": "另一个用户",
      "email": "<EMAIL>",
      "createdTime": "2024-01-08T11:00:00Z"
    }
  ],
  "timestamp": 1704672000000
}
```

---

## 🔑 OAuth2 认证端点

### 1. 获取访问令牌

#### 接口信息
```http
POST /oauth2/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic {client_credentials}
```

#### 请求参数 (密码模式)
```
grant_type=password
username=user123
password=password123
scope=read write
```

#### 请求参数 (授权码模式)
```
grant_type=authorization_code
code=authorization_code
redirect_uri=http://localhost:8080/callback
```

#### 响应示例
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "scope": "read write"
}
```

### 2. 刷新访问令牌

#### 接口信息
```http
POST /oauth2/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic {client_credentials}
```

#### 请求参数
```
grant_type=refresh_token
refresh_token=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应示例
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "scope": "read write"
}
```

### 3. 撤销令牌

#### 接口信息
```http
POST /oauth2/revoke
Content-Type: application/x-www-form-urlencoded
Authorization: Basic {client_credentials}
```

#### 请求参数
```
token=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
token_type_hint=access_token
```

#### 响应示例
```http
HTTP/1.1 200 OK
```

---

## 🌐 网关服务 API (gateway)

### 基础信息
- **服务名称**: gateway
- **默认端口**: 8080
- **协议**: HTTP/HTTPS

### 1. 健康检查

#### 接口信息
```http
GET /actuator/health
```

#### 响应示例
```json
{
  "status": "UP",
  "components": {
    "diskSpace": {
      "status": "UP",
      "details": {
        "total": 499963174912,
        "free": 91943841792,
        "threshold": 10485760
      }
    },
    "ping": {
      "status": "UP"
    }
  }
}
```

### 2. 路由信息

#### 接口信息
```http
GET /actuator/gateway/routes
```

#### 响应示例
```json
[
  {
    "route_id": "auth-service",
    "route_definition": {
      "id": "auth-service",
      "uri": "lb://auth-service",
      "predicates": [
        {
          "name": "Path",
          "args": {
            "pattern": "/api/auth/**"
          }
        }
      ],
      "filters": [
        {
          "name": "StripPrefix",
          "args": {
            "parts": "2"
          }
        }
      ]
    },
    "order": 0
  }
]
```

---

## ⚡ 代理服务 API (netty-multiplex-proxy-service)

### 基础信息
- **服务名称**: netty-multiplex-proxy-service
- **默认端口**: 8082 (HTTP API), 8888 (代理服务)
- **基础路径**: `/api`
- **协议**: HTTP/HTTPS

### 1. 服务状态查询

#### 接口信息
```http
GET /api/status
```

#### 响应示例
```json
{
  "status": "running",
  "version": "2.2.0",
  "startTime": "2024-01-08T10:00:00Z",
  "uptime": "2d 5h 30m",
  "connections": {
    "total": 1250,
    "active": 890,
    "idle": 360
  },
  "sessions": {
    "total": 3400,
    "active": 2100,
    "completed": 1300
  },
  "performance": {
    "requestsPerSecond": 1500,
    "bytesPerSecond": 2048000,
    "averageResponseTime": 45,
    "errorRate": 0.02
  },
  "memory": {
    "used": 256000000,
    "max": 1073741824,
    "usage": 0.24
  }
}
```

### 2. 配置信息查询

#### 接口信息
```http
GET /api/config
```

#### 响应示例
```json
{
  "version": "2.2.0",
  "global": {
    "enableThreadOptimization": true,
    "bossThreads": 2,
    "workerThreads": 8,
    "ioRatio": 70
  },
  "inbounds": [
    {
      "id": "multiplex-inbound-1",
      "type": "multiplex",
      "config": {
        "port": 8888,
        "bossThreads": 1,
        "workerThreads": 4,
        "enableSsl": false
      }
    }
  ],
  "outbounds": [
    {
      "id": "tcp-direct",
      "type": "tcp-direct",
      "config": {
        "connectTimeout": 5000,
        "readTimeout": 30000,
        "writeTimeout": 30000
      }
    }
  ],
  "routing": {
    "defaultOutbound": "tcp-direct",
    "rules": []
  }
}
```

### 3. 健康检查

#### 接口信息
```http
GET /api/health
```

#### 响应示例
```json
{
  "status": "UP",
  "components": {
    "proxyServer": {
      "status": "UP",
      "details": {
        "running": true,
        "port": 8888,
        "connections": 890,
        "sessions": 2100
      }
    },
    "inboundServers": {
      "status": "UP",
      "details": {
        "total": 1,
        "running": 1,
        "stopped": 0
      }
    },
    "connectionPool": {
      "status": "UP",
      "details": {
        "totalConnections": 150,
        "activeConnections": 89,
        "idleConnections": 61,
        "hitRate": 0.85
      }
    },
    "cache": {
      "status": "UP",
      "details": {
        "size": 1250,
        "hitRate": 0.92,
        "evictions": 45
      }
    }
  }
}
```

### 4. 性能指标查询

#### 接口信息
```http
GET /api/metrics
```

#### 响应示例
```json
{
  "timestamp": 1704672000000,
  "uptime": 172800000,
  "connections": {
    "total": 15420,
    "active": 890,
    "peak": 1250,
    "errors": 23
  },
  "sessions": {
    "total": 45600,
    "active": 2100,
    "peak": 3400,
    "completed": 43500,
    "errors": 100
  },
  "traffic": {
    "bytesReceived": 1073741824,
    "bytesSent": 2147483648,
    "requestsPerSecond": 1500,
    "bytesPerSecond": 2048000
  },
  "performance": {
    "averageResponseTime": 45,
    "p50ResponseTime": 35,
    "p95ResponseTime": 120,
    "p99ResponseTime": 250,
    "errorRate": 0.02
  },
  "connectionPool": {
    "totalConnections": 150,
    "activeConnections": 89,
    "idleConnections": 61,
    "hitRate": 0.85,
    "missRate": 0.15,
    "createdConnections": 1250,
    "destroyedConnections": 1100
  },
  "cache": {
    "dnsCache": {
      "size": 850,
      "hitRate": 0.94,
      "missRate": 0.06,
      "evictions": 12
    },
    "geoCache": {
      "size": 400,
      "hitRate": 0.89,
      "missRate": 0.11,
      "evictions": 33
    }
  },
  "filter": {
    "totalRequests": 45600,
    "allowedRequests": 44700,
    "blockedRequests": 900,
    "blockReasons": {
      "geoLocation": 450,
      "maliciousDomain": 250,
      "maliciousKeyword": 150,
      "blacklist": 50
    }
  }
}
```

### 5. 连接管理

#### 获取连接列表
```http
GET /api/connections
```

#### 响应示例
```json
{
  "total": 890,
  "connections": [
    {
      "id": "conn-001",
      "clientAddress": "*************:54321",
      "serverAddress": "0.0.0.0:8888",
      "protocol": "multiplex",
      "status": "active",
      "createdTime": "2024-01-08T10:30:00Z",
      "lastActiveTime": "2024-01-08T12:45:30Z",
      "sessions": 5,
      "bytesReceived": 1048576,
      "bytesSent": 2097152
    }
  ]
}
```

#### 关闭连接
```http
DELETE /api/connections/{connectionId}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "连接已关闭",
  "data": {
    "connectionId": "conn-001",
    "closed": true
  },
  "timestamp": 1704672000000
}
```

### 6. 会话管理

#### 获取会话列表
```http
GET /api/sessions
```

#### 响应示例
```json
{
  "total": 2100,
  "sessions": [
    {
      "id": 12345,
      "connectionId": "conn-001",
      "targetHost": "www.example.com",
      "targetPort": 443,
      "protocol": "tcp",
      "status": "active",
      "createdTime": "2024-01-08T12:30:00Z",
      "lastActiveTime": "2024-01-08T12:45:30Z",
      "bytesReceived": 4096,
      "bytesSent": 8192
    }
  ]
}
```

#### 关闭会话
```http
DELETE /api/sessions/{sessionId}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "会话已关闭",
  "data": {
    "sessionId": 12345,
    "closed": true
  },
  "timestamp": 1704672000000
}
```

### 7. 黑名单管理

#### 获取黑名单
```http
GET /api/blacklist
```

#### 响应示例
```json
{
  "total": 25,
  "hosts": [
    {
      "host": "malicious.example.com",
      "addedTime": "2024-01-08T10:00:00Z",
      "reason": "连接超时",
      "hitCount": 15
    }
  ],
  "stats": {
    "totalHits": 450,
    "totalBlocked": 450,
    "hitRate": 0.01
  }
}
```

#### 添加黑名单
```http
POST /api/blacklist
Content-Type: application/json
```

#### 请求参数
```json
{
  "host": "malicious.example.com",
  "reason": "手动添加"
}
```

#### 删除黑名单
```http
DELETE /api/blacklist/{host}
```

### 8. 配置管理

#### 重载配置
```http
POST /api/config/reload
```

#### 响应示例
```json
{
  "code": 200,
  "message": "配置重载成功",
  "data": {
    "reloaded": true,
    "timestamp": "2024-01-08T12:45:30Z"
  },
  "timestamp": 1704672000000
}
```

#### 更新配置
```http
PUT /api/config
Content-Type: application/json
```

#### 请求参数
```json
{
  "global": {
    "enableThreadOptimization": true,
    "bossThreads": 2,
    "workerThreads": 8
  }
}
```

---

## 🔧 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  },
  "timestamp": 1704672000000
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": null,
  "timestamp": 1704672000000
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      // 数据列表
    ],
    "page": 1,
    "size": 20,
    "total": 100,
    "totalPages": 5
  },
  "timestamp": 1704672000000
}
```

---

## 🔒 认证和授权

### 1. JWT令牌认证

#### 请求头
```http
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 令牌格式
```json
{
  "sub": "user123",
  "aud": ["proxy-client"],
  "iss": "auth-service",
  "exp": 1704675600,
  "iat": 1704672000,
  "authorities": ["USER"],
  "userId": 1001
}
```

### 2. Basic认证

#### 请求头
```http
Authorization: Basic dXNlcjEyMzpwYXNzd29yZDEyMw==
```

### 3. API Key认证

#### 请求头
```http
X-API-Key: your-api-key-here
```

---

## 📊 错误码说明

### HTTP状态码
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |
| 502 | 网关错误 |
| 503 | 服务不可用 |

### 业务错误码
| 错误码 | 说明 |
|--------|------|
| 1001 | 用户名已存在 |
| 1002 | 邮箱已存在 |
| 1003 | 用户不存在 |
| 1004 | 密码错误 |
| 1005 | 令牌无效 |
| 1006 | 令牌已过期 |
| 2001 | 连接不存在 |
| 2002 | 会话不存在 |
| 2003 | 配置加载失败 |
| 2004 | 服务未启动 |

---

## 🧪 API测试示例

### 1. 用户注册和登录流程

```bash
# 1. 注册用户
curl -X POST http://localhost:8081/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123",
    "email": "<EMAIL>",
    "nickname": "测试用户"
  }'

# 2. 获取访问令牌
curl -X POST http://localhost:8081/oauth2/token \
  -H "Authorization: Basic cHJveHktY2xpZW50OnNlY3JldA==" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&username=testuser&password=password123&scope=read write"

# 3. 使用令牌访问API
curl -X GET http://localhost:8081/api/auth/current-user \
  -H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 2. 代理服务监控

```bash
# 1. 查看服务状态
curl -X GET http://localhost:8082/api/status

# 2. 查看性能指标
curl -X GET http://localhost:8082/api/metrics

# 3. 查看健康状态
curl -X GET http://localhost:8082/api/health

# 4. 查看连接列表
curl -X GET http://localhost:8082/api/connections
```

### 3. 配置管理

```bash
# 1. 查看当前配置
curl -X GET http://localhost:8082/api/config

# 2. 重载配置
curl -X POST http://localhost:8082/api/config/reload

# 3. 更新配置
curl -X PUT http://localhost:8082/api/config \
  -H "Content-Type: application/json" \
  -d '{
    "global": {
      "enableThreadOptimization": true,
      "workerThreads": 16
    }
  }'
```

---

## 📚 SDK和客户端库

### Java SDK示例

```java
// 认证客户端
public class AuthClient {
    private final RestTemplate restTemplate;
    private final String baseUrl;
    
    public AuthClient(String baseUrl) {
        this.baseUrl = baseUrl;
        this.restTemplate = new RestTemplate();
    }
    
    public RegisterResponse register(RegisterRequest request) {
        String url = baseUrl + "/api/auth/register";
        ResponseEntity<R<RegisterResponse>> response = restTemplate.postForEntity(
            url, request, new ParameterizedTypeReference<R<RegisterResponse>>() {});
        
        if (response.getBody().getCode() == 200) {
            return response.getBody().getData();
        }
        
        throw new RuntimeException(response.getBody().getMessage());
    }
    
    public TokenResponse getToken(String username, String password) {
        String url = baseUrl + "/oauth2/token";
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setBasicAuth("proxy-client", "secret");
        
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("grant_type", "password");
        params.add("username", username);
        params.add("password", password);
        params.add("scope", "read write");
        
        HttpEntity<MultiValueMap<String, String>> request = 
            new HttpEntity<>(params, headers);
        
        ResponseEntity<TokenResponse> response = restTemplate.postForEntity(
            url, request, TokenResponse.class);
        
        return response.getBody();
    }
}

// 代理服务客户端
public class ProxyClient {
    private final RestTemplate restTemplate;
    private final String baseUrl;
    
    public ProxyClient(String baseUrl) {
        this.baseUrl = baseUrl;
        this.restTemplate = new RestTemplate();
    }
    
    public ProxyStatus getStatus() {
        String url = baseUrl + "/api/status";
        ResponseEntity<ProxyStatus> response = restTemplate.getForEntity(
            url, ProxyStatus.class);
        return response.getBody();
    }
    
    public ProxyMetrics getMetrics() {
        String url = baseUrl + "/api/metrics";
        ResponseEntity<ProxyMetrics> response = restTemplate.getForEntity(
            url, ProxyMetrics.class);
        return response.getBody();
    }
}
```

### Python SDK示例

```python
import requests
from typing import Dict, Any, Optional

class AuthClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
    
    def register(self, username: str, password: str, email: str, 
                nickname: Optional[str] = None) -> Dict[str, Any]:
        url = f"{self.base_url}/api/auth/register"
        data = {
            "username": username,
            "password": password,
            "email": email,
            "nickname": nickname
        }
        
        response = self.session.post(url, json=data)
        response.raise_for_status()
        
        result = response.json()
        if result["code"] == 200:
            return result["data"]
        
        raise Exception(result["message"])
    
    def get_token(self, username: str, password: str) -> Dict[str, Any]:
        url = f"{self.base_url}/oauth2/token"
        
        headers = {
            "Authorization": "Basic cHJveHktY2xpZW50OnNlY3JldA==",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        data = {
            "grant_type": "password",
            "username": username,
            "password": password,
            "scope": "read write"
        }
        
        response = self.session.post(url, headers=headers, data=data)
        response.raise_for_status()
        
        return response.json()

class ProxyClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
    
    def get_status(self) -> Dict[str, Any]:
        url = f"{self.base_url}/api/status"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
    
    def get_metrics(self) -> Dict[str, Any]:
        url = f"{self.base_url}/api/metrics"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()
```

---

**文档版本**: v1.0  
**创建日期**: 2025年1月8日  
**维护者**: AI助手Kiro  
**下次更新**: 根据API变更持续更新