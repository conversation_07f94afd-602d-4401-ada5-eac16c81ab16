package com.xiang.traffic.server.enumeration;

/**
 * 客户端认证方式
 *
 * @<NAME_EMAIL>
 * @since 2021/1/26 19:58
 */
public enum ClientAuthType {

    SIMPLE(0, "simple"), USER(1, "user");

    private final byte messageHeader;

    private final String configValue;

    ClientAuthType(int messageHeader, String configValue) {
        this.messageHeader = (byte) messageHeader;
        this.configValue = configValue;
    }

    public byte getMessageHeader() {
        return messageHeader;
    }

    public String configValue() {
        return configValue;
    }

    public static ClientAuthType configValueOf(String value) {
        for (ClientAuthType type : values()) {
            if (type.configValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
