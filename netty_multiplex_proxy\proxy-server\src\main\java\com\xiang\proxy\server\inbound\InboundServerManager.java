package com.xiang.proxy.server.inbound;

import com.xiang.proxy.server.core.ProxyProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Inbound服务器管理器
 * 统一管理多个Inbound服务器的启动、停止和监控
 */
public class InboundServerManager {
    private static final Logger logger = LoggerFactory.getLogger(InboundServerManager.class);

    private final Map<String, InboundServer> servers = new ConcurrentHashMap<>();
    private final ProxyProcessor proxyProcessor;

    public InboundServerManager(ProxyProcessor proxyProcessor) {
        this.proxyProcessor = proxyProcessor;
    }

    /**
     * 注册Inbound服务器
     */
    public void registerServer(InboundServer server) {
        String serverId = server.getServerId();
        if (servers.containsKey(serverId)) {
            throw new IllegalArgumentException("Server ID already exists: " + serverId);
        }
        
        servers.put(serverId, server);
        logger.info("注册Inbound服务器: {} ({}:{})", 
                serverId, server.getConfig().getBindAddress(), server.getPort());
    }

    /**
     * 注销Inbound服务器
     */
    public CompletableFuture<Void> unregisterServer(String serverId) {
        InboundServer server = servers.remove(serverId);
        if (server == null) {
            return CompletableFuture.completedFuture(null);
        }

        logger.info("注销Inbound服务器: {}", serverId);
        return server.stop();
    }

    /**
     * 启动指定服务器
     */
    public CompletableFuture<Void> startServer(String serverId) {
        InboundServer server = servers.get(serverId);
        if (server == null) {
            return CompletableFuture.failedFuture(
                    new IllegalArgumentException("Server not found: " + serverId));
        }

        return server.start();
    }

    /**
     * 停止指定服务器
     */
    public CompletableFuture<Void> stopServer(String serverId) {
        InboundServer server = servers.get(serverId);
        if (server == null) {
            return CompletableFuture.completedFuture(null);
        }

        return server.stop();
    }

    /**
     * 启动所有服务器
     */
    public CompletableFuture<Void> startAll() {
        logger.info("启动所有Inbound服务器，共{}个", servers.size());
        
        List<CompletableFuture<Void>> futures = servers.values().stream()
                .map(InboundServer::start)
                .collect(Collectors.toList());

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        logger.error("启动部分Inbound服务器失败", throwable);
                    } else {
                        logger.info("所有Inbound服务器启动完成");
                    }
                });
    }

    /**
     * 停止所有服务器
     */
    public CompletableFuture<Void> stopAll() {
        logger.info("停止所有Inbound服务器，共{}个", servers.size());
        
        List<CompletableFuture<Void>> futures = servers.values().stream()
                .map(InboundServer::stop)
                .collect(Collectors.toList());

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        logger.warn("停止部分Inbound服务器时发生异常", throwable);
                    } else {
                        logger.info("所有Inbound服务器停止完成");
                    }
                });
    }

    /**
     * 获取服务器
     */
    public InboundServer getServer(String serverId) {
        return servers.get(serverId);
    }

    /**
     * 获取所有服务器
     */
    public Map<String, InboundServer> getAllServers() {
        return new ConcurrentHashMap<>(servers);
    }

    /**
     * 获取运行中的服务器
     */
    public List<InboundServer> getRunningServers() {
        return servers.values().stream()
                .filter(InboundServer::isRunning)
                .collect(Collectors.toList());
    }

    /**
     * 获取停止的服务器
     */
    public List<InboundServer> getStoppedServers() {
        return servers.values().stream()
                .filter(server -> !server.isRunning())
                .collect(Collectors.toList());
    }

    /**
     * 检查端口是否被占用
     */
    public boolean isPortInUse(int port) {
        return servers.values().stream()
                .anyMatch(server -> server.getPort() == port);
    }

    /**
     * 获取管理器统计信息
     */
    public ManagerStatistics getStatistics() {
        int totalServers = servers.size();
        int runningServers = (int) servers.values().stream().filter(InboundServer::isRunning).count();
        int totalConnections = servers.values().stream()
                .mapToInt(InboundServer::getCurrentConnections)
                .sum();

        return new ManagerStatistics(totalServers, runningServers, totalConnections);
    }

    /**
     * 获取所有服务器的详细统计信息
     */
    public Map<String, InboundServerStatistics> getAllStatistics() {
        return servers.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getStatistics()
                ));
    }

    /**
     * 重置所有服务器的统计信息
     */
    public void resetAllStatistics() {
        servers.values().forEach(InboundServer::resetStatistics);
        logger.info("重置所有Inbound服务器统计信息");
    }

    /**
     * 获取健康状态报告
     */
    public HealthReport getHealthReport() {
        Map<String, InboundServer.ServerHealthStatus> serverHealth = servers.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getHealthStatus()
                ));

        return new HealthReport(serverHealth);
    }


    /**
     * 管理器统计信息
     */
    public static class ManagerStatistics {
        private final int totalServers;
        private final int runningServers;
        private final int totalConnections;

        public ManagerStatistics(int totalServers, int runningServers, int totalConnections) {
            this.totalServers = totalServers;
            this.runningServers = runningServers;
            this.totalConnections = totalConnections;
        }

        public int getTotalServers() {
            return totalServers;
        }

        public int getRunningServers() {
            return runningServers;
        }

        public int getStoppedServers() {
            return totalServers - runningServers;
        }

        public int getTotalConnections() {
            return totalConnections;
        }

        @Override
        public String toString() {
            return String.format("ManagerStatistics{total=%d, running=%d, stopped=%d, connections=%d}",
                    totalServers, runningServers, getStoppedServers(), totalConnections);
        }
    }

    /**
     * 健康状态报告
     */
    public static class HealthReport {
        private final Map<String, InboundServer.ServerHealthStatus> serverHealth;

        public HealthReport(Map<String, InboundServer.ServerHealthStatus> serverHealth) {
            this.serverHealth = serverHealth;
        }

        public Map<String, InboundServer.ServerHealthStatus> getServerHealth() {
            return serverHealth;
        }

        public boolean isAllHealthy() {
            return serverHealth.values().stream()
                    .allMatch(status -> status == InboundServer.ServerHealthStatus.HEALTHY);
        }

        public long getHealthyCount() {
            return serverHealth.values().stream()
                    .filter(status -> status == InboundServer.ServerHealthStatus.HEALTHY)
                    .count();
        }

        public long getUnhealthyCount() {
            return serverHealth.values().stream()
                    .filter(status -> status == InboundServer.ServerHealthStatus.UNHEALTHY)
                    .count();
        }

        @Override
        public String toString() {
            return String.format("HealthReport{total=%d, healthy=%d, unhealthy=%d, allHealthy=%s}",
                    serverHealth.size(), getHealthyCount(), getUnhealthyCount(), isAllHealthy());
        }
    }
}