package com.proxy.client.inbound;

import io.netty.channel.ChannelFuture;

/**
 * 代理接入组件接口
 * 定义统一的代理协议接入规范
 */
public interface ProxyInbound {
    
    /**
     * 获取组件名称
     */
    String getName();
    
    /**
     * 获取协议类型
     */
    ProxyProtocol getProtocol();
    
    /**
     * 获取监听端口
     */
    int getPort();
    
    /**
     * 启动代理接入组件
     * @return ChannelFuture 启动结果
     */
    ChannelFuture start();
    
    /**
     * 停止代理接入组件
     */
    void stop();
    
    /**
     * 检查组件是否正在运行
     */
    boolean isRunning();
    
    /**
     * 获取组件状态信息
     */
    InboundStatus getStatus();
    
    /**
     * 代理协议类型枚举
     */
    enum ProxyProtocol {
        SOCKS5("SOCKS5", "SOCKS5代理协议"),
        HTTP("HTTP", "HTTP CONNECT代理协议"),
        HTTPS("HTTPS", "HTTPS CONNECT代理协议");
        
        private final String name;
        private final String description;
        
        ProxyProtocol(String name, String description) {
            this.name = name;
            this.description = description;
        }
        
        public String getName() {
            return name;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 组件状态信息
     */
    class InboundStatus {
        private final boolean running;
        private final int port;
        private final long startTime;
        private final long activeConnections;
        private final long totalConnections;
        private final String lastError;
        
        public InboundStatus(boolean running, int port, long startTime, 
                           long activeConnections, long totalConnections, String lastError) {
            this.running = running;
            this.port = port;
            this.startTime = startTime;
            this.activeConnections = activeConnections;
            this.totalConnections = totalConnections;
            this.lastError = lastError;
        }
        
        public boolean isRunning() {
            return running;
        }
        
        public int getPort() {
            return port;
        }
        
        public long getStartTime() {
            return startTime;
        }
        
        public long getActiveConnections() {
            return activeConnections;
        }
        
        public long getTotalConnections() {
            return totalConnections;
        }
        
        public String getLastError() {
            return lastError;
        }
        
        @Override
        public String toString() {
            return String.format("InboundStatus{running=%s, port=%d, activeConnections=%d, totalConnections=%d}", 
                running, port, activeConnections, totalConnections);
        }
    }
}
