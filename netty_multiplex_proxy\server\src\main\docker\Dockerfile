#FROM java:openjdk-8u111-alpine
FROM openjdk:19-jdk-alpine3.16

WORKDIR /opt
ADD traffic-server-bin.zip ./
#RUN microdnf install unzip
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk update
RUN apk add unzip

RUN unzip -x -d ./ traffic-server-bin.zip
RUN rm traffic-server-bin.zip
VOLUME /opt/traffic-server/config
RUN chmod 777 /opt/traffic-server/bin/*
# 声明服务运行在1090,7060端口
EXPOSE 1090
EXPOSE 7060
#设置时区
RUN apk add tzdata
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone
# 执行命令
#CMD ["/bin/bash","/opt/traffic-server/bin/startup.sh"]
#java -server -Dflyingsocks.config.location="$FS_CONFIG_DIR" -Xbootclasspath/a:$FS_CONFIG_DIR:../ -cp "$FS_LIB_DIR/*" com.xiang.traffic.server.ServerBoot > /dev/null 2>&1& echo $! > $FS_PID_FILE
ENTRYPOINT ["java","-server","-Dflyingsocks.config.location=/opt/traffic-server/config","-Xbootclasspath/a:/opt/traffic-server/config:../","-cp","/opt/traffic-server/lib/*","com.xiang.traffic.server.ServerBoot","> /dev/null 2>&1& echo $! > /var/run/fs-server.pid"]

