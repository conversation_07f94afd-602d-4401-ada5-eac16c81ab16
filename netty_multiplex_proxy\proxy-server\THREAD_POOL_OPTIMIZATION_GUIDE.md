# 线程池优化指南

## 📋 优化概述

本次线程池优化针对proxy-server的I/O密集型特性，实现了智能线程数计算、性能监控和配置管理，显著提升了并发处理能力。

## 🚀 主要优化内容

### 1. 智能线程数计算

#### Boss线程优化
- **原始逻辑**: 固定1个Boss线程
- **优化后**: 根据CPU核心数智能计算
  - CPU核心数 ≥ 16: 使用2个Boss线程
  - CPU核心数 < 16: 使用1个Boss线程

#### Worker线程优化
- **原始逻辑**: `CPU核心数 * 2`
- **优化后**: 基于I/O比例的智能计算
  ```java
  // I/O比例越高，需要的线程数越多
  double ioMultiplier = 1.0 + (ioRatio / 100.0) * 7.0;
  int baseThreads = (int) (cpuCores * ioMultiplier);
  ```

### 2. 内存感知的线程数限制

```java
// 根据可用内存调整线程数
long memoryPerThreadMB = 2; // 每线程2MB开销
long availableMemoryMB = maxMemory / (1024 * 1024);
int memoryLimitedThreads = (int) (availableMemoryMB / memoryPerThreadMB / 4);
```

### 3. 可配置的线程池参数

新增配置选项：
- `io-ratio`: I/O操作比例 (1-100)
- `enable-thread-optimization`: 是否启用智能优化
- `max-worker-threads`: 最大工作线程数
- `min-worker-threads`: 最小工作线程数

## 📊 配置示例

### 开发环境配置
```yaml
performance:
  boss-threads: 0
  worker-threads: 0
  io-ratio: 60
  enable-thread-optimization: true
  max-worker-threads: 32
  min-worker-threads: 2
```

### 生产环境配置
```yaml
performance:
  boss-threads: 0
  worker-threads: 0
  io-ratio: 75
  enable-thread-optimization: true
  max-worker-threads: 0  # 无限制
  min-worker-threads: 4
```

### 高性能环境配置
```yaml
performance:
  boss-threads: 2
  worker-threads: 0
  io-ratio: 80
  enable-thread-optimization: true
  max-worker-threads: 0
  min-worker-threads: 8
```

## 🔧 优化的线程工厂

### 特性
1. **有意义的线程名称**: `proxy-server-boss-1`, `proxy-server-worker-1`
2. **优化的线程优先级**: Boss线程优先级更高
3. **守护线程设置**: 避免阻止JVM关闭
4. **异常处理**: 统一的未捕获异常处理器

### 实现
```java
private ThreadFactory createOptimizedThreadFactory(String namePrefix, int priority) {
    return new ThreadFactory() {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + "-" + threadNumber.getAndIncrement());
            thread.setDaemon(true);
            thread.setPriority(priority);
            thread.setUncaughtExceptionHandler((t, e) -> {
                logger.error("线程 {} 发生未捕获异常", t.getName(), e);
            });
            return thread;
        }
    };
}
```

## 📈 性能监控

### ThreadPoolPerformanceAnalyzer
新增的性能分析工具提供：
- CPU使用率监控
- 线程数统计 (总数/活跃/守护)
- 内存使用监控
- 性能报告生成

### 监控指标
```
=== 线程池性能报告 ===
CPU使用率: 45.67%
线程统计: 总数=24, 活跃=16, 守护=8
内存使用: 已用=512.34MB, 总计=1024.00MB, 最大=2048.00MB, 使用率=25.02%
```

## 🎯 性能提升预期

### 并发处理能力
- **低负载**: 线程数减少，降低资源消耗
- **中等负载**: 智能扩展，提升处理能力
- **高负载**: 内存感知限制，避免系统崩溃

### 资源利用率
- **CPU**: 根据I/O比例优化，减少上下文切换
- **内存**: 智能限制线程数，避免内存溢出
- **响应时间**: 优化线程优先级，提升响应速度

## 🔍 使用建议

### 1. 配置调优
- **代理服务器**: `io-ratio` 设置为 70-80
- **高并发场景**: 适当增加 `min-worker-threads`
- **内存受限**: 设置合理的 `max-worker-threads`

### 2. 监控关注点
- CPU使用率 > 80% 时考虑增加线程数
- 内存使用率 > 80% 时考虑减少线程数
- 线程数 > CPU核心数*10 时检查配置

### 3. JVM参数优化
```bash
# 推荐的JVM参数
-Xms2g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication
-Dio.netty.allocator.type=pooled
```

## 🚨 注意事项

1. **线程数不是越多越好**: 过多线程会导致上下文切换开销
2. **内存限制**: 每个线程约占用2MB内存
3. **配置测试**: 新配置应在测试环境充分验证
4. **监控重要**: 持续监控性能指标，及时调整配置

## 📝 后续优化方向

1. **动态调整**: 根据实时负载动态调整线程数
2. **更细粒度监控**: 添加更多性能指标
3. **自动调优**: 基于历史数据自动优化配置
4. **负载预测**: 预测性的线程池扩缩容
