package com.xiang.proxy.server.router;

/**
 * 路由统计信息
 */
public class RouteStatistics {
    private final long totalRoutes;
    private final long matchedRoutes;
    private final long unmatchedRoutes;
    private final int ruleCount;
    private final long timestamp;

    public RouteStatistics(long totalRoutes, long matchedRoutes, long unmatchedRoutes, int ruleCount) {
        this.totalRoutes = totalRoutes;
        this.matchedRoutes = matchedRoutes;
        this.unmatchedRoutes = unmatchedRoutes;
        this.ruleCount = ruleCount;
        this.timestamp = System.currentTimeMillis();
    }

    public long getTotalRoutes() {
        return totalRoutes;
    }

    public long getMatchedRoutes() {
        return matchedRoutes;
    }

    public long getUnmatchedRoutes() {
        return unmatchedRoutes;
    }

    public int getRuleCount() {
        return ruleCount;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public double getMatchRate() {
        return totalRoutes > 0 ? (double) matchedRoutes / totalRoutes : 0.0;
    }

    public double getUnmatchRate() {
        return totalRoutes > 0 ? (double) unmatchedRoutes / totalRoutes : 0.0;
    }

    @Override
    public String toString() {
        return String.format("RouteStatistics{total=%d, matched=%d, unmatched=%d, rules=%d, matchRate=%.2f%%}",
                totalRoutes, matchedRoutes, unmatchedRoutes, ruleCount, getMatchRate() * 100);
    }
}