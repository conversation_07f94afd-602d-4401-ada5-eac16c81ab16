package com.xiang.proxy.server.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自适应队列管理器
 * 根据系统负载动态调整队列处理策略
 */
public class AdaptiveQueueManager {
    private static final Logger logger = LoggerFactory.getLogger(AdaptiveQueueManager.class);
    
    private final ProxyProcessor processor;
    private final ProxyMetrics metrics;
    private final ScheduledExecutorService scheduler;
    
    // 自适应参数
    private final AtomicInteger currentBatchSize = new AtomicInteger(1);
    private final AtomicInteger currentPollTimeout = new AtomicInteger(1000);
    
    // 阈值配置
    private final double highLoadThreshold = 0.8; // 队列使用率超过80%认为高负载
    private final double lowLoadThreshold = 0.2;  // 队列使用率低于20%认为低负载
    private final int maxBatchSize = 50;
    private final int minBatchSize = 1;
    private final int maxPollTimeout = 5000;
    private final int minPollTimeout = 100;

    public AdaptiveQueueManager(ProxyProcessor processor, ProxyMetrics metrics) {
        this.processor = processor;
        this.metrics = metrics;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "adaptive-queue-manager");
            t.setDaemon(true);
            return t;
        });
    }

    /**
     * 启动自适应调整
     */
    public void start() {
        scheduler.scheduleAtFixedRate(this::adjustQueueStrategy, 10, 10, TimeUnit.SECONDS);
        logger.info("自适应队列管理器启动");
    }

    /**
     * 停止自适应调整
     */
    public void stop() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            scheduler.shutdownNow();
        }
        logger.info("自适应队列管理器停止");
    }

    /**
     * 调整队列处理策略
     */
    private void adjustQueueStrategy() {
        try {
            ProxyProcessor.QueueStats queueStats = processor.getQueueStats();
            ProxyMetrics.MetricsReport metricsReport = metrics.getReport();
            
            // 计算系统负载指标
            LoadIndicators indicators = calculateLoadIndicators(queueStats, metricsReport);
            
            // 根据负载调整策略
            adjustBatchSize(indicators);
            adjustPollTimeout(indicators);
            
            logger.debug("队列策略调整完成: batchSize={}, pollTimeout={}, load={}",
                    currentBatchSize.get(), currentPollTimeout.get(), indicators);
                    
        } catch (Exception e) {
            logger.error("调整队列策略时发生异常", e);
        }
    }

    /**
     * 计算负载指标
     */
    private LoadIndicators calculateLoadIndicators(ProxyProcessor.QueueStats queueStats, 
                                                 ProxyMetrics.MetricsReport metricsReport) {
        
        // 队列负载率
        double queueLoadRatio = (double) queueStats.getTotalQueueSize() / 
                (processor.getQueueCount() * processor.getConfig().getQueueCapacity());
        
        // 请求处理速率
        double requestRate = metricsReport.getRequestsPerSecond();
        
        // 平均处理时间
        double avgProcessingTime = metricsReport.getAvgProcessingTime();
        
        // 成功率
        double successRate = metricsReport.getSuccessRate();
        
        // 拒绝率
        double rejectionRate = metricsReport.getTotalRequests() > 0 ? 
                (double) metricsReport.getRejectedRequests() / metricsReport.getTotalRequests() : 0;
        
        return new LoadIndicators(queueLoadRatio, requestRate, avgProcessingTime, 
                                successRate, rejectionRate);
    }

    /**
     * 调整批处理大小
     */
    private void adjustBatchSize(LoadIndicators indicators) {
        int currentSize = currentBatchSize.get();
        int newSize = currentSize;
        
        if (indicators.queueLoadRatio > highLoadThreshold) {
            // 高负载：增加批处理大小以提高吞吐量
            newSize = Math.min(currentSize + 5, maxBatchSize);
        } else if (indicators.queueLoadRatio < lowLoadThreshold) {
            // 低负载：减少批处理大小以降低延迟
            newSize = Math.max(currentSize - 2, minBatchSize);
        }
        
        // 考虑处理时间
        if (indicators.avgProcessingTime > 1000) { // 处理时间超过1秒
            newSize = Math.max(newSize - 3, minBatchSize);
        }
        
        if (newSize != currentSize) {
            currentBatchSize.set(newSize);
            logger.info("调整批处理大小: {} -> {}", currentSize, newSize);
        }
    }

    /**
     * 调整轮询超时时间
     */
    private void adjustPollTimeout(LoadIndicators indicators) {
        int currentTimeout = currentPollTimeout.get();
        int newTimeout = currentTimeout;
        
        if (indicators.requestRate > 100) { // 高请求率
            // 减少超时时间，更频繁地检查队列
            newTimeout = Math.max(currentTimeout - 200, minPollTimeout);
        } else if (indicators.requestRate < 10) { // 低请求率
            // 增加超时时间，减少CPU消耗
            newTimeout = Math.min(currentTimeout + 500, maxPollTimeout);
        }
        
        if (newTimeout != currentTimeout) {
            currentPollTimeout.set(newTimeout);
            logger.info("调整轮询超时: {}ms -> {}ms", currentTimeout, newTimeout);
        }
    }

    /**
     * 获取当前批处理大小
     */
    public int getCurrentBatchSize() {
        return currentBatchSize.get();
    }

    /**
     * 获取当前轮询超时
     */
    public int getCurrentPollTimeout() {
        return currentPollTimeout.get();
    }

    /**
     * 负载指标
     */
    private static class LoadIndicators {
        final double queueLoadRatio;
        final double requestRate;
        final double avgProcessingTime;
        final double successRate;
        final double rejectionRate;

        LoadIndicators(double queueLoadRatio, double requestRate, double avgProcessingTime,
                      double successRate, double rejectionRate) {
            this.queueLoadRatio = queueLoadRatio;
            this.requestRate = requestRate;
            this.avgProcessingTime = avgProcessingTime;
            this.successRate = successRate;
            this.rejectionRate = rejectionRate;
        }

        @Override
        public String toString() {
            return String.format("LoadIndicators{queueLoad=%.2f, requestRate=%.2f, " +
                    "avgTime=%.2fms, successRate=%.2f%%, rejectionRate=%.2f%%}",
                    queueLoadRatio, requestRate, avgProcessingTime, successRate * 100, rejectionRate * 100);
        }
    }

    /**
     * 获取调整建议
     */
    public String getAdjustmentRecommendations() {
        ProxyProcessor.QueueStats queueStats = processor.getQueueStats();
        ProxyMetrics.MetricsReport metricsReport = metrics.getReport();
        LoadIndicators indicators = calculateLoadIndicators(queueStats, metricsReport);
        
        StringBuilder recommendations = new StringBuilder();
        
        if (indicators.queueLoadRatio > 0.9) {
            recommendations.append("- 队列负载过高，建议增加队列容量或队列数量\n");
        }
        
        if (indicators.rejectionRate > 0.05) {
            recommendations.append("- 请求拒绝率过高，建议优化处理速度或增加资源\n");
        }
        
        if (indicators.avgProcessingTime > 2000) {
            recommendations.append("- 平均处理时间过长，建议检查网络连接或优化处理逻辑\n");
        }
        
        if (indicators.successRate < 0.95) {
            recommendations.append("- 成功率偏低，建议检查错误日志和网络状况\n");
        }
        
        if (recommendations.length() == 0) {
            recommendations.append("- 系统运行正常，无需调整");
        }
        
        return recommendations.toString();
    }
}