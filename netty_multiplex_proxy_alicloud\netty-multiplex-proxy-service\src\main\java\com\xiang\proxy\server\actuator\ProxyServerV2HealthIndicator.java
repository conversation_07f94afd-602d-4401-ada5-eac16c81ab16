package com.xiang.proxy.server.actuator;

import com.xiang.proxy.server.service.ProxyServerV2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

/**
 * ProxyServerV2 健康检查指示器
 * 为Spring Boot Actuator提供ProxyServerV2的健康状态
 */
@Component
public class ProxyServerV2HealthIndicator implements HealthIndicator {
    
    @Autowired
    private ProxyServerV2Service proxyServerV2Service;
    
    @Override
    public Health health() {
        try {
            String status = proxyServerV2Service.getStatus();
            
            if (proxyServerV2Service.isStartupFailed()) {
                Health.Builder builder = Health.down()
                        .withDetail("status", status)
                        .withDetail("message", "ProxyServerV2 startup failed");
                
                Exception startupException = proxyServerV2Service.getStartupException();
                if (startupException != null) {
                    builder.withException(startupException);
                }
                
                return builder.build();
            }
            
            if (proxyServerV2Service.isStarted()) {
                return Health.up()
                        .withDetail("status", status)
                        .withDetail("message", "ProxyServerV2 is running normally")
                        .build();
            } else {
                return Health.down()
                        .withDetail("status", status)
                        .withDetail("message", "ProxyServerV2 is not running")
                        .build();
            }
        } catch (Exception e) {
            return Health.down()
                    .withDetail("status", "ERROR")
                    .withDetail("message", "Failed to check ProxyServerV2 health")
                    .withException(e)
                    .build();
        }
    }
}