package com.proxy.client.connection;

import com.proxy.client.config.ProxyClientConfigManager;
import com.proxy.client.config.properties.ProxyClientProperties;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.ReadTimeoutException;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 直连处理器
 * 用于处理不通过proxy-server的直接连接
 */
public class DirectConnectionHandler {
    private static final Logger logger = LoggerFactory.getLogger(DirectConnectionHandler.class);
    
    private static DirectConnectionHandler instance;
    private final EventLoopGroup eventLoopGroup;
    private final ConcurrentHashMap<Integer, DirectConnection> connections;
    private final AtomicInteger sessionIdGenerator;

    // 连接统计
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong successfulConnections = new AtomicLong(0);
    private final AtomicLong failedConnections = new AtomicLong(0);
    private final AtomicLong connectionResets = new AtomicLong(0);
    private volatile long lastStatsTime = System.currentTimeMillis();
    
    private DirectConnectionHandler() {
        // Use configurable performance settings
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        ProxyClientProperties.PerformanceProperties perfConfig = configManager.getProperties().getPerformance();
        
        // Calculate thread count for direct connections
        int workerThreads = perfConfig.getDirectConnectionThreads();
        if (workerThreads <= 0) {
            // Automatically calculate thread count based on CPU cores
            workerThreads = Math.max(4, Runtime.getRuntime().availableProcessors());
        }
        
        ThreadFactory threadFactory = new DefaultThreadFactory("proxy-direct-connection");
        this.eventLoopGroup = new NioEventLoopGroup(workerThreads, threadFactory);
        this.connections = new ConcurrentHashMap<>();
        this.sessionIdGenerator = new AtomicInteger(1);
        logger.debug("DirectConnectionHandler initialized with {} worker threads", workerThreads);
    }
    
    public static synchronized DirectConnectionHandler getInstance() {
        if (instance == null) {
            instance = new DirectConnectionHandler();
        }
        return instance;
    }
    
    /**
     * 创建直连会话
     * 
     * @param targetHost 目标主机
     * @param targetPort 目标端口
     * @param handler 会话处理器
     * @return 会话ID
     */
    public int createDirectConnection(String targetHost, int targetPort, SessionHandler handler) {
        int sessionId = sessionIdGenerator.getAndIncrement();
        totalConnections.incrementAndGet();
        logStatsIfNeeded(); // 定期输出统计信息

        logger.info("创建直连会话: sessionId={}, target={}:{}", sessionId, targetHost, targetPort);
        
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(eventLoopGroup)
                .channel(NioSocketChannel.class)
                // Netty性能优化选项
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000) // 5秒连接超时，提高响应速度
                .option(ChannelOption.SO_RCVBUF, 65536) // 接收缓冲区大小
                .option(ChannelOption.SO_SNDBUF, 65536) // 发送缓冲区大小
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT) // 使用池化内存分配器
                .option(ChannelOption.WRITE_BUFFER_WATER_MARK, new WriteBufferWaterMark(8 * 1024, 32 * 1024)) // 设置写缓冲区水位
                .handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) throws Exception {
                        ch.pipeline()
                            .addLast(new ReadTimeoutHandler(60)) // 60秒读取超时
                            .addLast(new DirectConnectionChannelHandler(sessionId, handler));
                    }
                });
        
        // 异步连接
        bootstrap.connect(targetHost, targetPort)
                .addListener((ChannelFutureListener) future -> {
                    if (future.isSuccess()) {
                        Channel channel = future.channel();
                        DirectConnection connection = new DirectConnection(sessionId, channel, handler);
                        connections.put(sessionId, connection);
                        successfulConnections.incrementAndGet();

                        logger.info("直连建立成功: sessionId={}, target={}:{}", sessionId, targetHost, targetPort);
                        handler.onConnectResponse(true, sessionId);
                        
                        // 监听连接关闭
                        channel.closeFuture().addListener(closeFuture -> {
                            connections.remove(sessionId);
                            handler.onClose();
                            logger.info("直连已关闭: sessionId={}, target={}:{}", sessionId, targetHost, targetPort);
                        });
                        
                    } else {
                        failedConnections.incrementAndGet();
                        Throwable cause = future.cause();

                        // 根据失败原因记录不同级别的日志
                        if (cause instanceof java.net.ConnectException) {
                            logger.info("直连建立失败: sessionId={}, target={}:{}, 原因: {}",
                                sessionId, targetHost, targetPort, cause.getMessage());
                        } else {
                            logger.error("直连建立失败: sessionId={}, target={}:{}",
                                sessionId, targetHost, targetPort, cause);
                        }
                        handler.onConnectResponse(false, sessionId);
                    }
                });
        
        return sessionId;
    }
    
    /**
     * 发送数据到直连会话
     * 
     * @param sessionId 会话ID
     * @param data 数据
     */
    public void sendData(int sessionId, byte[] data) {
        DirectConnection connection = connections.get(sessionId);
        if (connection != null && connection.channel.isActive()) {
            ByteBuf buffer = connection.channel.alloc().buffer(data.length);
            buffer.writeBytes(data);
            connection.channel.writeAndFlush(buffer);
            logger.debug("发送数据到直连会话: sessionId={}, dataLength={}", sessionId, data.length);
        } else {
            logger.warn("直连会话不存在或已关闭: sessionId={}", sessionId);
        }
    }
    
    /**
     * 发送数据到直连会话
     * 
     * @param sessionId 会话ID
     * @param data 数据缓冲区
     */
    public void sendData(int sessionId, ByteBuf data) {
        DirectConnection connection = connections.get(sessionId);
        if (connection != null && connection.channel.isActive()) {
            // Use retain to increment reference count since we're passing it to another thread
            data.retain();
            connection.channel.writeAndFlush(data);
            logger.debug("发送数据到直连会话: sessionId={}, dataLength={}", sessionId, data.readableBytes());
        } else {
            // Release the buffer if we're not using it
            data.release();
            logger.warn("直连会话不存在或已关闭: sessionId={}", sessionId);
        }
    }
    
    /**
     * 关闭直连会话
     * 
     * @param sessionId 会话ID
     */
    public void closeConnection(int sessionId) {
        DirectConnection connection = connections.remove(sessionId);
        if (connection != null) {
            connection.channel.close();
            logger.info("关闭直连会话: sessionId={}", sessionId);
        }
    }
    
    /**
     * 获取连接统计信息
     */
    public String getConnectionStats() {
        long total = totalConnections.get();
        long successful = successfulConnections.get();
        long failed = failedConnections.get();
        long resets = connectionResets.get();
        int active = connections.size();

        double successRate = total > 0 ? (double) successful / total * 100 : 0;

        return String.format("直连统计 - 总连接: %d, 成功: %d (%.1f%%), 失败: %d, 重置: %d, 活跃: %d",
            total, successful, successRate, failed, resets, active);
    }

    /**
     * 定期输出统计信息（每5分钟）
     */
    private void logStatsIfNeeded() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastStatsTime > 300000) { // 5分钟
            lastStatsTime = currentTime;
            if (totalConnections.get() > 0) {
                logger.info(getConnectionStats());
            }
        }
    }

    /**
     * 关闭所有连接
     */
    public void shutdown() {
        logger.info("关闭直连处理器...");
        logger.info(getConnectionStats());

        // 关闭所有连接
        for (DirectConnection connection : connections.values()) {
            connection.channel.close();
        }
        connections.clear();

        // 关闭事件循环组
        eventLoopGroup.shutdownGracefully();
    }
    
    /**
     * 直连连接信息
     */
    private static class DirectConnection {
        final int sessionId;
        final Channel channel;
        final SessionHandler handler;
        
        DirectConnection(int sessionId, Channel channel, SessionHandler handler) {
            this.sessionId = sessionId;
            this.channel = channel;
            this.handler = handler;
        }
    }
    
    /**
     * 直连通道处理器
     */
    private static class DirectConnectionChannelHandler extends ChannelInboundHandlerAdapter {
        private final int sessionId;
        private final SessionHandler handler;
        
        DirectConnectionChannelHandler(int sessionId, SessionHandler handler) {
            this.sessionId = sessionId;
            this.handler = handler;
        }
        
        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            if (msg instanceof ByteBuf) {
                ByteBuf buffer = (ByteBuf) msg;
                // Increase reference count since we're passing it to another component
                buffer.retain();
                try {
                    byte[] data = new byte[buffer.readableBytes()];
                    buffer.readBytes(data);
                    handler.onData(data);
                    logger.debug("从直连会话接收数据: sessionId={}, dataLength={}", sessionId, data.length);
                } finally {
                    buffer.release();
                }
            }
        }
        
        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            logger.debug("直连通道已断开: sessionId={}", sessionId);
            handler.onClose();
            super.channelInactive(ctx);
        }
        
        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            // 根据异常类型进行不同的处理和日志记录
            if (cause instanceof java.net.SocketException) {
                String message = cause.getMessage();
                if (message != null && message.contains("Connection reset")) {
                    // Connection reset 是常见的网络异常，降低日志级别并统计
                    DirectConnectionHandler.getInstance().connectionResets.incrementAndGet();
                    logger.debug("直连连接被重置: sessionId={}, 原因: {}", sessionId, message);
                } else if (message != null && (message.contains("Connection timed out") ||
                          message.contains("Connection refused"))) {
                    // 连接超时或拒绝，使用INFO级别
                    logger.info("直连连接失败: sessionId={}, 原因: {}", sessionId, message);
                } else {
                    // 其他Socket异常
                    logger.warn("直连Socket异常: sessionId={}, 原因: {}", sessionId, message);
                }
            } else if (cause instanceof java.io.IOException) {
                // IO异常，通常是网络问题
                logger.info("直连IO异常: sessionId={}, 原因: {}", sessionId, cause.getMessage());
            } else if (cause instanceof java.nio.channels.ClosedChannelException) {
                // 通道已关闭，这是正常情况
                logger.debug("直连通道已关闭: sessionId={}", sessionId);
            } else if (cause instanceof ReadTimeoutException) {
                // 读取超时，通常是长时间无数据传输
                logger.debug("直连读取超时: sessionId={}", sessionId);
            } else {
                // 其他未知异常，记录完整堆栈
                logger.error("直连通道异常: sessionId={}", sessionId, cause);
            }

            // 确保通道关闭
            if (ctx.channel().isActive()) {
                ctx.close();
            }
        }
    }
}
