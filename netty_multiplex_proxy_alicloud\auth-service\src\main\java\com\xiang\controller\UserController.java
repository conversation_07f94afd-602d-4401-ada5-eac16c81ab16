package com.xiang.controller;

import com.xiang.chat.core.R;
import com.xiang.dto.UserInfo;
import com.xiang.entity.UserEntity;
import com.xiang.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    
    /**
     * 根据用户ID获取用户信息
     */
    @GetMapping("/{userId}")
    public R<UserInfo> getUserInfo(@PathVariable Long userId) {
        try {
            UserEntity user = userService.findById(userId);
            if (user == null) {
                return R.error("用户不存在");
            }
            
            UserInfo userInfo = convertToUserInfo(user);
            return R.success(userInfo);
            
        } catch (Exception e) {
            log.error("获取用户信息失败: userId={}", userId, e);
            return R.error("获取用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量获取用户信息
     */
    @PostMapping("/batch")
    public R<List<UserInfo>> getUserInfoBatch(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Object> userIdObjects = (List<Object>) request.get("userIds");
            
            if (userIdObjects == null || userIdObjects.isEmpty()) {
                return R.success(List.of());
            }
            
            Set<Long> userIds = userIdObjects.stream()
                .map(obj -> Long.valueOf(obj.toString()))
                .collect(Collectors.toSet());
            
            List<UserInfo> userInfoList = userIds.stream()
                .map(userService::findById)
                .filter(user -> user != null)
                .map(this::convertToUserInfo)
                .collect(Collectors.toList());
            
            return R.success(userInfoList);
            
        } catch (Exception e) {
            log.error("批量获取用户信息失败: request={}", request, e);
            return R.error("批量获取用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据用户名获取用户信息
     */
    @GetMapping("/username/{username}")
    public R<UserInfo> getUserInfoByUsername(@PathVariable String username) {
        try {
            UserEntity user = userService.findByUsername(username);
            if (user == null) {
                return R.error("用户不存在");
            }
            
            UserInfo userInfo = convertToUserInfo(user);
            return R.success(userInfo);
            
        } catch (Exception e) {
            log.error("根据用户名获取用户信息失败: username={}", username, e);
            return R.error("获取用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 转换UserEntity为UserInfo
     */
    private UserInfo convertToUserInfo(UserEntity user) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setNickname(user.getNickname());
        userInfo.setEmail(user.getEmail());
        userInfo.setRole(user.getRole());
        userInfo.setAvatarUrl(user.getAvatarUrl());
        return userInfo;
    }
}
