package com.proxy.client.config.properties;

import com.proxy.client.config.annotation.ConfigurationProperties;
import com.proxy.client.filter.AddressFilter;

import java.util.ArrayList;
import java.util.List;

/**
 * 代理客户端配置属性
 */
@ConfigurationProperties
public class ProxyClientProperties {

    private FilterProperties filter = new FilterProperties();
    private ProxyProperties proxy = new ProxyProperties();
    private LocalProperties local = new LocalProperties();
    private AuthProperties auth = new AuthProperties();
    private InboundProperties inbound = new InboundProperties();
    private SslProperties ssl = new SslProperties();
    private OnlineDataSourcesProperties onlineDataSources = new OnlineDataSourcesProperties();
    private PerformanceProperties performance = new PerformanceProperties();
    private QueueProperties queue = new QueueProperties();
    private NacosProperties nacos = new NacosProperties();

    /**
     * 默认构造函数 - 为GraalVM Native Image反射访问提供支持
     */
    public ProxyClientProperties() {
        // 字段已在声明时初始化
    }

    // Getters and Setters
    public FilterProperties getFilter() {
        return filter;
    }

    public void setFilter(FilterProperties filter) {
        this.filter = filter;
    }

    public ProxyProperties getProxy() {
        return proxy;
    }

    public void setProxy(ProxyProperties proxy) {
        this.proxy = proxy;
    }

    public LocalProperties getLocal() {
        return local;
    }

    public void setLocal(LocalProperties local) {
        this.local = local;
    }

    public AuthProperties getAuth() {
        return auth;
    }

    public void setAuth(AuthProperties auth) {
        this.auth = auth;
    }

    public InboundProperties getInbound() {
        return inbound;
    }

    public void setInbound(InboundProperties inbound) {
        this.inbound = inbound;
    }

    public SslProperties getSsl() {
        return ssl;
    }

    public void setSsl(SslProperties ssl) {
        this.ssl = ssl;
    }

    public OnlineDataSourcesProperties getOnlineDataSources() {
        return onlineDataSources;
    }

    public void setOnlineDataSources(OnlineDataSourcesProperties onlineDataSources) {
        this.onlineDataSources = onlineDataSources;
    }

    public PerformanceProperties getPerformance() {
        return performance;
    }

    public void setPerformance(PerformanceProperties performance) {
        this.performance = performance;
    }

    public QueueProperties getQueue() {
        return queue;
    }

    public void setQueue(QueueProperties queue) {
        this.queue = queue;
    }

    public NacosProperties getNacos() {
        return nacos;
    }

    public void setNacos(NacosProperties nacos) {
        this.nacos = nacos;
    }

    /**
     * 过滤器配置
     */
    public static class FilterProperties {
        private AddressFilter.FilterMode mode = AddressFilter.FilterMode.ALL_PROXY;

        public FilterProperties() {
            // 字段已在声明时初始化
        }

        public AddressFilter.FilterMode getMode() {
            return mode;
        }

        public void setMode(AddressFilter.FilterMode mode) {
            this.mode = mode;
        }
    }

    /**
     * 代理服务器配置
     */
    public static class ProxyProperties {
        private ServerProperties server = new ServerProperties();

        public ProxyProperties() {
            // 字段已在声明时初始化
        }

        public ServerProperties getServer() {
            return server;
        }

        public void setServer(ServerProperties server) {
            this.server = server;
        }

        public static class ServerProperties {
            private String host = "localhost";
            private int port = 8888;

            public ServerProperties() {
                // 字段已在声明时初始化
            }

            public String getHost() {
                return host;
            }

            public void setHost(String host) {
                this.host = host;
            }

            public int getPort() {
                return port;
            }

            public void setPort(int port) {
                this.port = port;
            }
        }
    }

    /**
     * 本地配置
     */
    public static class LocalProperties {
        private int port = 1081;

        public LocalProperties() {
            // 字段已在声明时初始化
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }
    }

    /**
     * 认证配置
     */
    public static class AuthProperties {
        private boolean enable = false;
        private String username = "admin";
        private String password = "password";
        private TimeoutProperties timeout = new TimeoutProperties();

        public AuthProperties() {
            // 字段已在声明时初始化
        }

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public TimeoutProperties getTimeout() {
            return timeout;
        }

        public void setTimeout(TimeoutProperties timeout) {
            this.timeout = timeout;
        }

        public static class TimeoutProperties {
            private int seconds = 30;

            public TimeoutProperties() {
                // 字段已在声明时初始化
            }

            public int getSeconds() {
                return seconds;
            }

            public void setSeconds(int seconds) {
                this.seconds = seconds;
            }
        }
    }

    /**
     * 接入器配置
     */
    public static class InboundProperties {
        private List<InboundItemProperties> socks5 = new ArrayList<>();
        private List<InboundItemProperties> http = new ArrayList<>();

        public InboundProperties() {
            // 字段已在声明时初始化
        }

        public List<InboundItemProperties> getSocks5() {
            return socks5;
        }

        public void setSocks5(List<InboundItemProperties> socks5) {
            this.socks5 = socks5;
        }

        public List<InboundItemProperties> getHttp() {
            return http;
        }

        public void setHttp(List<InboundItemProperties> http) {
            this.http = http;
        }
    }

    /**
     * 单个接入器配置
     */
    public static class InboundItemProperties {
        private String name;
        private int port;
        private boolean enabled = true;
        private String description;

        public InboundItemProperties() {
            // 字段已在声明时初始化
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        @Override
        public String toString() {
            return "InboundItemProperties{" +
                    "name='" + name + '\'' +
                    ", port=" + port +
                    ", enabled=" + enabled +
                    ", description='" + description + '\'' +
                    '}';
        }
    }

    /**
     * 向后兼容的旧配置
     */
    public static class LegacyProperties {
        private LegacyInboundProperties inbound = new LegacyInboundProperties();

        public LegacyProperties() {
            // 字段已在声明时初始化
        }

        public LegacyInboundProperties getInbound() {
            return inbound;
        }

        public void setInbound(LegacyInboundProperties inbound) {
            this.inbound = inbound;
        }

        public static class LegacyInboundProperties {
            private String enabled = "socks5";
            private LegacyInboundItemProperties socks5 = new LegacyInboundItemProperties();
            private LegacyInboundItemProperties http = new LegacyInboundItemProperties();

            public LegacyInboundProperties() {
                // 字段已在声明时初始化
            }

            public String getEnabled() {
                return enabled;
            }

            public void setEnabled(String enabled) {
                this.enabled = enabled;
            }

            public LegacyInboundItemProperties getSocks5() {
                return socks5;
            }

            public void setSocks5(LegacyInboundItemProperties socks5) {
                this.socks5 = socks5;
            }

            public LegacyInboundItemProperties getHttp() {
                return http;
            }

            public void setHttp(LegacyInboundItemProperties http) {
                this.http = http;
            }

            public static class LegacyInboundItemProperties {
                private int port = 1081;
                private boolean enabled = false;

                public LegacyInboundItemProperties() {
                    // 字段已在声明时初始化
                }

                public int getPort() {
                    return port;
                }

                public void setPort(int port) {
                    this.port = port;
                }

                public boolean isEnabled() {
                    return enabled;
                }

                public void setEnabled(boolean enabled) {
                    this.enabled = enabled;
                }
            }
        }
    }

    /**
     * SSL/TLS配置
     */
    public static class SslProperties {
        private boolean enable = false;
        private boolean trustAll = false;
        private String trustStorePath = "";
        private String trustStorePassword = "changeit";
        private String trustStoreType = "PKCS12";
        private String keyStorePath = "";
        private String keyStorePassword = "changeit";
        private String keyStoreType = "PKCS12";
        private String[] protocols = {"TLSv1.2", "TLSv1.3"};
        private String[] cipherSuites = {};
        private boolean verifyHostname = true;
        private int handshakeTimeoutSeconds = 30;

        public SslProperties() {
            // 字段已在声明时初始化
        }

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public boolean isTrustAll() {
            return trustAll;
        }

        public void setTrustAll(boolean trustAll) {
            this.trustAll = trustAll;
        }

        public String getTrustStorePath() {
            return trustStorePath;
        }

        public void setTrustStorePath(String trustStorePath) {
            this.trustStorePath = trustStorePath;
        }

        public String getTrustStorePassword() {
            return trustStorePassword;
        }

        public void setTrustStorePassword(String trustStorePassword) {
            this.trustStorePassword = trustStorePassword;
        }

        public String getTrustStoreType() {
            return trustStoreType;
        }

        public void setTrustStoreType(String trustStoreType) {
            this.trustStoreType = trustStoreType;
        }

        public String getKeyStorePath() {
            return keyStorePath;
        }

        public void setKeyStorePath(String keyStorePath) {
            this.keyStorePath = keyStorePath;
        }

        public String getKeyStorePassword() {
            return keyStorePassword;
        }

        public void setKeyStorePassword(String keyStorePassword) {
            this.keyStorePassword = keyStorePassword;
        }

        public String getKeyStoreType() {
            return keyStoreType;
        }

        public void setKeyStoreType(String keyStoreType) {
            this.keyStoreType = keyStoreType;
        }

        public String[] getProtocols() {
            return protocols;
        }

        public void setProtocols(String[] protocols) {
            this.protocols = protocols;
        }

        public String[] getCipherSuites() {
            return cipherSuites;
        }

        public void setCipherSuites(String[] cipherSuites) {
            this.cipherSuites = cipherSuites;
        }

        public boolean isVerifyHostname() {
            return verifyHostname;
        }

        public void setVerifyHostname(boolean verifyHostname) {
            this.verifyHostname = verifyHostname;
        }

        public int getHandshakeTimeoutSeconds() {
            return handshakeTimeoutSeconds;
        }

        public void setHandshakeTimeoutSeconds(int handshakeTimeoutSeconds) {
            this.handshakeTimeoutSeconds = handshakeTimeoutSeconds;
        }
    }

    /**
     * 在线数据源配置
     */
    public static class OnlineDataSourcesProperties {
        private java.util.List<String> chinaIpRanges = java.util.Arrays.asList(
                "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt",
                "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
        );

        public OnlineDataSourcesProperties() {
            // 字段已在声明时初始化
        }

        public java.util.List<String> getChinaIpRanges() {
            return chinaIpRanges;
        }

        public void setChinaIpRanges(java.util.List<String> chinaIpRanges) {
            this.chinaIpRanges = chinaIpRanges;
        }
    }

    /**
     * 性能配置
     */
    public static class PerformanceProperties {
        private int workerThreads = 0; // 0表示自动计算
        private int directConnectionThreads = 0; // 0表示自动计算

        public PerformanceProperties() {
            // 字段已在声明时初始化
        }

        public int getWorkerThreads() {
            return workerThreads;
        }

        public void setWorkerThreads(int workerThreads) {
            this.workerThreads = workerThreads;
        }

        public int getDirectConnectionThreads() {
            return directConnectionThreads;
        }

        public void setDirectConnectionThreads(int directConnectionThreads) {
            this.directConnectionThreads = directConnectionThreads;
        }
    }

    /**
     * 队列配置
     */
    public static class QueueProperties {
        private int capacity = 10000;
        private int batchSize = 100;
        private long flushIntervalMs = 10;
        private RetryProperties retry = new RetryProperties();
        private MonitoringProperties monitoring = new MonitoringProperties();

        public QueueProperties() {
            // 字段已在声明时初始化
        }

        public int getCapacity() {
            return capacity;
        }

        public void setCapacity(int capacity) {
            this.capacity = capacity;
        }

        public int getBatchSize() {
            return batchSize;
        }

        public void setBatchSize(int batchSize) {
            this.batchSize = batchSize;
        }

        public long getFlushIntervalMs() {
            return flushIntervalMs;
        }

        public void setFlushIntervalMs(long flushIntervalMs) {
            this.flushIntervalMs = flushIntervalMs;
        }

        public RetryProperties getRetry() {
            return retry;
        }

        public void setRetry(RetryProperties retry) {
            this.retry = retry;
        }

        public MonitoringProperties getMonitoring() {
            return monitoring;
        }

        public void setMonitoring(MonitoringProperties monitoring) {
            this.monitoring = monitoring;
        }

        /**
         * 重试配置
         */
        public static class RetryProperties {
            private int maxAttempts = 3;
            private long delayMs = 1000;

            public RetryProperties() {
                // 字段已在声明时初始化
            }

            public int getMaxAttempts() {
                return maxAttempts;
            }

            public void setMaxAttempts(int maxAttempts) {
                this.maxAttempts = maxAttempts;
            }

            public long getDelayMs() {
                return delayMs;
            }

            public void setDelayMs(long delayMs) {
                this.delayMs = delayMs;
            }
        }

        /**
         * 监控配置
         */
        public static class MonitoringProperties {
            private boolean enabled = true;
            private int reportIntervalSeconds = 30;
            private int warningThreshold = 80;
            private int errorThreshold = 95;

            public MonitoringProperties() {
                // 字段已在声明时初始化
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

            public int getReportIntervalSeconds() {
                return reportIntervalSeconds;
            }

            public void setReportIntervalSeconds(int reportIntervalSeconds) {
                this.reportIntervalSeconds = reportIntervalSeconds;
            }

            public int getWarningThreshold() {
                return warningThreshold;
            }

            public void setWarningThreshold(int warningThreshold) {
                this.warningThreshold = warningThreshold;
            }

            public int getErrorThreshold() {
                return errorThreshold;
            }

            public void setErrorThreshold(int errorThreshold) {
                this.errorThreshold = errorThreshold;
            }
        }
    }

    /**
     * Nacos 服务发现配置
     */
    public static class NacosProperties {
        private boolean enabled = false;
        private String serverAddr = "127.0.0.1:8848";
        private String namespace = "";
        private String serviceName = "main-multiplex";
        private String groupName = "DEFAULT_GROUP";
        private String username= "nacos";
        private String password= "nacos";

        public NacosProperties() {
            // 字段已在声明时初始化
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getServerAddr() {
            return serverAddr;
        }

        public void setServerAddr(String serverAddr) {
            this.serverAddr = serverAddr;
        }

        public String getNamespace() {
            return namespace;
        }

        public void setNamespace(String namespace) {
            this.namespace = namespace;
        }

        public String getServiceName() {
            return serviceName;
        }

        public void setServiceName(String serviceName) {
            this.serviceName = serviceName;
        }

        public String getGroupName() {
            return groupName;
        }

        public void setGroupName(String groupName) {
            this.groupName = groupName;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }
}
