# Connection Reset Handling Improvement

## Problem Analysis

The current error logs show "Connection reset by peer" errors occurring in the proxy server's multiplex system:

```
15:13:42.508 [main-multiplex-worker] ERROR c.p.s.o.i.TcpDirectOutboundHandler - 发送数据失败: 030a61d3-a7d6-4295-9aa5-a01ebbea64fb, bytes=16413
java.io.IOException: Connection reset by peer
```

This error indicates that the remote server forcibly closed the TCP connection, which is a common network issue that can occur due to:

1. **Server-side connection limits**: Target server reached max connections
2. **Network timeouts**: Connection idle for too long
3. **Server restart/maintenance**: Target server went down
4. **Firewall/NAT issues**: Network infrastructure dropped the connection
5. **Application-level issues**: Target application closed connection unexpectedly

## Current Issues

1. **Duplicate error logging**: Same connection failure generates multiple error logs
2. **Poor error classification**: All connection resets treated the same way
3. **No retry mechanism**: Failed connections aren't retried appropriately
4. **Connection pool pollution**: Failed connections may remain in pool
5. **Client notification delay**: Client doesn't get timely error feedback

## Improvement Strategy

### 1. Enhanced Error Classification

Categorize connection reset errors by cause:
- **Temporary network issues** (should retry)
- **Server overload** (should backoff)
- **Permanent failures** (should not retry)

### 2. Intelligent Retry Logic

Implement exponential backoff for retryable errors:
- First retry: immediate
- Second retry: 100ms delay
- Third retry: 500ms delay
- Max retries: 3 attempts

### 3. Connection Pool Health Management

- Remove failed connections from pool immediately
- Implement connection health checks
- Add connection age limits

### 4. Improved Error Reporting

- Reduce duplicate error logs
- Provide more context in error messages
- Add metrics for connection failure patterns

## Implementation Plan

### Phase 1: Error Classification Enhancement
- Update `TcpDirectOutboundHandler` error handling
- Add connection failure categorization
- Implement retry decision logic

### Phase 2: Connection Pool Integration
- Enhance connection validation
- Add proactive health checks
- Implement connection replacement

### Phase 3: Monitoring and Metrics
- Add connection failure metrics
- Implement alerting thresholds
- Create diagnostic tools

## Expected Benefits

1. **Reduced error noise**: Fewer duplicate error logs
2. **Better reliability**: Automatic retry for transient failures
3. **Improved performance**: Faster failure detection and recovery
4. **Enhanced monitoring**: Better visibility into connection health
5. **User experience**: More responsive error handling

## Configuration Options

```yaml
outbound:
  connection-handling:
    retry:
      enabled: true
      max-attempts: 3
      initial-delay-ms: 100
      max-delay-ms: 5000
      backoff-multiplier: 2.0
    
    health-check:
      enabled: true
      interval-seconds: 30
      timeout-ms: 5000
    
    error-suppression:
      enabled: true
      duplicate-window-ms: 10000
      max-logs-per-window: 3
```

This improvement will make the proxy server more resilient to network issues and provide better diagnostic information for troubleshooting.