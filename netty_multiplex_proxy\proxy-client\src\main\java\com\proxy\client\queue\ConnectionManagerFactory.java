package com.proxy.client.queue;

import com.proxy.client.connection.ConnectionManager;
import com.proxy.client.connection.DynamicConnectionManager;
import com.proxy.client.discovery.NacosServiceDiscovery;
import com.proxy.client.config.ProxyClientConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 连接管理器工厂
 * 负责创建和管理队列化的连接管理器实例
 */
public class ConnectionManagerFactory {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionManagerFactory.class);

    private static volatile QueuedConnectionManager queuedInstance;
    private static volatile ConnectionManager originalInstance;

    /**
     * 获取队列化的连接管理器实例
     */
    public static QueuedConnectionManager getQueuedInstance(String proxyServerHost, int proxyServerPort) {
        if (queuedInstance == null) {
            synchronized (ConnectionManagerFactory.class) {
                if (queuedInstance == null) {
                    ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();

                    // 检查是否启用 Nacos 服务发现
                    if (configManager.isNacosEnabled()) {
                        logger.info("启用 Nacos 服务发现，创建动态连接管理器");
                        queuedInstance = createNacosConnectionManager(configManager);
                    } else {
                        logger.info("使用静态配置，创建标准队列化连接管理器");
                        // 创建原始ConnectionManager实例
                        originalInstance = ConnectionManager.getInstance(proxyServerHost, proxyServerPort);
                        // 创建队列化包装器
                        queuedInstance = new QueuedConnectionManager(originalInstance);
                    }

                    logger.info("创建队列化连接管理器实例: {}:{}", proxyServerHost, proxyServerPort);
                }
            }
        }
        return queuedInstance;
    }

    /**
     * 创建基于 Nacos 的动态连接管理器
     */
    private static QueuedConnectionManager createNacosConnectionManager(ProxyClientConfigManager configManager) {
        try {
            // 创建 Nacos 服务发现实例
            NacosServiceDiscovery serviceDiscovery = new NacosServiceDiscovery(
                    configManager.getNacosServerAddr(),
                    configManager.getNacosNamespace(),
                    configManager.getNacosServiceName(),
                    configManager.getNacosGroupName(),
                    configManager.getUsername(),
                    configManager.getPassword()
            );

            // 创建动态连接管理器
            DynamicConnectionManager dynamicManager = new DynamicConnectionManager(serviceDiscovery);

            logger.info("成功创建 Nacos 动态连接管理器 - 服务: {}, 分组: {}",
                    configManager.getNacosServiceName(), configManager.getNacosGroupName());

            return dynamicManager;

        } catch (Exception e) {
            logger.error("创建 Nacos 动态连接管理器失败，回退到静态配置", e);

            // 回退到静态配置
            originalInstance = ConnectionManager.getInstance(
                    configManager.getProxyServerHost(),
                    configManager.getProxyServerPort()
            );
            return new QueuedConnectionManager(originalInstance);
        }
    }

    /**
     * 获取队列化的连接管理器实例（已初始化）
     */
    public static QueuedConnectionManager getQueuedInstance() {
        if (queuedInstance == null) {
            throw new IllegalStateException("QueuedConnectionManager not initialized");
        }
        return queuedInstance;
    }

    /**
     * 获取原始的连接管理器实例
     */
    public static ConnectionManager getOriginalInstance() {
        if (originalInstance == null) {
            throw new IllegalStateException("ConnectionManager not initialized");
        }
        return originalInstance;
    }

    /**
     * 检查是否已初始化
     */
    public static boolean isInitialized() {
        return queuedInstance != null;
    }

    /**
     * 关闭所有实例
     */
    public static void shutdown() {
        if (queuedInstance != null) {
            queuedInstance.stop();
            queuedInstance = null;
        }
        if (originalInstance != null) {
            originalInstance = null;
        }
        logger.info("ConnectionManagerFactory已关闭");
    }

    // ========== 配置读取方法 ==========

    private static int getQueueCapacity(ProxyClientConfigManager configManager) {
        // 从配置中读取队列容量，默认10000
        try {
            // 这里可以扩展配置项
            return 10000;
        } catch (Exception e) {
            logger.warn("读取队列容量配置失败，使用默认值", e);
            return 10000;
        }
    }

    private static int getBatchSize(ProxyClientConfigManager configManager) {
        // 从配置中读取批处理大小，默认100
        try {
            return 100;
        } catch (Exception e) {
            logger.warn("读取批处理大小配置失败，使用默认值", e);
            return 100;
        }
    }

    private static long getFlushInterval(ProxyClientConfigManager configManager) {
        // 从配置中读取刷新间隔，默认10ms
        try {
            return 10;
        } catch (Exception e) {
            logger.warn("读取刷新间隔配置失败，使用默认值", e);
            return 10;
        }
    }

    private static int getRetryAttempts(ProxyClientConfigManager configManager) {
        // 从配置中读取重试次数，默认3次
        try {
            return 3;
        } catch (Exception e) {
            logger.warn("读取重试次数配置失败，使用默认值", e);
            return 3;
        }
    }

    private static long getRetryDelay(ProxyClientConfigManager configManager) {
        // 从配置中读取重试延迟，默认1000ms
        try {
            return 1000;
        } catch (Exception e) {
            logger.warn("读取重试延迟配置失败，使用默认值", e);
            return 1000;
        }
    }
}