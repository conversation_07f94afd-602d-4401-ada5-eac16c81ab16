# 🏗️ 微服务架构指南

## 📋 概述

本文档详细介绍了多路复用代理系统的微服务架构设计，包括服务拆分、通信机制、数据管理和部署策略。

---

## 🏛️ 整体架构设计

### 🎯 架构原则
- **单一职责**: 每个服务专注于特定的业务领域
- **服务自治**: 服务独立开发、部署和扩展
- **去中心化**: 避免单点故障，提高系统可用性
- **容错设计**: 服务间故障隔离和优雅降级

### 🔧 技术选型
```yaml
服务框架: Spring Boot 2.7+
服务发现: Spring Cloud Gateway
配置管理: Spring Cloud Config
负载均衡: Spring Cloud LoadBalancer
熔断器: Resilience4j
API网关: Spring Cloud Gateway
```

---

## 🚀 服务架构详解

### 1. 🔐 认证服务 (auth-service)

#### 服务职责
- 用户认证和授权
- JWT令牌管理
- 用户信息管理
- OAuth2授权服务器

#### 技术栈
```yaml
框架: Spring Boot + Spring Security
认证: OAuth2 + JWT
数据库: MySQL + MyBatis Plus
缓存: Redis (可选)
```

#### 核心API
```java
// 用户注册
POST /api/auth/register
{
  "username": "user123",
  "password": "password",
  "email": "<EMAIL>",
  "nickname": "用户昵称"
}

// 获取访问令牌
POST /oauth2/token
{
  "grant_type": "password",
  "username": "user123",
  "password": "password",
  "client_id": "proxy-client"
}

// 验证令牌
GET /api/auth/validate?token=jwt_token

// 用户信息查询
GET /api/auth/user/{userId}
GET /api/auth/user/username/{username}
```

#### 数据模型
```sql
-- 用户表
CREATE TABLE user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    nickname VARCHAR(50),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0
);

-- OAuth2客户端表
CREATE TABLE oauth2_registered_client (
    id VARCHAR(100) PRIMARY KEY,
    client_id VARCHAR(100) NOT NULL,
    client_secret VARCHAR(200),
    client_name VARCHAR(200) NOT NULL,
    authorization_grant_types VARCHAR(1000) NOT NULL,
    redirect_uris VARCHAR(1000),
    scopes VARCHAR(1000) NOT NULL
);
```

### 2. 🌐 网关服务 (gateway)

#### 服务职责
- API路由和转发
- 负载均衡
- 认证集成
- 限流和熔断
- 监控和日志

#### 技术栈
```yaml
框架: Spring Cloud Gateway
路由: 动态路由配置
过滤器: 自定义过滤器链
认证: JWT令牌验证
监控: Spring Boot Actuator
```

#### 路由配置
```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=2
            
        - id: proxy-service
          uri: lb://netty-multiplex-proxy-service
          predicates:
            - Path=/api/proxy/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
```

#### 自定义过滤器
```java
@Component
public class AuthenticationFilter implements GlobalFilter, Ordered {
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 跳过认证的路径
        if (isExcludedPath(request.getPath().value())) {
            return chain.filter(exchange);
        }
        
        // JWT令牌验证
        String token = extractToken(request);
        if (token == null || !validateToken(token)) {
            return unauthorized(exchange);
        }
        
        return chain.filter(exchange);
    }
    
    @Override
    public int getOrder() {
        return -100; // 高优先级
    }
}
```

### 3. ⚡ 代理服务 (netty-multiplex-proxy-service)

#### 服务职责
- 高性能代理转发
- 多路复用协议处理
- 连接池管理
- 性能监控
- 安全过滤

#### 技术栈
```yaml
网络框架: Netty 4.1.100
协议: 自研多路复用协议V2
监控: 30+项性能指标
安全: SSL/TLS + 地理位置过滤
配置: 多环境配置管理
```

#### 核心组件
```java
// 代理服务器V2
@Component
public class ProxyServerV2 {
    private InboundServerManager inboundServerManager;
    private ProxyProcessor proxyProcessor;
    private Router router;
    
    public CompletableFuture<Void> start() {
        // 启动入站服务器
        setupInboundServers();
        // 配置出站处理器
        setupOutbounds();
        // 设置路由规则
        setupRoutes();
        // 启动监控
        startMonitoring();
        
        return CompletableFuture.completedFuture(null);
    }
}

// 代理处理器
@Component
public class ProxyProcessor {
    
    public CompletableFuture<ProxyResponse> processRequest(ProxyRequest request) {
        // 路由请求
        RouteResult routeResult = routeRequest(request);
        
        // 处理出站连接
        return handleOutbound(request, routeResult);
    }
}
```

#### REST API
```java
@RestController
@RequestMapping("/api")
public class ProxyServerV2Controller {
    
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "running");
        status.put("connections", getCurrentConnections());
        status.put("sessions", getActiveSessions());
        status.put("uptime", getUptime());
        return ResponseEntity.ok(status);
    }
    
    @GetMapping("/config")
    public ResponseEntity<Object> getConfig() {
        return ResponseEntity.ok(configManager.getProperties());
    }
    
    @GetMapping("/health")
    public ResponseEntity<Object> getHealthReport() {
        return ResponseEntity.ok(generateHealthReport());
    }
}
```

### 4. 📚 公共组件 (common)

#### 组件职责
- 统一响应格式
- 全局异常处理
- 业务异常定义
- 工具类和常量

#### 核心组件
```java
// 统一响应格式
@Data
public class R<T> implements Serializable {
    private int code;
    private String message;
    private T data;
    private long timestamp;
    
    public static <T> R<T> success() {
        return new R<>(200, "success", null, System.currentTimeMillis());
    }
    
    public static <T> R<T> success(T data) {
        return new R<>(200, "success", data, System.currentTimeMillis());
    }
    
    public static <T> R<T> error(String message) {
        return new R<>(500, message, null, System.currentTimeMillis());
    }
}

// 全局异常处理
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public R<Void> handleBusinessException(BusinessException e) {
        return R.error(e.getMessage());
    }
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors()
            .stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining(", "));
        return R.error("参数验证失败: " + message);
    }
    
    @ExceptionHandler(Exception.class)
    public R<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return R.error("系统异常，请稍后重试");
    }
}
```

---

## 🔄 服务间通信

### 1. 🌐 HTTP通信

#### API调用
```java
@Service
public class AuthServiceClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public UserInfo getUserInfo(Long userId) {
        String url = "http://auth-service/api/user/" + userId;
        R<UserInfo> response = restTemplate.getForObject(url, 
            new ParameterizedTypeReference<R<UserInfo>>() {});
        
        if (response != null && response.getCode() == 200) {
            return response.getData();
        }
        
        throw new BusinessException("获取用户信息失败");
    }
}
```

#### 负载均衡配置
```java
@Configuration
public class LoadBalancerConfig {
    
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
    
    @Bean
    public ReactorLoadBalancer<ServiceInstance> reactorServiceInstanceLoadBalancer(
            Environment environment,
            LoadBalancerClientFactory loadBalancerClientFactory) {
        String name = environment.getProperty(LoadBalancerClientFactory.PROPERTY_NAME);
        return new RoundRobinLoadBalancer(
            loadBalancerClientFactory.getLazyProvider(name, ServiceInstanceListSupplier.class),
            name);
    }
}
```

### 2. 📨 异步消息通信

#### 消息队列配置
```yaml
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: proxy-system
      auto-offset-reset: earliest
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.JsonSerializer
```

#### 事件发布和订阅
```java
// 事件发布
@Service
public class UserEventPublisher {
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    public void publishUserRegistered(UserRegisteredEvent event) {
        rabbitTemplate.convertAndSend("user.exchange", "user.registered", event);
    }
}

// 事件监听
@Component
public class UserEventListener {
    
    @RabbitListener(queues = "user.registered.queue")
    public void handleUserRegistered(UserRegisteredEvent event) {
        log.info("用户注册事件: {}", event);
        // 处理用户注册后的业务逻辑
    }
}
```

---

## 💾 数据管理策略

### 1. 🗄️ 数据库设计

#### 数据库分离
```yaml
认证服务数据库:
  - 用户表 (user)
  - OAuth2客户端表 (oauth2_registered_client)
  - 权限表 (permission)
  - 角色表 (role)

代理服务数据库:
  - 配置表 (proxy_config)
  - 统计表 (proxy_stats)
  - 黑名单表 (blacklist)
  - 白名单表 (whitelist)

网关服务数据库:
  - 路由配置表 (gateway_route)
  - 限流配置表 (rate_limit_config)
  - 监控数据表 (gateway_metrics)
```

#### 数据一致性
```java
// 分布式事务管理
@Service
@Transactional
public class UserRegistrationService {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private NotificationService notificationService;
    
    @GlobalTransactional // Seata分布式事务
    public void registerUser(RegisterRequest request) {
        // 创建用户
        UserEntity user = userService.createUser(request);
        
        // 发送通知
        notificationService.sendWelcomeEmail(user.getEmail());
        
        // 发布事件
        eventPublisher.publishUserRegistered(new UserRegisteredEvent(user));
    }
}
```

### 2. 📊 缓存策略

#### Redis缓存配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
```

#### 缓存使用
```java
@Service
public class UserCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Cacheable(value = "user", key = "#userId")
    public UserInfo getUserInfo(Long userId) {
        return userService.findById(userId);
    }
    
    @CacheEvict(value = "user", key = "#userId")
    public void evictUserCache(Long userId) {
        // 缓存失效
    }
    
    @CachePut(value = "user", key = "#user.id")
    public UserInfo updateUser(UserInfo user) {
        return userService.update(user);
    }
}
```

---

## 🔧 配置管理

### 1. ⚙️ 配置中心

#### Spring Cloud Config
```yaml
spring:
  cloud:
    config:
      server:
        git:
          uri: https://github.com/your-org/config-repo
          search-paths: configs/{application}
          default-label: main
      client:
        config:
          uri: http://config-server:8888
          name: ${spring.application.name}
          profile: ${spring.profiles.active}
```

#### 配置文件结构
```
config-repo/
├── configs/
│   ├── auth-service/
│   │   ├── auth-service-dev.yml
│   │   ├── auth-service-prod.yml
│   │   └── auth-service-test.yml
│   ├── gateway/
│   │   ├── gateway-dev.yml
│   │   ├── gateway-prod.yml
│   │   └── gateway-test.yml
│   └── netty-multiplex-proxy-service/
│       ├── proxy-service-dev.yml
│       ├── proxy-service-prod.yml
│       └── proxy-service-test.yml
```

### 2. 🔄 配置热更新

#### 配置刷新
```java
@RestController
@RefreshScope
public class ConfigController {
    
    @Value("${app.feature.enabled:false}")
    private boolean featureEnabled;
    
    @PostMapping("/refresh")
    public String refresh() {
        // 触发配置刷新
        return "配置已刷新";
    }
}
```

---

## 📊 监控和观测

### 1. 🔍 服务监控

#### Actuator端点
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

#### 自定义健康检查
```java
@Component
public class CustomHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        // 检查数据库连接
        if (isDatabaseHealthy()) {
            return Health.up()
                .withDetail("database", "连接正常")
                .build();
        } else {
            return Health.down()
                .withDetail("database", "连接异常")
                .build();
        }
    }
}
```

### 2. 📈 指标收集

#### Micrometer指标
```java
@Service
public class MetricsService {
    
    private final Counter requestCounter;
    private final Timer requestTimer;
    private final Gauge activeConnections;
    
    public MetricsService(MeterRegistry meterRegistry) {
        this.requestCounter = Counter.builder("proxy.requests.total")
            .description("代理请求总数")
            .register(meterRegistry);
            
        this.requestTimer = Timer.builder("proxy.request.duration")
            .description("代理请求耗时")
            .register(meterRegistry);
            
        this.activeConnections = Gauge.builder("proxy.connections.active")
            .description("活跃连接数")
            .register(meterRegistry, this, MetricsService::getActiveConnections);
    }
    
    public void recordRequest(Duration duration) {
        requestCounter.increment();
        requestTimer.record(duration);
    }
}
```

### 3. 📋 链路追踪

#### Sleuth配置
```yaml
spring:
  sleuth:
    sampler:
      probability: 1.0
    zipkin:
      base-url: http://zipkin-server:9411
```

#### 自定义Span
```java
@Service
public class ProxyService {
    
    @NewSpan("proxy-request")
    public void processRequest(@SpanTag("request.id") String requestId) {
        // 处理代理请求
    }
}
```

---

## 🚀 部署策略

### 1. 🐳 容器化部署

#### Docker Compose
```yaml
version: '3.8'
services:
  auth-service:
    image: proxy-system/auth-service:latest
    ports:
      - "8081:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=*******************************
    depends_on:
      - mysql
      - redis
      
  gateway:
    image: proxy-system/gateway:latest
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
    depends_on:
      - auth-service
      
  proxy-service:
    image: proxy-system/proxy-service:latest
    ports:
      - "8888:8888"
      - "8082:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
    depends_on:
      - auth-service
      
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=auth_db
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 2. ☸️ Kubernetes部署

#### 服务部署清单
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: proxy-system/auth-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
spec:
  selector:
    app: auth-service
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
```

### 3. 🔄 CI/CD流水线

#### Jenkins Pipeline
```groovy
pipeline {
    agent any
    
    stages {
        stage('Checkout') {
            steps {
                git 'https://github.com/your-org/proxy-system.git'
            }
        }
        
        stage('Build') {
            steps {
                sh 'mvn clean package -DskipTests'
            }
        }
        
        stage('Test') {
            steps {
                sh 'mvn test'
            }
        }
        
        stage('Docker Build') {
            steps {
                script {
                    def services = ['auth-service', 'gateway', 'netty-multiplex-proxy-service']
                    services.each { service ->
                        sh "docker build -t proxy-system/${service}:${BUILD_NUMBER} ${service}/"
                    }
                }
            }
        }
        
        stage('Deploy') {
            steps {
                sh 'kubectl apply -f k8s/'
            }
        }
    }
}
```

---

## 🛡️ 安全考虑

### 1. 🔐 服务间认证

#### JWT令牌验证
```java
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String token = extractToken(request);
        if (token != null && validateToken(token)) {
            Authentication auth = getAuthentication(token);
            SecurityContextHolder.getContext().setAuthentication(auth);
        }
        
        filterChain.doFilter(request, response);
    }
}
```

### 2. 🌐 网络安全

#### HTTPS配置
```yaml
server:
  port: 8443
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12
    key-alias: tomcat
```

### 3. 🔒 数据加密

#### 敏感数据加密
```java
@Service
public class EncryptionService {
    
    @Value("${app.encryption.key}")
    private String encryptionKey;
    
    public String encrypt(String plainText) {
        // AES加密实现
        return AESUtil.encrypt(plainText, encryptionKey);
    }
    
    public String decrypt(String encryptedText) {
        // AES解密实现
        return AESUtil.decrypt(encryptedText, encryptionKey);
    }
}
```

---

## 📈 性能优化

### 1. ⚡ 连接池优化

#### 数据库连接池
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 30000
```

### 2. 💾 缓存优化

#### 多级缓存
```java
@Service
public class CacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private final Cache<String, Object> localCache = Caffeine.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build();
    
    public Object get(String key) {
        // L1缓存 - 本地缓存
        Object value = localCache.getIfPresent(key);
        if (value != null) {
            return value;
        }
        
        // L2缓存 - Redis缓存
        value = redisTemplate.opsForValue().get(key);
        if (value != null) {
            localCache.put(key, value);
            return value;
        }
        
        return null;
    }
}
```

---

## 📚 最佳实践

### 1. 🏗️ 架构设计
- **服务边界清晰**: 每个服务职责单一，边界明确
- **数据一致性**: 使用事件驱动架构保证最终一致性
- **故障隔离**: 服务间故障不相互影响
- **可观测性**: 完善的监控、日志和链路追踪

### 2. 🔧 开发规范
- **API设计**: RESTful API设计规范
- **错误处理**: 统一的错误码和异常处理
- **日志规范**: 结构化日志和统一格式
- **代码质量**: 代码审查和自动化测试

### 3. 🚀 运维管理
- **自动化部署**: CI/CD流水线自动化
- **配置管理**: 外部化配置和环境隔离
- **监控告警**: 完善的监控指标和告警机制
- **容量规划**: 基于监控数据的容量规划

---

**文档版本**: v1.0  
**创建日期**: 2025年1月8日  
**维护者**: AI助手Kiro  
**下次更新**: 根据架构演进持续更新