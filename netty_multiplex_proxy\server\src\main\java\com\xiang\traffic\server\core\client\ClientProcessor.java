package com.xiang.traffic.server.core.client;

import com.xiang.traffic.AbstractComponent;
import com.xiang.traffic.ComponentException;
import com.xiang.traffic.ConfigManager;
import com.xiang.traffic.encrypt.EncryptProvider;
import com.xiang.traffic.encrypt.EncryptSupport;
import com.xiang.traffic.encrypt.JksSSLEncryptProvider;
import com.xiang.traffic.encrypt.OpenSSLEncryptProvider;
import com.xiang.traffic.protocol.AuthRequestMessage;
import com.xiang.traffic.server.ServerConfig;
import com.xiang.traffic.server.core.OpenSSLConfig;
import com.xiang.traffic.server.core.ProxyProcessor;
import com.xiang.traffic.server.db.user.UserDatabase;
import com.xiang.traffic.server.enumeration.ClientAuthType;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 用于处理FS客户端连接的组件
 *
 * <AUTHOR>
 * @date 2024/6/6 15:19
 */
public class ClientProcessor extends AbstractComponent<ProxyProcessor> {

    public ClientProcessor(ProxyProcessor proxyProcessor) {
        super("ClientProcessor", Objects.requireNonNull(proxyProcessor));
    }

    @Override
    protected void initInternal() {
        ServerConfig.Node node = parent.getServerConfig();
        EncryptProvider provider;
        switch (node.encryptType) {
            case OpenSSL:
                provider = EncryptSupport.lookupProvider("OpenSSL");
                break;
            case JKS:
                provider = EncryptSupport.lookupProvider("JKS");
                break;
            case NONE:
                provider = null;
                break;
            default: {
                log.error("Unsupport encrypt type {}", node.encryptType);
                System.exit(1);
                return;
            }
        }

        if (provider != null) {
            if (provider instanceof OpenSSLEncryptProvider) {
                ConfigManager<?> manager = parent.getParentComponent().getConfigManager();
                OpenSSLConfig cfg = new OpenSSLConfig(manager, node.getName());
                manager.registerConfig(cfg);

                Map<String, Object> params = new HashMap<>(8);

                try (InputStream certIs = cfg.openRootCertStream()) {
                    byte[] buf = new byte[10240];
                    int r = certIs.read(buf);
                    byte[] file = new byte[r];
                    System.arraycopy(buf, 0, file, 0, r);
                    addComponent(new CertRequestProcessor(this, file));
                    ByteArrayInputStream bais = new ByteArrayInputStream(file);
                    params.put("file.cert", bais);
                } catch (IOException e) {
                    log.error("Exception occur at CA cert MD5 calcuate", e);
                    System.exit(1);
                }

                try {
                    params.put("client", false);
                    params.put("file.cert.root", cfg.openRootCertStream());
                    params.put("file.key", cfg.openKeyStream());
                } catch (IOException e) {
                    log.error("Can not open CA file stream", e);
                    System.exit(1);
                }

                try {
                    provider.initialize(params);
                } catch (Exception e) {
                    log.error("Load OpenSSL Module occur a exception", e);
                    throw new ComponentException(e);
                }

            } else if (provider instanceof JksSSLEncryptProvider) {
                throw new ComponentException("Unsupport JKS encrypt method");
            } else {
                throw new ComponentException("Unsupport other encrypt method");
            }
        }

        addComponent(new ProxyRequestProcessor(this, provider));

        super.initInternal();
    }

    @Override
    protected void startInternal() {
        super.startInternal();
    }

    @Override
    protected void stopInternal() {
        super.stopInternal();
    }

    /**
     * 对客户端认证报文进行比对
     *
     * @param authRequestMessage 客户端认证报文
     * @return 是否通过认证
     */
    boolean doAuth(AuthRequestMessage authRequestMessage) {
        ServerConfig.Node n = parent.getServerConfig();
        if (n.authType.getMessageHeader() != authRequestMessage.getAuthType()) { //如果认证方式不匹配
            return false;
        }

        Map<String, String> parameters = authRequestMessage.getParameters();

        if (n.authType == ClientAuthType.SIMPLE) {
            String password = parameters.get("password");
            return Objects.equals(n.getArgument("password"), password);
        } else if (n.authType == ClientAuthType.USER) {
            String group = n.getArgument("group");
            UserDatabase db = parent.getParentComponent().getUserDatabase();
            return db.doAuth(group, authRequestMessage.getParameter("user"), authRequestMessage.getParameter("pass"));
        }

        return false;
    }
}
