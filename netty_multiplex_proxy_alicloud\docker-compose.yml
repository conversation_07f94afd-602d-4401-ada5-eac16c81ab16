version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: springcloud-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: springcloud_chat
      MYSQL_USER: springcloud
      MYSQL_PASSWORD: 123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./configs/mysql/conf:/etc/mysql/conf.d
      - ./configs/mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - proxy-network
  # Nacos注册中心
  nacos:
    image: nacos/nacos-server:v2.1.2
    container_name: springcloud-nacos
    restart: always
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos_config
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: 123456
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
      JVM_XMS: 512m
      JVM_XMX: 512m
      JVM_XMN: 256m
      JAVA_OPT_EXT: "-Dmanagement.metrics.binders.processor.enabled=false -Djdk.attach.allowAttachSelf=true"
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_data:/home/<USER>/data
      - nacos_logs:/home/<USER>/logs
    depends_on:
      - mysql
    networks:
      - proxy-network
  proxy-server:
    build:
      context: ../
      dockerfile: netty_multiplex_proxy_alicloud/netty-multiplex-proxy-service/Dockerfile
    image: proxy-server:1.0.0
    container_name: proxy-server
    restart: unless-stopped
    ports:
      - "8888:8888"
    volumes:
      # 配置文件挂载 - 读写挂载
      # 将宿主机的 ../netty_multiplex_proxy/configs/production/server 目录挂载到容器的 /app/config 目录
      - ../netty_multiplex_proxy/configs/production/server:/app/config

      # 日志文件挂载 - 读写挂载
      # 将宿主机的 ./logs 目录挂载到容器的 /app/logs 目录
      - ./logs:/app/logs
    depends_on:
      - nacos
    networks:
      - proxy-network
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

networks:
  proxy-network:
    driver: bridge
# 数据卷
volumes:
  mysql_data:
    driver: local
  nacos_data:
    driver: local
  nacos_logs:
    driver: local