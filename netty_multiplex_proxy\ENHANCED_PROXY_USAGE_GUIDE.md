# 增强代理服务器使用指南

## 概述

本指南介绍如何在项目中使用改进的proxy-server队列机制和连接复用功能。这些改进基于server中的DispatchProcessor实现，并进行了优化。

## 快速开始

### 1. 基本使用

```java
import com.xiang.proxy.server.factory.EnhancedProxyProcessorFactory;
import com.xiang.proxy.server.core.EnhancedProxyProcessor;
import com.xiang.proxy.server.router.DefaultRouter;

// 创建路由器
DefaultRouter router = new DefaultRouter();
// ... 添加路由规则

// 创建增强代理处理器（推荐方式）
EnhancedProxyProcessor processor = EnhancedProxyProcessorFactory.createRecommended(router);

// 启动处理器
processor.start();

// 使用完毕后关闭
processor.shutdown();
```

### 2. 使用工厂类创建不同类型的处理器

```java
// 根据系统资源自动选择最佳配置
ProxyProcessor processor = EnhancedProxyProcessorFactory.createRecommended(router);

// 或者手动选择类型
EnhancedProxyProcessor enhanced = EnhancedProxyProcessorFactory.createEnhanced(router);
BatchProxyProcessor batch = EnhancedProxyProcessorFactory.createBatch(router);
ProxyProcessor standard = EnhancedProxyProcessorFactory.createStandard(router);

// 使用预定义配置
ProxyProcessor highPerf = EnhancedProxyProcessorFactory.create(
    EnhancedProxyProcessorFactory.ProcessorType.ENHANCED, 
    router, 
    EnhancedProxyProcessorFactory.createHighPerformanceConfig()
);
```

## 配置选项

### 1. 系统信息查看

```java
// 查看系统信息和推荐配置
EnhancedProxyProcessorFactory.printSystemInfo();
```

### 2. 自定义配置

```java
ProxyProcessorConfig config = new ProxyProcessorConfig();

// 基础配置
config.setQueueCount(8);                    // 队列数量
config.setQueueCapacity(2000);              // 每个队列容量
config.setWorkerThreadPrefix("my-worker-"); // 工作线程前缀

// 批处理配置
config.setBatchSize(15);                    // 批处理大小
config.setBatchTimeoutMs(50);               // 批处理超时

// 启用自适应调整
config.setEnableAdaptiveAdjustment(true);

// 创建处理器
EnhancedProxyProcessor processor = new EnhancedProxyProcessor(router, config);
```

## 监控和统计

### 1. 连接统计

```java
// 获取连接管理器统计
Map<String, ActiveConnectionManager.ConnectionStats> connectionStats = processor.getConnectionStats();

for (Map.Entry<String, ActiveConnectionManager.ConnectionStats> entry : connectionStats.entrySet()) {
    String queueName = entry.getKey();
    ActiveConnectionManager.ConnectionStats stats = entry.getValue();
    
    System.out.printf("队列 %s: 总连接=%d, 活跃连接=%d, 总消息=%d, 队列消息=%d%n",
        queueName, stats.getTotalConnections(), stats.getActiveConnections(),
        stats.getTotalMessages(), stats.getQueuedMessages());
}
```

### 2. 连接池统计

```java
// 如果使用了EnhancedConnectionPool
EnhancedConnectionPool pool = new EnhancedConnectionPool();
EnhancedConnectionPool.PoolStats poolStats = pool.getStats();

System.out.printf("连接池统计: 总连接=%d, 池中连接=%d, 活跃连接=%d, 命中率=%.2f%%%n",
    poolStats.totalConnections, poolStats.pooledConnections, 
    poolStats.activeConnections, poolStats.getHitRate());
```

### 3. 自适应管理器建议

```java
// 获取自适应调整建议
if (processor.getAdaptiveManager() != null) {
    String recommendations = processor.getAdaptiveManager().getAdjustmentRecommendations();
    System.out.println("自适应建议:\n" + recommendations);
}
```

## 路由配置

### 1. 基本路由规则

```java
DefaultRouter router = new DefaultRouter();

// HTTP路由
RouteRule httpRule = new RouteRule("http-rule", "HTTP流量路由", 10, "http-outbound");
httpRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PROTOCOL, RouteMatcher.Operator.EQUALS, "HTTP"));
httpRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PORT, RouteMatcher.Operator.EQUALS, "80"));
router.addRoute(httpRule);

// HTTPS路由
RouteRule httpsRule = new RouteRule("https-rule", "HTTPS流量路由", 20, "https-outbound");
httpsRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PROTOCOL, RouteMatcher.Operator.EQUALS, "HTTPS"));
httpsRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PORT, RouteMatcher.Operator.EQUALS, "443"));
router.addRoute(httpsRule);

// 默认路由
RouteRule defaultRule = new RouteRule("default-rule", "默认路由", 999, "default-outbound");
router.addRoute(defaultRule);
```

## 性能调优

### 1. 根据业务场景选择配置

```java
// 高吞吐量场景
ProxyProcessorConfig highThroughputConfig = EnhancedProxyProcessorFactory.createHighPerformanceConfig();

// 低延迟场景
ProxyProcessorConfig lowLatencyConfig = EnhancedProxyProcessorFactory.createLowLatencyConfig();

// 内存受限场景
ProxyProcessorConfig memoryOptimizedConfig = EnhancedProxyProcessorFactory.createMemoryOptimizedConfig();
```

### 2. 监控关键指标

```java
// 定期监控
ScheduledExecutorService monitor = Executors.newSingleThreadScheduledExecutor();
monitor.scheduleAtFixedRate(() -> {
    // 检查队列使用率
    var queueStats = processor.getQueueStats();
    if (queueStats.getAverageQueueUsage() > 0.8) {
        logger.warn("队列使用率过高: {}%", queueStats.getAverageQueueUsage() * 100);
    }
    
    // 检查连接复用效率
    var connectionStats = processor.getConnectionStats();
    long totalConnections = connectionStats.values().stream()
        .mapToLong(ActiveConnectionManager.ConnectionStats::getTotalConnections)
        .sum();
    long activeConnections = connectionStats.values().stream()
        .mapToLong(ActiveConnectionManager.ConnectionStats::getActiveConnections)
        .sum();
        
    if (activeConnections > 0) {
        double reuseRate = (double) totalConnections / activeConnections;
        logger.info("连接复用率: {:.2f}", reuseRate);
    }
}, 30, 30, TimeUnit.SECONDS);
```

## 最佳实践

### 1. 启动顺序

```java
// 1. 创建路由器并配置规则
DefaultRouter router = new DefaultRouter();
// ... 配置路由规则

// 2. 创建并配置出站处理器
// ... 注册出站处理器

// 3. 创建代理处理器
EnhancedProxyProcessor processor = EnhancedProxyProcessorFactory.createRecommended(router);

// 4. 启动处理器
processor.start();

// 5. 启动监控（可选）
startMonitoring(processor);
```

### 2. 优雅关闭

```java
// 注册关闭钩子
Runtime.getRuntime().addShutdownHook(new Thread(() -> {
    logger.info("开始关闭代理服务器...");
    
    try {
        // 停止接收新请求
        // ... 停止入站处理器
        
        // 等待现有请求处理完成
        Thread.sleep(5000);
        
        // 关闭代理处理器
        processor.shutdown();
        
        // 关闭连接池
        if (connectionPool != null) {
            connectionPool.shutdown();
        }
        
        logger.info("代理服务器已优雅关闭");
    } catch (Exception e) {
        logger.error("关闭代理服务器时发生异常", e);
    }
}));
```

### 3. 错误处理

```java
// 处理请求时的错误处理
CompletableFuture<ProxyResponse> future = processor.processRequest(request);
future.whenComplete((response, throwable) -> {
    if (throwable != null) {
        logger.error("请求处理失败: {}", request.getRequestId(), throwable);
        // 发送错误响应给客户端
        sendErrorResponse(request.getClientChannel(), throwable);
    } else if (!response.isSuccess()) {
        logger.warn("请求处理失败: {} - {}", request.getRequestId(), response.getMessage());
        // 发送失败响应给客户端
        sendFailureResponse(request.getClientChannel(), response.getMessage());
    } else {
        logger.debug("请求处理成功: {}", request.getRequestId());
        // 处理成功响应
        handleSuccessResponse(response);
    }
});
```

## 故障排查

### 1. 常见问题

**问题**: 连接复用效率低
**解决**: 检查连接标识是否正确，确保相同目标的请求使用相同的clientId

**问题**: 队列积压严重
**解决**: 增加队列数量或工作线程数，启用自适应管理

**问题**: 内存使用过高
**解决**: 减少队列容量，启用连接清理，使用内存优化配置

### 2. 日志配置

```xml
<!-- logback.xml -->
<logger name="com.xiang.proxy.server.core.ActiveConnectionManager" level="DEBUG"/>
<logger name="com.xiang.proxy.server.core.EnhancedProxyProcessor" level="INFO"/>
<logger name="com.xiang.proxy.server.pool.EnhancedConnectionPool" level="INFO"/>
```

## 迁移指南

### 从标准ProxyProcessor迁移

```java
// 原有代码
ProxyProcessor processor = new ProxyProcessor(router, config);

// 迁移后
EnhancedProxyProcessor processor = new EnhancedProxyProcessor(router, config);
// 或者使用工厂类
EnhancedProxyProcessor processor = EnhancedProxyProcessorFactory.createEnhanced(router, config);
```

### 配置兼容性

所有原有的ProxyProcessorConfig配置都兼容，新增的连接复用功能会自动启用。

## 总结

通过使用增强的代理处理器，您可以获得：

1. **更好的连接复用**: 减少连接建立开销
2. **消息队列缓存**: 提高连接建立前的消息处理效率
3. **智能监控**: 详细的性能统计和调优建议
4. **自适应调整**: 根据负载自动优化参数
5. **更好的资源管理**: 自动清理超时连接，防止内存泄漏

建议在生产环境中使用`EnhancedProxyProcessorFactory.createRecommended()`方法，它会根据系统资源自动选择最佳配置。
