package com.xiang.proxy.server.inbound.impl.multiplex;

import com.xiang.proxy.server.auth.AuthConfig;
import com.xiang.proxy.server.auth.AuthManager;
import com.xiang.proxy.server.core.ProxyProcessor;
import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.inbound.InboundHandler;
import com.xiang.proxy.server.metrics.AdvancedMetrics;
import com.xiang.proxy.server.metrics.PerformanceMetrics;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 多路复用Inbound处理器 V2
 * 适配现有的多路复用协议到新的组件化架构
 * 专注于协议解析和数据转发，连接管理由outbound处理器负责
 * 集成了认证、黑名单、地理位置过滤、性能监控等功能
 */
public class MultiplexInboundHandler implements InboundHandler {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexInboundHandler.class);

    // 全局客户端连接计数器
    private static final AtomicLong CLIENT_CONNECTION_COUNTER = new AtomicLong(0);

    private final ProxyProcessor proxyProcessor;
    private final Map<Channel, MultiplexSession> sessions = new ConcurrentHashMap<>();

    // 添加性能指标收集实例
    private final AdvancedMetrics advancedMetrics = AdvancedMetrics.getInstance();

    public MultiplexInboundHandler(ProxyProcessor proxyProcessor) {
        this.proxyProcessor = proxyProcessor;
    }

    @Override
    public void handleInbound(Channel channel, Object message) {
        if (!(message instanceof ByteBuf)) {
            logger.warn("收到非ByteBuf消息，忽略: {}", message.getClass());
            return;
        }

        // 记录请求处理
        advancedMetrics.recordRequest();
        long startTime = System.currentTimeMillis();

        ByteBuf data = (ByteBuf) message;
        MultiplexSession session = getOrCreateSession(channel);

        try {
            session.processData(data);

            // 记录响应处理
            advancedMetrics.recordResponse();
        } catch (Exception e) {
            logger.error("处理多路复用数据时发生异常: {}", channel.remoteAddress(), e);
            cleanupSession(channel);
        } finally {
            // 记录处理延迟
            long latency = System.currentTimeMillis() - startTime;
            advancedMetrics.recordLatency("request", latency);
        }
    }

    /**
     * 获取或创建会话
     */
    private MultiplexSession getOrCreateSession(Channel channel) {
        return sessions.computeIfAbsent(channel, ch -> {
            long clientConnectionId = CLIENT_CONNECTION_COUNTER.incrementAndGet();

            // 性能指标统计
            PerformanceMetrics.getInstance().incrementActiveConnections();

            logger.info("创建新的多路复用会话: {} (连接ID: {})", ch.remoteAddress(), clientConnectionId);

            // 启动认证过程
            if (AuthConfig.isAuthEnabled()) {
                AuthManager.getInstance().startAuthProcess(ch);
                logger.debug("连接 {} 需要认证", clientConnectionId);
            }

            // 添加连接关闭监听器
            ch.closeFuture().addListener(future -> {
                logger.info("客户端连接关闭: {} (连接ID: {})", ch.remoteAddress(), clientConnectionId);
                cleanupSession(ch);

                // 清理认证状态
                AuthManager.getInstance().clearAuth(ch);
            });

            return new MultiplexSession(ch, proxyProcessor, clientConnectionId);
        });
    }

    /**
     * 清理会话
     */
    private void cleanupSession(Channel channel) {
        MultiplexSession session = sessions.remove(channel);
        if (session != null) {
            session.cleanup();
            logger.debug("清理多路复用会话: {}, 活跃会话数: {}",
                    channel.remoteAddress(), session.getActiveSessionCount());
        }
    }

    /**
     * 获取会话统计信息
     */
    public int getTotalSessions() {
        return sessions.size();
    }

    /**
     * 获取总的活跃子会话数
     */
    public int getTotalActiveSubSessions() {
        return sessions.values().stream()
                .mapToInt(MultiplexSession::getActiveSessionCount)
                .sum();
    }

    @Override
    public boolean supports(String protocol) {
        return ProxyRequest.Protocol.MULTIPLEX.equals(protocol);
    }

    @Override
    public String getProtocolName() {
        return ProxyRequest.Protocol.MULTIPLEX;
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public void destroy() {
        // 清理所有会话
        sessions.values().forEach(MultiplexSession::cleanup);
        sessions.clear();
        logger.info("多路复用Inbound处理器已销毁");
    }

    /**
     * 客户端断开连接时的回调，触发对应会话清理
     */
    public void onClientDisconnected(Channel channel) {
        try {
            cleanupSession(channel);
        } catch (Exception e) {
            logger.warn("处理客户端断开清理时发生异常: {}", e.getMessage());
        }
    }
}