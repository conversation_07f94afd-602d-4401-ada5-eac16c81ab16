# 多组件代理客户端使用指南

proxy-client现在支持同时启动多种代理协议接入，包括SOCKS5和HTTP CONNECT代理。

## 🚀 核心特性

### 支持的协议
- **SOCKS5**: 标准SOCKS5代理协议
- **HTTP CONNECT**: HTTP隧道代理协议
- **同时运行**: 可以同时启动多个协议接入

### 架构优势
- **统一管理**: 所有协议共享同一个ConnectionManager
- **配置灵活**: 支持配置文件和命令行参数
- **资源共享**: 共享地址过滤器和认证机制
- **独立端口**: 每个协议使用独立的监听端口

## ⚙️ 配置方法

### 1. YAML配置文件方式（推荐）

编辑 `configs/development/proxy-client.yml`：

```yaml
# 接入器配置 - 支持多个同类型接入器
inbound:
  # SOCKS5 接入器列表
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
      description: "主要SOCKS5代理"
    - name: "socks5-backup"
      port: 1083
      enabled: false
      description: "备用SOCKS5代理"

  # HTTP 接入器列表
  http:
    - name: "http-main"
      port: 1082
      enabled: true
      description: "主要HTTP代理"
    - name: "http-backup"
      port: 1085
      enabled: false
      description: "备用HTTP代理"

# 其他配置
filter:
  mode: ALL_PROXY

proxy:
  server:
    host: localhost
    port: 8888

auth:
  enable: true
  username: admin
  password: password11
```

### 配置文件路径指定

#### 方式1：Properties文件指定
编辑 `proxy-client/src/main/resources/application.properties`：
```properties
# YAML配置文件路径
config.file.path=configs/development/proxy-client.yml
```

#### 方式2：命令行参数
```bash
# 指定配置文件
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="--config=configs/production/proxy-client.yml"

# 或使用短参数
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="-c my-config.yml"
```

### 2. 命令行参数方式

```bash
# 使用默认配置文件启动
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient"

# 使用自定义配置文件启动
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="--config=my-config.yml"

# 单SOCKS5端口（兼容模式）
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient" -Dexec.args="1080"

# 指定协议和端口
java -jar proxy-client.jar socks5 1080
java -jar proxy-client.jar http 8080

# 同时启动两个协议（HTTP端口=SOCKS5端口+1）
java -jar proxy-client.jar both 1080

# 多组件模式（SOCKS5端口 HTTP端口 代理服务器地址 代理服务器端口）
java -jar proxy-client.jar 1080 8080 localhost 8888
```

## 🏃‍♂️ 启动步骤

### 1. 启动代理服务器
```bash
cd proxy-server
mvn exec:java -Dexec.mainClass="com.proxy.server.ProxyServer" -Dexec.args="8888"
```

### 2. 启动多组件客户端
```bash
cd proxy-client
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient"
```

### 3. 验证启动状态
启动成功后会看到类似输出：
```
INFO  - 多组件代理客户端启动完成
INFO  - === 代理客户端状态 ===
INFO  - 运行状态: 运行中
INFO  - 过滤模式: CHINA_DIRECT
INFO  - 代理服务器: localhost:8888
INFO  - 认证状态: 启用
INFO  - 接入组件 (2):
INFO  -   - SOCKS5-Proxy (SOCKS5): 端口=1081, 状态=运行中, 活跃连接=0, 总连接=0
INFO  -   - HTTP-Proxy (HTTP): 端口=8080, 状态=运行中, 活跃连接=0, 总连接=0
```

## 🧪 功能测试

### 测试SOCKS5代理
```bash
# 测试SOCKS5连接
curl --socks5 localhost:1081 http://httpbin.org/ip

# 使用浏览器设置
# SOCKS代理: localhost:1081
```

### 测试HTTP代理
```bash
# 测试HTTP CONNECT
curl --proxy http://localhost:8080 http://httpbin.org/ip

# 使用浏览器设置
# HTTP代理: localhost:8080
```

### 测试地址过滤
```bash
# 中国网站（应该直连）
curl --socks5 localhost:1081 http://www.baidu.com
curl --proxy http://localhost:8080 http://www.baidu.com

# 海外网站（应该走代理）
curl --socks5 localhost:1081 http://www.google.com
curl --proxy http://localhost:8080 http://www.google.com
```

## 🔧 Native Image支持

多组件功能完全支持GraalVM Native Image：

### 构建Native Image
```bash
cd proxy-client
build-native-final-fix.bat  # Windows
# 或
./build-native-final-fix.sh  # Linux/macOS
```

### 运行Native Image
```bash
# 使用配置文件
target\proxy-client.exe

# 命令行参数
target\proxy-client.exe 1080
target\proxy-client.exe http 8080
target\proxy-client.exe both 1080
target\proxy-client.exe 1080 8080 localhost 8888
```

## 📊 性能特点

### 资源共享
- **单连接管理器**: 所有协议共享同一个到proxy-server的连接
- **统一认证**: 一次认证，所有协议都可使用
- **共享过滤器**: 地址过滤逻辑在所有协议间共享

### 内存占用
- **JVM版本**: 约120-180MB（多协议）vs 100-150MB（单协议）
- **Native Image**: 约30-60MB（多协议）vs 20-40MB（单协议）
- **增量很小**: 多协议带来的额外开销很小

### 启动时间
- **JVM版本**: 约2-3秒（与单协议相同）
- **Native Image**: 约50-100毫秒（与单协议相同）

## 🛠️ 架构说明

### 组件结构
```
ProxyClient (主启动类)
├── ProxyClientManager (组件管理器)
│   ├── ConnectionManager (连接管理 - 共享)
│   ├── AddressFilter (地址过滤 - 共享)
│   ├── ProxyInbound[] (代理接入组件数组)
│   │   ├── Socks5Inbound (SOCKS5接入)
│   │   ├── HttpInbound (HTTP接入)
│   │   └── ... (可扩展其他协议)
│   └── ProxyClientConfig (配置管理)
```

### 数据流向
```
客户端请求 → 协议接入组件 → 地址过滤判断 → 连接方式选择
    ↓
直连模式 → DirectConnectionHandler → 目标服务器
代理模式 → ConnectionManager → proxy-server → 目标服务器
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   - 确保SOCKS5和HTTP使用不同端口
   - 检查端口是否被其他程序占用

2. **组件启动失败**
   - 检查配置文件格式
   - 查看启动日志中的错误信息
   - 确认防火墙设置

3. **代理不工作**
   - 验证proxy-server是否正常运行
   - 检查认证配置是否正确
   - 测试网络连接

### 调试方法

1. **启用详细日志**
   ```xml
   <!-- logback.xml -->
   <logger name="com.proxy.client" level="DEBUG"/>
   ```

2. **检查组件状态**
   - 启动时会输出详细的状态信息
   - 包括每个组件的运行状态和连接统计

3. **测试单个协议**
   - 先测试单个协议是否正常
   - 再测试多协议同时运行

## 🎯 最佳实践

### 生产环境
1. **端口规划**: 为不同协议分配固定端口
2. **监控告警**: 监控各协议的连接数和错误率
3. **负载均衡**: 可以启动多个实例分担负载
4. **安全配置**: 启用认证并使用强密码

### 开发环境
1. **快速测试**: 使用`both`参数快速启动两个协议
2. **调试模式**: 启用DEBUG日志查看详细信息
3. **配置灵活**: 使用命令行参数快速切换配置

---

通过多组件架构，proxy-client现在可以同时为不同类型的客户端提供代理服务，大大提高了系统的灵活性和适用性。
