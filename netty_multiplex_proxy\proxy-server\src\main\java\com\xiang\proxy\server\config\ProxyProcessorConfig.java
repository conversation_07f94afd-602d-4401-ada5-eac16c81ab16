package com.xiang.proxy.server.config;

/**
 * ProxyProcessor配置类
 * 用于配置队列和线程相关参数
 */
public class ProxyProcessorConfig {
    
    // 默认配置
    public static final int DEFAULT_QUEUE_COUNT = Runtime.getRuntime().availableProcessors() * 2;
    public static final int DEFAULT_QUEUE_CAPACITY = 10000;
    public static final long DEFAULT_SHUTDOWN_TIMEOUT_SECONDS = 30;
    public static final boolean DEFAULT_ENABLE_QUEUE_MONITORING = true;
    public static final int DEFAULT_BATCH_SIZE = 10;
    public static final long DEFAULT_BATCH_TIMEOUT_MS = 50;
    public static final boolean DEFAULT_ENABLE_ADAPTIVE_ADJUSTMENT = true;
    
    private int queueCount = DEFAULT_QUEUE_COUNT;
    private int queueCapacity = DEFAULT_QUEUE_CAPACITY;
    private long shutdownTimeoutSeconds = DEFAULT_SHUTDOWN_TIMEOUT_SECONDS;
    private boolean enableQueueMonitoring = DEFAULT_ENABLE_QUEUE_MONITORING;
    private String workerThreadPrefix = "proxy-worker-";
    
    // 批处理配置
    private int batchSize = DEFAULT_BATCH_SIZE;
    private long batchTimeoutMs = DEFAULT_BATCH_TIMEOUT_MS;
    

    // 自适应调整配置
    private boolean enableAdaptiveAdjustment = DEFAULT_ENABLE_ADAPTIVE_ADJUSTMENT;
    private long adaptiveAdjustmentIntervalMs = 10000; // 10秒

    public ProxyProcessorConfig() {
    }

    public ProxyProcessorConfig(int queueCount, int queueCapacity) {
        this.queueCount = queueCount;
        this.queueCapacity = queueCapacity;
    }

    // Getters and Setters
    public int getQueueCount() {
        return queueCount;
    }

    public ProxyProcessorConfig setQueueCount(int queueCount) {
        if (queueCount <= 0) {
            throw new IllegalArgumentException("Queue count must be positive");
        }
        this.queueCount = queueCount;
        return this;
    }

    public int getQueueCapacity() {
        return queueCapacity;
    }

    public ProxyProcessorConfig setQueueCapacity(int queueCapacity) {
        if (queueCapacity <= 0) {
            throw new IllegalArgumentException("Queue capacity must be positive");
        }
        this.queueCapacity = queueCapacity;
        return this;
    }

    public long getShutdownTimeoutSeconds() {
        return shutdownTimeoutSeconds;
    }

    public ProxyProcessorConfig setShutdownTimeoutSeconds(long shutdownTimeoutSeconds) {
        if (shutdownTimeoutSeconds < 0) {
            throw new IllegalArgumentException("Shutdown timeout must be non-negative");
        }
        this.shutdownTimeoutSeconds = shutdownTimeoutSeconds;
        return this;
    }

    public boolean isEnableQueueMonitoring() {
        return enableQueueMonitoring;
    }

    public ProxyProcessorConfig setEnableQueueMonitoring(boolean enableQueueMonitoring) {
        this.enableQueueMonitoring = enableQueueMonitoring;
        return this;
    }

    public String getWorkerThreadPrefix() {
        return workerThreadPrefix;
    }

    public ProxyProcessorConfig setWorkerThreadPrefix(String workerThreadPrefix) {
        if (workerThreadPrefix == null || workerThreadPrefix.trim().isEmpty()) {
            throw new IllegalArgumentException("Worker thread prefix cannot be null or empty");
        }
        this.workerThreadPrefix = workerThreadPrefix;
        return this;
    }

    /**
     * 创建默认配置
     */
    public static ProxyProcessorConfig defaultConfig() {
        return new ProxyProcessorConfig();
    }

    // 新增配置的Getters和Setters
    public int getBatchSize() { return batchSize; }
    public ProxyProcessorConfig setBatchSize(int batchSize) {
        if (batchSize <= 0) throw new IllegalArgumentException("Batch size must be positive");
        this.batchSize = batchSize;
        return this;
    }

    public long getBatchTimeoutMs() { return batchTimeoutMs; }
    public ProxyProcessorConfig setBatchTimeoutMs(long batchTimeoutMs) {
        if (batchTimeoutMs < 0) throw new IllegalArgumentException("Batch timeout must be non-negative");
        this.batchTimeoutMs = batchTimeoutMs;
        return this;
    }


    public boolean isEnableAdaptiveAdjustment() { return enableAdaptiveAdjustment; }
    public ProxyProcessorConfig setEnableAdaptiveAdjustment(boolean enableAdaptiveAdjustment) {
        this.enableAdaptiveAdjustment = enableAdaptiveAdjustment;
        return this;
    }

    public long getAdaptiveAdjustmentIntervalMs() { return adaptiveAdjustmentIntervalMs; }
    public ProxyProcessorConfig setAdaptiveAdjustmentIntervalMs(long adaptiveAdjustmentIntervalMs) {
        if (adaptiveAdjustmentIntervalMs <= 0) throw new IllegalArgumentException("Adaptive adjustment interval must be positive");
        this.adaptiveAdjustmentIntervalMs = adaptiveAdjustmentIntervalMs;
        return this;
    }

    /**
     * 创建高性能配置
     */
    public static ProxyProcessorConfig highPerformanceConfig() {
        return new ProxyProcessorConfig()
                .setQueueCount(Runtime.getRuntime().availableProcessors() * 4)
                .setQueueCapacity(50000)
                .setShutdownTimeoutSeconds(60)
                .setBatchSize(20)
                .setBatchTimeoutMs(30)
                .setEnableAdaptiveAdjustment(true);
    }

    /**
     * 创建低资源配置
     */
    public static ProxyProcessorConfig lowResourceConfig() {
        return new ProxyProcessorConfig()
                .setQueueCount(2)
                .setQueueCapacity(1000)
                .setShutdownTimeoutSeconds(10)
                .setBatchSize(5)
                .setBatchTimeoutMs(100)
                .setEnableAdaptiveAdjustment(false);
    }

    /**
     * 创建连接池优化配置
     */
    public static ProxyProcessorConfig connectionPoolOptimizedConfig() {
        return new ProxyProcessorConfig()
                .setBatchSize(15)
                .setEnableAdaptiveAdjustment(true);
    }

    @Override
    public String toString() {
        return String.format("ProxyProcessorConfig{queueCount=%d, queueCapacity=%d, " +
                        "shutdownTimeoutSeconds=%d, enableQueueMonitoring=%s, workerThreadPrefix='%s'}",
                queueCount, queueCapacity, shutdownTimeoutSeconds, 
                enableQueueMonitoring, workerThreadPrefix);
    }
}