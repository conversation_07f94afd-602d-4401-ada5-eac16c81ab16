# System properties for GraalVM Native Image
# These properties are set at build time and runtime

# Disable Netty ResourceLeakDetector to avoid initialization issues
io.netty.leakDetection.level=DISABLED
io.netty.leakDetectionLevel=DISABLED
io.netty.resourceLeakDetection.level=DISABLED

# Disable Netty buffer leak detection
io.netty.buffer.checkAccessible=false
io.netty.buffer.checkBounds=false

# Disable Netty allocator leak detection
io.netty.allocator.type=unpooled
io.netty.noPreferDirect=true

# Disable Netty unsafe usage warnings
io.netty.noUnsafe=false
io.netty.tryUnsafe=true

# Disable Netty native transport
io.netty.transport.noNative=true

# Set logging level
io.netty.util.internal.logging.InternalLoggerFactory=io.netty.util.internal.logging.Slf4JLoggerFactory
