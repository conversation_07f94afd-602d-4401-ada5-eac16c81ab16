package com.xiang.proxy.server.exception;

import com.xiang.proxy.server.metrics.AdvancedMetrics;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.nio.channels.ClosedChannelException;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 统一异常处理器
 * 提供标准化的异常处理、分类、日志记录和统计功能
 */
public class ExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(ExceptionHandler.class);
    
    // 异常统计计数器
    private static final AtomicLong CONNECTION_RESET_COUNT = new AtomicLong(0);
    private static final AtomicLong CHANNEL_CLOSED_COUNT = new AtomicLong(0);
    private static final AtomicLong TIMEOUT_COUNT = new AtomicLong(0);
    private static final AtomicLong UNKNOWN_HOST_COUNT = new AtomicLong(0);
    private static final AtomicLong CONNECTION_REFUSED_COUNT = new AtomicLong(0);
    private static final AtomicLong IO_ERROR_COUNT = new AtomicLong(0);
    private static final AtomicLong UNKNOWN_ERROR_COUNT = new AtomicLong(0);
    
    private final AdvancedMetrics metrics = AdvancedMetrics.getInstance();
    
    /**
     * 异常类型枚举
     */
    public enum ExceptionType {
        CONNECTION_RESET("连接重置", "CONNECTION_RESET"),
        CHANNEL_CLOSED("通道关闭", "CHANNEL_CLOSED"),
        NETWORK_TIMEOUT("网络超时", "NETWORK_TIMEOUT"),
        UNKNOWN_HOST("未知主机", "UNKNOWN_HOST"),
        CONNECTION_REFUSED("连接被拒绝", "CONNECTION_REFUSED"),
        IO_ERROR("IO错误", "IO_ERROR"),
        UNKNOWN_ERROR("未知错误", "UNKNOWN_ERROR");
        
        private final String description;
        private final String metricName;
        
        ExceptionType(String description, String metricName) {
            this.description = description;
            this.metricName = metricName;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getMetricName() {
            return metricName;
        }
    }
    
    /**
     * 异常处理结果
     */
    public static class ExceptionResult {
        private final ExceptionType type;
        private final String message;
        private final boolean shouldLog;
        private final boolean shouldClose;
        
        public ExceptionResult(ExceptionType type, String message, boolean shouldLog, 
                             boolean shouldClose) {
            this.type = type;
            this.message = message;
            this.shouldLog = shouldLog;
            this.shouldClose = shouldClose;
        }
        
        // Getters
        public ExceptionType getType() { return type; }
        public String getMessage() { return message; }
        public boolean shouldLog() { return shouldLog; }
        public boolean shouldClose() { return shouldClose; }
    }
    
    /**
     * 处理通道异常
     */
    public ExceptionResult handleChannelException(ChannelHandlerContext ctx, Throwable cause, 
                                                String contextInfo) {
        ExceptionType type = classifyException(cause);
        String message = formatExceptionMessage(cause, contextInfo);
        
        // 更新统计计数器
        if (ExceptionHandlingConfig.isEnableExceptionStats()) {
            updateExceptionStats(type);
        }
        
        // 记录到指标系统
        metrics.recordError(type.getMetricName());
        
        // 根据异常类型决定处理策略
        boolean shouldLog = shouldLogException(type, cause);
        boolean shouldClose = shouldCloseConnection(type, cause);
        
        // 记录日志
        if (shouldLog) {
            logException(type, message, cause, ctx);
        }
        
        return new ExceptionResult(type, message, shouldLog, shouldClose);
    }
    
    /**
     * 处理连接异常
     */
    public ExceptionResult handleConnectionException(Throwable cause, String host, int port, 
                                                   String connectionId) {
        ExceptionType type = classifyException(cause);
        String contextInfo = String.format("连接到 %s:%d (连接ID: %s)", host, port, connectionId);
        String message = formatExceptionMessage(cause, contextInfo);
        
        // 更新统计计数器
        if (ExceptionHandlingConfig.isEnableExceptionStats()) {
            updateExceptionStats(type);
        }
        
        // 记录到指标系统
        metrics.recordError(type.getMetricName());
        metrics.recordConnectionQuality(host, false, 0);
        
        // 根据异常类型决定处理策略
        boolean shouldLog = shouldLogException(type, cause);
        boolean shouldClose = true; // 连接异常通常需要关闭
        
        // 记录日志
        if (shouldLog) {
            logConnectionException(type, message, cause, host, port, connectionId);
        }
        
        return new ExceptionResult(type, message, shouldLog, shouldClose);
    }
    
    /**
     * 分类异常类型
     */
    private ExceptionType classifyException(Throwable cause) {
        if (cause == null) {
            return ExceptionType.UNKNOWN_ERROR;
        }
        
        if (!ExceptionHandlingConfig.isEnableSmartClassification()) {
            return ExceptionType.UNKNOWN_ERROR;
        }
        
        // 检查连接重置异常
        if (isConnectionResetException(cause)) {
            return ExceptionType.CONNECTION_RESET;
        }
        
        // 检查通道关闭异常
        if (isChannelClosedException(cause)) {
            return ExceptionType.CHANNEL_CLOSED;
        }
        
        // 检查超时异常
        if (isTimeoutException(cause)) {
            return ExceptionType.NETWORK_TIMEOUT;
        }
        
        // 检查未知主机异常
        if (cause instanceof UnknownHostException) {
            return ExceptionType.UNKNOWN_HOST;
        }
        
        // 检查连接被拒绝异常
        if (cause instanceof ConnectException) {
            return ExceptionType.CONNECTION_REFUSED;
        }
        
        // 检查IO异常
        if (cause instanceof IOException) {
            return ExceptionType.IO_ERROR;
        }
        
        return ExceptionType.UNKNOWN_ERROR;
    }
    
    /**
     * 检查是否为连接重置异常
     */
    private boolean isConnectionResetException(Throwable cause) {
        if (cause instanceof SocketException) {
            String message = cause.getMessage();
            return message != null && (
                message.contains("Connection reset by peer") ||
                message.contains("Connection reset") ||
                message.contains("远程主机强迫关闭了一个现有的连接")
            );
        }
        return false;
    }
    
    /**
     * 检查是否为通道关闭异常
     */
    private boolean isChannelClosedException(Throwable cause) {
        if (cause instanceof ClosedChannelException) {
            return true;
        }
        
        // 检查Netty的StacklessClosedChannelException
        String className = cause.getClass().getSimpleName();
        if ("StacklessClosedChannelException".equals(className)) {
            return true;
        }
        
        if (cause instanceof IOException) {
            String message = cause.getMessage();
            return message != null && (
                message.contains("Channel is closed") ||
                message.contains("通道已关闭") ||
                message.contains("Connection is closed")
            );
        }
        
        return false;
    }
    
    /**
     * 检查是否为超时异常
     */
    private boolean isTimeoutException(Throwable cause) {
        if (cause instanceof TimeoutException) {
            return true;
        }
        
        if (cause instanceof IOException) {
            String message = cause.getMessage();
            return message != null && (
                message.contains("timeout") ||
                message.contains("超时") ||
                message.contains("Read timed out") ||
                message.contains("Write timed out") ||
                message.contains("Connection timed out")
            );
        }
        
        return false;
    }
    
    /**
     * 格式化异常消息
     */
    private String formatExceptionMessage(Throwable cause, String contextInfo) {
        if (cause == null) {
            return "未知异常: " + contextInfo;
        }
        
        String message = cause.getMessage();
        if (message == null || message.trim().isEmpty()) {
            message = cause.getClass().getSimpleName();
        }
        
        return String.format("%s - %s", contextInfo, message);
    }
    
    /**
     * 更新异常统计计数器
     */
    private void updateExceptionStats(ExceptionType type) {
        switch (type) {
            case CONNECTION_RESET:
                CONNECTION_RESET_COUNT.incrementAndGet();
                break;
            case CHANNEL_CLOSED:
                CHANNEL_CLOSED_COUNT.incrementAndGet();
                break;
            case NETWORK_TIMEOUT:
                TIMEOUT_COUNT.incrementAndGet();
                break;
            case UNKNOWN_HOST:
                UNKNOWN_HOST_COUNT.incrementAndGet();
                break;
            case CONNECTION_REFUSED:
                CONNECTION_REFUSED_COUNT.incrementAndGet();
                break;
            case IO_ERROR:
                IO_ERROR_COUNT.incrementAndGet();
                break;
            case UNKNOWN_ERROR:
                UNKNOWN_ERROR_COUNT.incrementAndGet();
                break;
        }
    }
    
    /**
     * 判断是否应该记录日志
     */
    private boolean shouldLogException(ExceptionType type, Throwable cause) {
        switch (type) {
            case CONNECTION_RESET:
            case CHANNEL_CLOSED:
                // 常见的网络异常，降级为DEBUG级别
                return logger.isDebugEnabled();
            case NETWORK_TIMEOUT:
            case UNKNOWN_HOST:
            case CONNECTION_REFUSED:
                // 网络问题，使用WARN级别
                return true;
            case IO_ERROR:
            case UNKNOWN_ERROR:
                // 严重异常，使用ERROR级别
                return true;
            default:
                return true;
        }
    }
    
    /**
     * 判断是否应该关闭连接
     */
    private boolean shouldCloseConnection(ExceptionType type, Throwable cause) {
        switch (type) {
            case CONNECTION_RESET:
            case CHANNEL_CLOSED:
            case IO_ERROR:
                // 这些异常通常意味着连接已经不可用
                return true;
            case NETWORK_TIMEOUT:
                // 超时可能是临时的，但为了安全起见还是关闭
                return true;
            case UNKNOWN_HOST:
            case CONNECTION_REFUSED:
                // 连接建立失败，需要关闭
                return true;
            case UNKNOWN_ERROR:
                // 未知异常，为了安全起见关闭连接
                return true;
            default:
                return true;
        }
    }
    
    /**
     * 记录通道异常日志
     */
    private void logException(ExceptionType type, String message, Throwable cause, 
                            ChannelHandlerContext ctx) {
        String remoteAddress = "unknown";
        if (ctx != null && ctx.channel() != null) {
            remoteAddress = String.valueOf(ctx.channel().remoteAddress());
        }
        
        switch (type) {
            case CONNECTION_RESET:
            case CHANNEL_CLOSED:
                logger.debug("[{}] {}: {}", type.getDescription(), remoteAddress, message);
                break;
            case NETWORK_TIMEOUT:
            case UNKNOWN_HOST:
            case CONNECTION_REFUSED:
                logger.warn("[{}] {}: {}", type.getDescription(), remoteAddress, message);
                break;
            case IO_ERROR:
            case UNKNOWN_ERROR:
                logger.error("[{}] {}: {}", type.getDescription(), remoteAddress, message, cause);
                break;
        }
    }
    
    /**
     * 记录连接异常日志
     */
    private void logConnectionException(ExceptionType type, String message, Throwable cause,
                                      String host, int port, String connectionId) {
        switch (type) {
            case CONNECTION_RESET:
            case CHANNEL_CLOSED:
                logger.debug("[{}] {}:{} (连接ID: {}): {}", 
                    type.getDescription(), host, port, connectionId, message);
                break;
            case NETWORK_TIMEOUT:
            case UNKNOWN_HOST:
            case CONNECTION_REFUSED:
                logger.warn("[{}] {}:{} (连接ID: {}): {}", 
                    type.getDescription(), host, port, connectionId, message);
                break;
            case IO_ERROR:
            case UNKNOWN_ERROR:
                logger.error("[{}] {}:{} (连接ID: {}): {}", 
                    type.getDescription(), host, port, connectionId, message, cause);
                break;
        }
    }
    
    /**
     * 安全关闭通道
     */
    public void safeCloseChannel(Channel channel, String reason) {
        if (channel == null) {
            return;
        }
        
        try {
            if (channel.isActive()) {
                logger.debug("安全关闭通道: {} - {}", channel.remoteAddress(), reason);
                channel.close().addListener(future -> {
                    if (!future.isSuccess()) {
                        logger.debug("关闭通道时发生异常: {}", future.cause().getMessage());
                    }
                });
            }
        } catch (Exception e) {
            logger.debug("安全关闭通道时发生异常: {}", e.getMessage());
        }
    }
    
    /**
     * 安全关闭ChannelHandlerContext
     */
    public void safeCloseContext(ChannelHandlerContext ctx, String reason) {
        if (ctx == null) {
            return;
        }
        
        try {
            if (ctx.channel().isActive()) {
                logger.debug("安全关闭上下文: {} - {}", ctx.channel().remoteAddress(), reason);
                ctx.close().addListener(future -> {
                    if (!future.isSuccess()) {
                        logger.debug("关闭上下文时发生异常: {}", future.cause().getMessage());
                    }
                });
            }
        } catch (Exception e) {
            logger.debug("安全关闭上下文时发生异常: {}", e.getMessage());
        }
    }
    
    /**
     * 获取异常统计信息
     */
    public String getExceptionStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("=== 异常统计信息 ===\n");
        stats.append(String.format("连接重置: %d\n", CONNECTION_RESET_COUNT.get()));
        stats.append(String.format("通道关闭: %d\n", CHANNEL_CLOSED_COUNT.get()));
        stats.append(String.format("网络超时: %d\n", TIMEOUT_COUNT.get()));
        stats.append(String.format("未知主机: %d\n", UNKNOWN_HOST_COUNT.get()));
        stats.append(String.format("连接被拒绝: %d\n", CONNECTION_REFUSED_COUNT.get()));
        stats.append(String.format("IO错误: %d\n", IO_ERROR_COUNT.get()));
        stats.append(String.format("未知错误: %d\n", UNKNOWN_ERROR_COUNT.get()));
        stats.append("==================");
        return stats.toString();
    }
    
    /**
     * 重置异常统计
     */
    public void resetExceptionStats() {
        CONNECTION_RESET_COUNT.set(0);
        CHANNEL_CLOSED_COUNT.set(0);
        TIMEOUT_COUNT.set(0);
        UNKNOWN_HOST_COUNT.set(0);
        CONNECTION_REFUSED_COUNT.set(0);
        IO_ERROR_COUNT.set(0);
        UNKNOWN_ERROR_COUNT.set(0);
        logger.info("异常统计信息已重置");
    }
}