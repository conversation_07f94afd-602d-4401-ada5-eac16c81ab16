# 🚀 部署指南

## 📋 概述

本指南提供多路复用代理系统的完整部署方案，包括开发环境、生产环境、高性能环境和容器化部署等多种场景。

---

## 🎯 部署架构

### 标准部署架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │───▶│  proxy-client   │───▶│  proxy-server   │
│  (浏览器/应用)   │    │   (本地代理)     │    │   (远程服务器)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────┐        ┌─────────────┐
                       │ 地址过滤器   │        │ 连接池管理   │
                       │ 恶意内容过滤 │        │ 性能监控     │
                       └─────────────┘        └─────────────┘
```

### 高可用部署架构
```
                    ┌─────────────────┐
                    │   Load Balancer │
                    └─────────┬───────┘
                              │
              ┌───────────────┼───────────────┐
              ▼               ▼               ▼
    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
    │proxy-server1│ │proxy-server2│ │proxy-server3│
    │   (主节点)   │ │   (备节点)   │ │   (备节点)   │
    └─────────────┘ └─────────────┘ └─────────────┘
```

---

## 🔧 环境要求

### 系统要求
| 组件 | 最低要求 | 推荐配置 | 高性能配置 |
|------|----------|----------|------------|
| **CPU** | 2核心 | 4核心 | 8核心+ |
| **内存** | 2GB | 4GB | 8GB+ |
| **磁盘** | 1GB | 2GB | 5GB+ |
| **网络** | 100Mbps | 1Gbps | 10Gbps+ |

### 软件要求
- **Java**: OpenJDK 17+ (推荐 OpenJDK 21)
- **Maven**: 3.8+ (构建时需要)
- **GraalVM**: 22.3+ (Native Image构建)
- **操作系统**: Windows 10+, Linux (Ubuntu 20.04+), macOS 12+

---

## 🚀 快速部署

### 1. 开发环境部署

#### 步骤1：克隆项目
```bash
git clone <repository-url>
cd netty_proxy_Multiplex
```

#### 步骤2：编译项目
```bash
# 编译服务器端
cd proxy-server
mvn clean compile

# 编译客户端
cd ../proxy-client
mvn clean compile
```

#### 步骤3：启动服务
```bash
# 启动代理服务器 (终端1)
cd proxy-server
mvn exec:java -Dexec.mainClass="com.proxy.server.ProxyServer"

# 启动代理客户端 (终端2)
cd proxy-client
mvn exec:java -Dexec.mainClass="com.proxy.client.ProxyClient"
```

#### 步骤4：验证部署
```bash
# 测试SOCKS5代理 (端口1081)
curl --socks5 localhost:1081 http://httpbin.org/ip

# 测试HTTP代理 (端口1082)
curl --proxy localhost:1082 http://httpbin.org/ip
```

### 2. Native Image部署 (推荐)

#### 步骤1：安装GraalVM
```bash
# 下载并安装GraalVM
# 设置JAVA_HOME和GRAALVM_HOME环境变量
```

#### 步骤2：构建Native Image
```bash
# Windows
cd proxy-client
build-native.bat

# Linux/macOS
cd proxy-client
chmod +x build-native.sh
./build-native.sh
```

#### 步骤3：运行Native Image
```bash
# Windows
target\proxy-client.exe

# Linux/macOS
./target/proxy-client
```

---

## 🏭 生产环境部署

### 1. 配置准备

#### 创建生产配置
```bash
# 创建生产配置目录
mkdir -p /opt/proxy/config/{server,client}

# 复制配置模板
cp configs/production/server/* /opt/proxy/config/server/
cp configs/production/client/* /opt/proxy/config/client/
```

#### 修改配置文件
```yaml
# /opt/proxy/config/server/proxy-server.yml
server:
  port: 8888
  host: "0.0.0.0"

# 性能优化配置
performance:
  thread-pool:
    boss-threads: 2
    worker-threads: 16
  connection-pool:
    max-connections-per-host: 50
    connection-timeout: 10000

# 监控配置
monitoring:
  enabled: true
  report-interval-seconds: 60
```

### 2. 系统服务配置

#### 创建systemd服务 (Linux)
```bash
# /etc/systemd/system/proxy-server.service
[Unit]
Description=Multiplex Proxy Server
After=network.target

[Service]
Type=simple
User=proxy
Group=proxy
WorkingDirectory=/opt/proxy
ExecStart=/usr/bin/java -jar proxy-server.jar --config=/opt/proxy/config/server/proxy-server.yml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 启动服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable proxy-server
sudo systemctl start proxy-server
sudo systemctl status proxy-server
```

### 3. 反向代理配置 (Nginx)

```nginx
# /etc/nginx/sites-available/proxy-server
upstream proxy_backend {
    server 127.0.0.1:8888 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8889 max_fails=3 fail_timeout=30s backup;
}

server {
    listen 80;
    server_name proxy.example.com;
    
    location / {
        proxy_pass http://proxy_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 长连接支持
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        
        # 超时配置
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

---

## 🔒 SSL/TLS部署

### 1. 证书生成

#### 生成自签名证书 (开发/测试)
```bash
cd scripts
# Windows
generate-ssl-certificates.bat

# Linux/macOS
chmod +x generate-ssl-certificates.sh
./generate-ssl-certificates.sh
```

#### 使用CA证书 (生产)
```bash
# 将CA证书放置到指定位置
cp server.crt /opt/proxy/ssl/
cp server.key /opt/proxy/ssl/
cp ca.crt /opt/proxy/ssl/
```

### 2. SSL配置

#### 服务器SSL配置
```yaml
# proxy-server.yml
ssl:
  enable: true
  key-store-path: "/opt/proxy/ssl/server.p12"
  key-store-password: "your_password"
  key-store-type: "PKCS12"
  protocols: ["TLSv1.2", "TLSv1.3"]
  cipher-suites: []
```

#### 客户端SSL配置
```yaml
# proxy-client.yml
ssl:
  enable: true
  trust-store-path: "/opt/proxy/ssl/truststore.p12"
  trust-store-password: "your_password"
  verify-hostname: true
```

---

## 📊 监控部署

### 1. 日志配置

#### Logback配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/var/log/proxy/proxy-server.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/var/log/proxy/proxy-server.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <root level="INFO">
        <appender-ref ref="FILE" />
    </root>
</configuration>
```

### 2. 性能监控

#### Prometheus集成
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'proxy-server'
    static_configs:
      - targets: ['localhost:8888']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

#### Grafana仪表板
- 连接数监控
- 吞吐量统计
- 延迟分布
- 错误率分析
- 资源使用情况

---

## 🐳 容器化部署

### 1. Docker部署

#### Dockerfile (服务器端)

```dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app
COPY proxy-server.jar .
COPY configs ./configs/

EXPOSE 8888

CMD ["java", "-jar", "proxy-server.jar", "--config=configs/production/proxy-server.yml"]
```

#### Docker Compose

```yaml
version: '3.8'

services:
  proxy-server:
    build: proxy-server
    ports:
      - "8888:8888"
    volumes:
      - ./configs:/app/configs
      - ./logs:/var/log/proxy
    environment:
      - JAVA_OPTS=-Xmx2g -Xms1g
    restart: unless-stopped

  proxy-client:
    build: proxy-client
    ports:
      - "1081:1081"
      - "1082:1082"
    depends_on:
      - proxy-server
    restart: unless-stopped
```

#### 启动容器
```bash
docker-compose up -d
docker-compose logs -f
```

### 2. Kubernetes部署

#### Deployment配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxy-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: proxy-server
  template:
    metadata:
      labels:
        app: proxy-server
    spec:
      containers:
      - name: proxy-server
        image: proxy-server:latest
        ports:
        - containerPort: 8888
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        env:
        - name: JAVA_OPTS
          value: "-Xmx1g -Xms512m"
```

#### Service配置
```yaml
apiVersion: v1
kind: Service
metadata:
  name: proxy-server-service
spec:
  selector:
    app: proxy-server
  ports:
  - protocol: TCP
    port: 8888
    targetPort: 8888
  type: LoadBalancer
```

---

## 🔧 性能调优

### 1. JVM参数优化

#### 生产环境JVM参数
```bash
JAVA_OPTS="
-Xmx4g -Xms2g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UnlockExperimentalVMOptions
-XX:+UseStringDeduplication
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-Xloggc:/var/log/proxy/gc.log
-XX:+UseGCLogFileRotation
-XX:NumberOfGCLogFiles=5
-XX:GCLogFileSize=10M
"
```

#### 高性能JVM参数
```bash
JAVA_OPTS="
-Xmx8g -Xms4g
-XX:+UseZGC
-XX:+UnlockExperimentalVMOptions
-XX:+UseTransparentHugePages
-XX:+AlwaysPreTouch
-XX:+UseLargePages
-Djava.net.preferIPv4Stack=true
-Dnetty.allocator.type=pooled
-Dnetty.allocator.numDirectArenas=16
"
```

### 2. 操作系统调优

#### Linux内核参数
```bash
# /etc/sysctl.conf
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
fs.file-max = 1000000
```

#### 文件描述符限制
```bash
# /etc/security/limits.conf
* soft nofile 65535
* hard nofile 65535
* soft nproc 65535
* hard nproc 65535
```

---

## 🔍 故障排除

### 1. 常见问题

#### 连接问题
```bash
# 检查端口监听
netstat -tlnp | grep 8888

# 检查防火墙
sudo ufw status
sudo firewall-cmd --list-ports

# 检查进程状态
ps aux | grep proxy
```

#### 性能问题
```bash
# 检查系统资源
top -p $(pgrep -f proxy-server)
iostat -x 1
sar -n DEV 1

# 检查JVM状态
jstat -gc $(pgrep -f proxy-server)
jmap -histo $(pgrep -f proxy-server)
```

### 2. 日志分析

#### 关键日志位置
```bash
# 应用日志
tail -f /var/log/proxy/proxy-server.log

# GC日志
tail -f /var/log/proxy/gc.log

# 系统日志
journalctl -u proxy-server -f
```

#### 性能监控命令
```bash
# 连接数统计
ss -s | grep TCP

# 网络流量监控
iftop -i eth0

# 实时性能监控
htop
```

---

## 📋 部署检查清单

### 部署前检查
- [ ] 系统要求满足
- [ ] 软件依赖安装
- [ ] 配置文件准备
- [ ] SSL证书配置
- [ ] 防火墙规则设置

### 部署后验证
- [ ] 服务启动正常
- [ ] 端口监听正确
- [ ] 代理功能测试
- [ ] 性能指标正常
- [ ] 日志输出正常
- [ ] 监控系统工作

### 生产环境检查
- [ ] 高可用配置
- [ ] 负载均衡设置
- [ ] 备份策略制定
- [ ] 监控告警配置
- [ ] 运维文档完善

---

## 📚 相关文档

- [README.md](README.md) - 项目概述
- [CONFIGURATION_GUIDE.md](CONFIGURATION_GUIDE.md) - 配置指南
- [SSL_DEPLOYMENT_GUIDE.md](SSL_DEPLOYMENT_GUIDE.md) - SSL部署指南
- [PERFORMANCE_OPTIMIZATION_SUMMARY.md](PERFORMANCE_OPTIMIZATION_SUMMARY.md) - 性能优化
- [TECHNICAL_HIGHLIGHTS.md](TECHNICAL_HIGHLIGHTS.md) - 技术亮点

---

**部署支持**: 如需部署支持，请参考相关文档或联系技术支持团队。