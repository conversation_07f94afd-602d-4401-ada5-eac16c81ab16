# Proxy Server 组件化重构设计方案

## 1. 架构概览

将现有的 proxy-server 拆分为三个核心组件：

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Inbound       │    │   Router     │    │   Outbound      │
│   Components    │───▶│  Component   │───▶│   Components    │
└─────────────────┘    └──────────────┘    └─────────────────┘
```

### 核心理念
- **统一请求模型**: 所有inbound流量封装成统一的ProxyRequest
- **智能路由**: 基于规则的路由决策，选择合适的outbound
- **解耦设计**: 各组件独立，便于扩展和维护

## 2. 组件详细设计

### 2.1 Inbound 组件

负责接收各种协议的入站流量，并将其标准化为统一的请求格式。

#### 核心接口
```java
public interface InboundHandler {
    void handleInbound(Channel channel, Object message);
    boolean supports(String protocol);
    String getProtocolName();
}
```

#### 实现组件
- **MultiplexInboundHandler**: 处理多路复用协议
- **HttpInboundHandler**: 处理HTTP/HTTPS协议  
- **Socks5InboundHandler**: 处理SOCKS5协议
- **TcpInboundHandler**: 处理原始TCP流量
- **UdpInboundHandler**: 处理UDP流量

#### 统一请求模型
```java
public class ProxyRequest {
    private String requestId;           // 唯一请求ID
    private String protocol;            // 协议类型 (TCP/UDP/HTTP/SOCKS5/MULTIPLEX)
    private String targetHost;          // 目标主机
    private int targetPort;             // 目标端口
    private ByteBuf data;              // 请求数据
    private Channel clientChannel;      // 客户端连接
    private Map<String, Object> attributes; // 扩展属性
    private long timestamp;            // 请求时间戳
    private String clientId;           // 客户端标识
    private int sessionId;             // 会话ID (多路复用)
}
```

### 2.2 Router 组件

负责根据路由规则决定请求应该转发到哪个outbound组件。

#### 核心接口
```java
public interface Router {
    RouteResult route(ProxyRequest request);
    void addRoute(RouteRule rule);
    void removeRoute(String ruleId);
    List<RouteRule> getRoutes();
}
```

#### 路由规则
```java
public class RouteRule {
    private String ruleId;
    private String name;
    private int priority;              // 优先级，数字越小优先级越高
    private List<RouteMatcher> matchers; // 匹配条件
    private String outboundId;         // 目标outbound ID
    private boolean enabled;           // 是否启用
    private Map<String, Object> config; // 路由配置
}

public class RouteMatcher {
    private String type;               // 匹配类型: host, port, protocol, client_ip, etc.
    private String operator;           // 操作符: equals, contains, regex, range, etc.
    private String value;              // 匹配值
}
```

#### 路由策略
- **基于主机的路由**: 根据目标主机选择outbound
- **基于端口的路由**: 根据目标端口选择outbound  
- **基于协议的路由**: 根据协议类型选择outbound
- **基于客户端的路由**: 根据客户端IP/ID选择outbound
- **负载均衡路由**: 在多个outbound间分配请求
- **故障转移路由**: 主outbound失败时切换到备用

### 2.3 Outbound 组件

负责将请求转发到目标服务器，并处理响应回写。

#### 核心接口
```java
public interface OutboundHandler {
    CompletableFuture<OutboundConnection> connect(ProxyRequest request);
    void sendData(OutboundConnection connection, ByteBuf data);
    void closeConnection(OutboundConnection connection);
    String getOutboundId();
    OutboundConfig getConfig();
}
```

#### 实现组件
- **DirectOutboundHandler**: 直连目标服务器
- **ProxyChainOutboundHandler**: 通过上游代理转发
- **LoadBalancerOutboundHandler**: 负载均衡到多个后端
- **FailoverOutboundHandler**: 故障转移处理
- **CacheOutboundHandler**: 带缓存的outbound

#### 连接模型
```java
public class OutboundConnection {
    private String connectionId;
    private Channel backendChannel;
    private String targetHost;
    private int targetPort;
    private String protocol;
    private long createTime;
    private AtomicLong bytesTransferred;
    private volatile boolean active;
}
```

## 3. 核心流程

### 3.1 请求处理流程

```
1. Inbound接收请求 → 2. 封装ProxyRequest → 3. Router路由决策 → 4. Outbound处理 → 5. 响应回写
```

### 3.2 详细流程

1. **Inbound阶段**
   - 接收客户端连接和数据
   - 协议解析和验证
   - 封装成统一的ProxyRequest
   - 传递给Router

2. **Router阶段**  
   - 遍历路由规则（按优先级）
   - 匹配请求属性
   - 选择合适的Outbound
   - 返回RouteResult

3. **Outbound阶段**
   - 建立到目标服务器的连接
   - 转发请求数据
   - 处理响应数据
   - 回写给客户端

## 4. 配置示例

### 4.1 路由配置
```yaml
routes:
  - id: "direct-route"
    name: "直连路由"
    priority: 100
    matchers:
      - type: "host"
        operator: "regex"
        value: ".*\\.local$"
    outbound: "direct"
    enabled: true
    
  - id: "proxy-route"  
    name: "代理路由"
    priority: 200
    matchers:
      - type: "host"
        operator: "not_contains"
        value: ".local"
    outbound: "upstream-proxy"
    enabled: true
```

### 4.2 Outbound配置
```yaml
outbounds:
  - id: "direct"
    type: "direct"
    config:
      connect_timeout: 5000
      read_timeout: 30000
      
  - id: "upstream-proxy"
    type: "proxy_chain"
    config:
      proxy_host: "upstream.proxy.com"
      proxy_port: 8080
      proxy_type: "http"
```

## 5. 实现优势

### 5.1 架构优势
- **模块化**: 各组件职责清晰，便于维护
- **可扩展**: 新协议和路由策略易于添加
- **可配置**: 路由规则支持动态配置
- **高性能**: 统一的请求模型减少数据转换开销

### 5.2 功能优势
- **协议无关**: 支持多种入站协议
- **智能路由**: 灵活的路由决策机制
- **故障处理**: 内置故障转移和重试机制
- **监控友好**: 统一的指标收集点

## 6. 迁移策略

### 6.1 渐进式迁移
1. **第一阶段**: 创建新的组件接口和基础实现
2. **第二阶段**: 将现有MultiplexProxyHandler重构为Inbound组件
3. **第三阶段**: 实现Router组件和基础路由规则
4. **第四阶段**: 重构后端连接逻辑为Outbound组件
5. **第五阶段**: 完整测试和性能优化

### 6.2 兼容性保证
- 保持现有API接口不变
- 支持旧配置格式
- 提供迁移工具和文档

## 7. 下一步行动

1. 创建核心接口定义
2. 实现基础的Inbound/Router/Outbound组件
3. 重构现有代码以适配新架构
4. 编写单元测试和集成测试
5. 性能测试和优化