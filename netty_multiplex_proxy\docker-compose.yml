version: '3.8'

services:
  proxy-server:
    build:
      context: proxy-server
      dockerfile: ./Dockerfile
    image: proxy-server:1.0.0
    container_name: proxy-server
    restart: unless-stopped
    ports:
      - "8888:8888"
    volumes:
      # 配置文件挂载 - 读写挂载
      # 将宿主机的 ./configs/production/server 目录挂载到容器的 /app/config 目录
      - ./configs/production/server:/app/config

      # 日志文件挂载 - 读写挂载
      # 将宿主机的 ./logs 目录挂载到容器的 /app/logs 目录
      - ./logs:/app/logs
      
    networks:
      - proxy-network
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

networks:
  proxy-network:
    driver: bridge
