# 代理服务器配置文件

# 服务器配置
server:
  port: 8888

# 认证配置
auth:
  enable: true
  username: admin
  password: password11
  timeout:
    seconds: 30

# 连接池配置 - 高性能优化
pool:
  enable: true
  max-connections:
    per-host: 50  # 增加每主机最大连接数
  idle-timeout:
    seconds: 120  # 增加空闲超时时间，减少连接重建
  cleanup-interval:
    seconds: 60   # 增加清理间隔，减少清理频率
  max-lifetime:
    seconds: 3600  # 连接最大存活时间1小时

# 性能监控配置
metrics:
  enable: true
  report:
    interval:
      seconds: 180  # 减少报告频率，降低监控开销

# 黑名单配置
blacklist:
  enable: true
  failure:
    threshold: 3  # 失败3次后加入黑名单
  cache:
    timeout:
      seconds: 300  # 增加黑名单缓存时间

# 性能配置
performance:
  # Boss线程数 (0表示自动计算)
  boss-threads: 0
  # 工作线程数 (0表示自动计算，基于CPU核心数和I/O比例)
  worker-threads: 0
  # I/O操作与CPU操作的比例 (1-100)，代理服务器通常为70-80
  io-ratio: 75
  # 是否启用智能线程优化
  enable-thread-optimization: true
  # 最大工作线程数 (0表示无限制，建议设置为CPU核心数的8倍)
  max-worker-threads: 0
  # 最小工作线程数 (建议至少为CPU核心数)
  min-worker-threads: 4

# SSL/TLS配置
#执行scripts/gen-ssl-certs.bat 自动生成证书
ssl:
  enable: true  # 是否启用SSL/TLS
  key-store-path: "server.p12"  # 密钥库路径（相对于classpath或绝对路径）
  key-store-password: "xiang1"  # 密钥库密码
  key-store-type: "PKCS12"  # 密钥库类型
  trust-store-path: "truststore.p12"  # 信任库路径（用于客户端认证）
  trust-store-password: "xiang1"  # 信任库密码
  trust-store-type: "PKCS12"  # 信任库类型
  protocols: # 支持的SSL/TLS协议版本
    - "TLSv1.2"
    - "TLSv1.3"
  # cipher-suites: # 支持的加密套件（可选）
  #   - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
  #   - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
  client-auth: false  # 是否要求客户端认证
  need-client-auth: false  # 是否必须客户端认证
  want-client-auth: false  # 是否可选客户端认证
  handshake-timeout:
    seconds: 30  # SSL握手超时时间

# 地理位置过滤配置
geo-location-filter:
  enable: false  # 是否启用地理位置过滤
  block-overseas-suspicious: false  # 是否阻止海外可疑网站
  enable-domain-filter: true  # 是否启用域名过滤
  enable-keyword-filter: true  # 是否启用关键字过滤
  enable-whitelist: true  # 是否启用白名单
  dns-cache-timeout:
    minutes: 5  # DNS缓存超时时间
  ip-cache-timeout:
    minutes: 60  # IP缓存超时时间
  max-cache-size: 10000  # 最大缓存条目数
  auto-update-ip-ranges: true  # 是否自动更新IP段数据
  update-interval:
    hours: 24  # 更新间隔时间
  online-data-sources:  # 在线数据源
    malicious-domains:
      - "https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts"
      - "https://someonewhocares.org/hosts/zero/hosts"
      - "https://raw.githubusercontent.com/AdguardTeam/AdguardFilters/master/BaseFilter/sections/adservers.txt"
    malicious-keywords:
      - "https://raw.githubusercontent.com/crazy-max/WindowsSpyBlocker/master/data/hosts/spy.txt"
    china-ip-ranges:
      - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
      - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"