package com.proxy.client.config.binder;

import com.proxy.client.config.annotation.ConfigurationProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 配置绑定器
 * 将 YAML 配置绑定到 Java Bean
 */
public class ConfigurationBinder {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationBinder.class);
    
    /**
     * 绑定配置到对象
     */
    public static <T> T bind(Map<String, Object> config, Class<T> targetClass) {
        try {
            T instance = targetClass.getDeclaredConstructor().newInstance();
            return bind(config, instance);
        } catch (Exception e) {
            logger.error("创建配置对象实例失败: {}", targetClass.getName(), e);
            throw new RuntimeException("Failed to create configuration instance", e);
        }
    }
    
    /**
     * 绑定配置到现有对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T bind(Map<String, Object> config, T target) {
        Class<?> targetClass = target.getClass();
        ConfigurationProperties annotation = targetClass.getAnnotation(ConfigurationProperties.class);
        
        Map<String, Object> sourceConfig = config;
        
        // 如果有前缀，获取对应的配置段
        if (annotation != null && !annotation.prefix().isEmpty()) {
            sourceConfig = getNestedConfig(config, annotation.prefix());
            if (sourceConfig == null) {
                logger.warn("未找到配置前缀: {}", annotation.prefix());
                return target;
            }
        }
        
        // 绑定字段
        bindFields(sourceConfig, target, annotation);
        
        return target;
    }
    
    /**
     * 绑定字段
     */
    private static void bindFields(Map<String, Object> config, Object target, ConfigurationProperties annotation) {
        Class<?> targetClass = target.getClass();
        Field[] fields = getAllFields(targetClass);
        
        for (Field field : fields) {
            try {
                bindField(config, target, field, annotation);
            } catch (Exception e) {
                if (annotation == null || !annotation.ignoreInvalidFields()) {
                    logger.error("绑定字段失败: {}.{}", targetClass.getSimpleName(), field.getName(), e);
                    throw new RuntimeException("Failed to bind field: " + field.getName(), e);
                } else {
                    logger.warn("忽略无效字段: {}.{} - {}", targetClass.getSimpleName(), field.getName(), e.getMessage());
                }
            }
        }
    }
    
    /**
     * 绑定单个字段
     */
    @SuppressWarnings("unchecked")
    private static void bindField(Map<String, Object> config, Object target, Field field, ConfigurationProperties annotation) throws Exception {
        String fieldName = field.getName();
        Object value = getConfigValue(config, fieldName);
        
        if (value == null) {
            return;
        }
        
        field.setAccessible(true);
        Class<?> fieldType = field.getType();
        
        logger.debug("绑定字段: {} ({}), 值类型: {}, 目标类型: {}, 泛型类型: {}", 
                    fieldName, target.getClass().getSimpleName(), value.getClass(), fieldType, field.getGenericType());
        
        // 转换并设置值
        Object convertedValue = convertValue(value, fieldType, field.getGenericType());
        field.set(target, convertedValue);
        
        logger.debug("绑定字段完成: {} = {} ({})", fieldName, convertedValue, convertedValue.getClass());
    }
    
    /**
     * 获取配置值，支持驼峰命名和短横线命名的转换
     */
    private static Object getConfigValue(Map<String, Object> config, String fieldName) {
        // 直接匹配
        if (config.containsKey(fieldName)) {
            return config.get(fieldName);
        }
        
        // 驼峰转短横线
        String kebabCase = camelToKebab(fieldName);
        if (config.containsKey(kebabCase)) {
            return config.get(kebabCase);
        }
        
        // 驼峰转下划线
        String snakeCase = camelToSnake(fieldName);
        if (config.containsKey(snakeCase)) {
            return config.get(snakeCase);
        }
        
        return null;
    }
    
    /**
     * 转换值类型
     */
    @SuppressWarnings("unchecked")
    private static Object convertValue(Object value, Class<?> targetType, Type genericType) {
        if (value == null) {
            return null;
        }
        
        logger.debug("convertValue: {} -> {}, 泛型: {}", value.getClass(), targetType, genericType);
        
        // 如果类型匹配，但是是泛型列表，需要检查元素类型
        if (targetType.isAssignableFrom(value.getClass())) {
            if (List.class.isAssignableFrom(targetType) && value instanceof List) {
                logger.debug("列表类型匹配，但需要检查元素类型");
                // 检查是否需要转换列表元素
                return convertToList(value, genericType);
            } else {
                logger.debug("类型匹配，直接返回");
                return value;
            }
        }
        
        // 基本类型转换
        if (targetType == String.class) {
            return value.toString();
        } else if (targetType == int.class || targetType == Integer.class) {
            return convertToInteger(value);
        } else if (targetType == long.class || targetType == Long.class) {
            return convertToLong(value);
        } else if (targetType == boolean.class || targetType == Boolean.class) {
            return convertToBoolean(value);
        } else if (targetType == double.class || targetType == Double.class) {
            return convertToDouble(value);
        } else if (targetType == float.class || targetType == Float.class) {
            return convertToFloat(value);
        } else if (targetType.isEnum()) {
            return convertToEnum(value, (Class<? extends Enum>) targetType);
        } else if (targetType.isArray()) {
            logger.debug("转换数组类型: {} -> {}", value.getClass(), targetType);
            return convertToArray(value, targetType);
        } else if (List.class.isAssignableFrom(targetType)) {
            logger.debug("转换列表类型: {} -> {}", value.getClass(), targetType);
            return convertToList(value, genericType);
        } else if (Map.class.isAssignableFrom(targetType)) {
            return convertToMap(value, genericType);
        } else if (value instanceof Map) {
            // 嵌套对象绑定
            try {
                Object nestedInstance = targetType.getDeclaredConstructor().newInstance();
                return bind((Map<String, Object>) value, nestedInstance);
            } catch (Exception e) {
                logger.error("创建嵌套对象失败: {}", targetType.getName(), e);
                throw new IllegalArgumentException("无法创建嵌套对象: " + targetType.getName(), e);
            }
        }

        throw new IllegalArgumentException("无法转换类型: " + value.getClass() + " -> " + targetType);
    }

    /**
     * 转换为整数
     */
    private static Integer convertToInteger(Object value) {
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return Integer.parseInt(value.toString());
    }

    /**
     * 转换为长整数
     */
    private static Long convertToLong(Object value) {
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return Long.parseLong(value.toString());
    }

    /**
     * 转换为布尔值
     */
    private static Boolean convertToBoolean(Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        String str = value.toString().toLowerCase();
        return "true".equals(str) || "yes".equals(str) || "1".equals(str);
    }

    /**
     * 转换为双精度浮点数
     */
    private static Double convertToDouble(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return Double.parseDouble(value.toString());
    }

    /**
     * 转换为单精度浮点数
     */
    private static Float convertToFloat(Object value) {
        if (value instanceof Number) {
            return ((Number) value).floatValue();
        }
        return Float.parseFloat(value.toString());
    }

    /**
     * 转换为枚举
     */
    @SuppressWarnings("unchecked")
    private static <E extends Enum<E>> E convertToEnum(Object value, Class<E> enumType) {
        String str = value.toString().toUpperCase();
        return Enum.valueOf(enumType, str);
    }

    /**
     * 转换为数组
     */
    @SuppressWarnings("unchecked")
    private static Object convertToArray(Object value, Class<?> arrayType) {
        if (!(value instanceof List)) {
            throw new IllegalArgumentException("值不是列表类型，无法转换为数组: " + value.getClass());
        }

        List<Object> sourceList = (List<Object>) value;
        Class<?> componentType = arrayType.getComponentType();

        logger.debug("转换数组，组件类型: {}, 源列表大小: {}", componentType, sourceList.size());

        // 创建目标数组
        Object targetArray = java.lang.reflect.Array.newInstance(componentType, sourceList.size());

        for (int i = 0; i < sourceList.size(); i++) {
            Object item = sourceList.get(i);
            Object convertedItem;

            if (componentType == String.class) {
                convertedItem = item.toString();
            } else if (componentType == int.class || componentType == Integer.class) {
                convertedItem = convertToInteger(item);
            } else if (componentType == long.class || componentType == Long.class) {
                convertedItem = convertToLong(item);
            } else if (componentType == boolean.class || componentType == Boolean.class) {
                convertedItem = convertToBoolean(item);
            } else if (componentType == double.class || componentType == Double.class) {
                convertedItem = convertToDouble(item);
            } else if (componentType == float.class || componentType == Float.class) {
                convertedItem = convertToFloat(item);
            } else {
                // 对于其他类型，尝试直接转换
                convertedItem = convertValue(item, componentType, componentType);
            }

            java.lang.reflect.Array.set(targetArray, i, convertedItem);
        }

        return targetArray;
    }

    /**
     * 转换为列表
     */
    @SuppressWarnings("unchecked")
    private static List<?> convertToList(Object value, Type genericType) {
        if (!(value instanceof List)) {
            throw new IllegalArgumentException("值不是列表类型: " + value.getClass());
        }

        List<Object> sourceList = (List<Object>) value;
        List<Object> targetList = new ArrayList<>();

        // 获取泛型类型
        Type elementType = Object.class;
        if (genericType instanceof ParameterizedType) {
            Type[] typeArgs = ((ParameterizedType) genericType).getActualTypeArguments();
            if (typeArgs.length > 0) {
                elementType = typeArgs[0];
            }
        }

        logger.debug("转换列表，元素类型: {}, 源列表大小: {}", elementType, sourceList.size());

        for (int i = 0; i < sourceList.size(); i++) {
            Object item = sourceList.get(i);
            logger.debug("处理列表元素 [{}]: {} ({})", i, item, item.getClass());

            if (elementType instanceof Class) {
                Class<?> elementClass = (Class<?>) elementType;
                Object convertedItem;

                // 如果是 Map 且目标类型是自定义类，进行嵌套绑定
                if (item instanceof Map && !elementClass.isPrimitive() &&
                    !elementClass.getName().startsWith("java.")) {
                    logger.debug("进行嵌套绑定: {} -> {}", item.getClass(), elementClass);
                    try {
                        Object nestedInstance = elementClass.getDeclaredConstructor().newInstance();
                        convertedItem = bind((Map<String, Object>) item, nestedInstance);
                        logger.debug("嵌套绑定成功: {}", convertedItem.getClass());
                    } catch (Exception e) {
                        logger.error("创建列表元素对象失败: {}", elementClass.getName(), e);
                        throw new IllegalArgumentException("无法创建列表元素对象: " + elementClass.getName(), e);
                    }
                } else {
                    logger.debug("使用常规转换: {} -> {}", item.getClass(), elementClass);
                    convertedItem = convertValue(item, elementClass, elementType);
                }

                targetList.add(convertedItem);
            } else {
                targetList.add(item);
            }
        }

        return targetList;
    }

    /**
     * 转换为映射
     */
    @SuppressWarnings("unchecked")
    private static Map<?, ?> convertToMap(Object value, Type genericType) {
        if (!(value instanceof Map)) {
            throw new IllegalArgumentException("值不是映射类型: " + value.getClass());
        }

        return (Map<?, ?>) value;
    }

    /**
     * 获取嵌套配置
     */
    @SuppressWarnings("unchecked")
    private static Map<String, Object> getNestedConfig(Map<String, Object> config, String prefix) {
        String[] parts = prefix.split("\\.");
        Map<String, Object> current = config;

        for (String part : parts) {
            Object value = current.get(part);
            if (value instanceof Map) {
                current = (Map<String, Object>) value;
            } else {
                return null;
            }
        }

        return current;
    }

    /**
     * 获取所有字段（包括父类）
     */
    private static Field[] getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();

        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }

        return fields.toArray(new Field[0]);
    }

    /**
     * 驼峰命名转短横线命名
     */
    private static String camelToKebab(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1-$2").toLowerCase();
    }

    /**
     * 驼峰命名转下划线命名
     */
    private static String camelToSnake(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
}
