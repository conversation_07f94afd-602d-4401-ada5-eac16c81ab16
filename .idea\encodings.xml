<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy_alicloud/auth-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy_alicloud/common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy_alicloud/gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy_alicloud/netty-multiplex-proxy-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy_alicloud/proxy-client-nacos/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy_alicloud/proxy-client-nacos/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy_alicloud/springcloud-alibaba-mybaits/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy_alicloud/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy_alicloud/src/main/resources" charset="UTF-8" />
  </component>
</project>