-cp
C:\\Users\\<USER>\\Desktop\\ai_gen\\netty_proxy\\netty_multiplex_proxy\\proxy-client\\target\\classes;E:\\.m2\\repository\\io\\netty\\netty-all\\4.1.100.Final\\netty-all-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-buffer\\4.1.100.Final\\netty-buffer-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec\\4.1.100.Final\\netty-codec-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-dns\\4.1.100.Final\\netty-codec-dns-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-haproxy\\4.1.100.Final\\netty-codec-haproxy-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-http\\4.1.100.Final\\netty-codec-http-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-http2\\4.1.100.Final\\netty-codec-http2-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-memcache\\4.1.100.Final\\netty-codec-memcache-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-mqtt\\4.1.100.Final\\netty-codec-mqtt-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-redis\\4.1.100.Final\\netty-codec-redis-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-smtp\\4.1.100.Final\\netty-codec-smtp-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-socks\\4.1.100.Final\\netty-codec-socks-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-stomp\\4.1.100.Final\\netty-codec-stomp-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-codec-xml\\4.1.100.Final\\netty-codec-xml-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-common\\4.1.100.Final\\netty-common-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-handler\\4.1.100.Final\\netty-handler-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-unix-common\\4.1.100.Final\\netty-transport-native-unix-common-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-handler-proxy\\4.1.100.Final\\netty-handler-proxy-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-handler-ssl-ocsp\\4.1.100.Final\\netty-handler-ssl-ocsp-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-resolver\\4.1.100.Final\\netty-resolver-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-resolver-dns\\4.1.100.Final\\netty-resolver-dns-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport\\4.1.100.Final\\netty-transport-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-rxtx\\4.1.100.Final\\netty-transport-rxtx-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-sctp\\4.1.100.Final\\netty-transport-sctp-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-udt\\4.1.100.Final\\netty-transport-udt-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-classes-epoll\\4.1.100.Final\\netty-transport-classes-epoll-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-classes-kqueue\\4.1.100.Final\\netty-transport-classes-kqueue-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-resolver-dns-classes-macos\\4.1.100.Final\\netty-resolver-dns-classes-macos-4.1.100.Final.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-epoll\\4.1.100.Final\\netty-transport-native-epoll-4.1.100.Final-linux-x86_64.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-epoll\\4.1.100.Final\\netty-transport-native-epoll-4.1.100.Final-linux-aarch_64.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-kqueue\\4.1.100.Final\\netty-transport-native-kqueue-4.1.100.Final-osx-x86_64.jar;E:\\.m2\\repository\\io\\netty\\netty-transport-native-kqueue\\4.1.100.Final\\netty-transport-native-kqueue-4.1.100.Final-osx-aarch_64.jar;E:\\.m2\\repository\\io\\netty\\netty-resolver-dns-native-macos\\4.1.100.Final\\netty-resolver-dns-native-macos-4.1.100.Final-osx-x86_64.jar;E:\\.m2\\repository\\io\\netty\\netty-resolver-dns-native-macos\\4.1.100.Final\\netty-resolver-dns-native-macos-4.1.100.Final-osx-aarch_64.jar;E:\\.m2\\repository\\org\\slf4j\\slf4j-api\\1.7.36\\slf4j-api-1.7.36.jar;E:\\.m2\\repository\\ch\\qos\\logback\\logback-classic\\1.2.12\\logback-classic-1.2.12.jar;E:\\.m2\\repository\\ch\\qos\\logback\\logback-core\\1.2.12\\logback-core-1.2.12.jar;E:\\.m2\\repository\\org\\yaml\\snakeyaml\\2.2\\snakeyaml-2.2.jar;E:\\.m2\\repository\\com\\alibaba\\nacos\\nacos-client\\2.3.2\\nacos-client-2.3.2.jar;E:\\.m2\\repository\\com\\alibaba\\nacos\\nacos-auth-plugin\\2.3.2\\nacos-auth-plugin-2.3.2.jar;E:\\.m2\\repository\\com\\alibaba\\nacos\\nacos-encryption-plugin\\2.3.2\\nacos-encryption-plugin-2.3.2.jar;E:\\.m2\\repository\\commons-codec\\commons-codec\\1.15\\commons-codec-1.15.jar;E:\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-core\\2.13.5\\jackson-core-2.13.5.jar;E:\\.m2\\repository\\org\\apache\\httpcomponents\\httpasyncclient\\4.1.5\\httpasyncclient-4.1.5.jar;E:\\.m2\\repository\\org\\apache\\httpcomponents\\httpcore-nio\\4.4.15\\httpcore-nio-4.4.15.jar;E:\\.m2\\repository\\org\\apache\\httpcomponents\\httpclient\\4.5.13\\httpclient-4.5.13.jar;E:\\.m2\\repository\\org\\apache\\httpcomponents\\httpcore\\4.4.16\\httpcore-4.4.16.jar;E:\\.m2\\repository\\io\\prometheus\\simpleclient\\0.15.0\\simpleclient-0.15.0.jar;E:\\.m2\\repository\\io\\prometheus\\simpleclient_tracer_otel\\0.15.0\\simpleclient_tracer_otel-0.15.0.jar;E:\\.m2\\repository\\io\\prometheus\\simpleclient_tracer_common\\0.15.0\\simpleclient_tracer_common-0.15.0.jar;E:\\.m2\\repository\\io\\prometheus\\simpleclient_tracer_otel_agent\\0.15.0\\simpleclient_tracer_otel_agent-0.15.0.jar;E:\\.m2\\repository\\io\\micrometer\\micrometer-core\\1.9.17\\micrometer-core-1.9.17.jar;E:\\.m2\\repository\\org\\hdrhistogram\\HdrHistogram\\2.1.12\\HdrHistogram-2.1.12.jar;E:\\.m2\\repository\\org\\latencyutils\\LatencyUtils\\2.0.3\\LatencyUtils-2.0.3.jar;E:\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-databind\\2.13.5\\jackson-databind-2.13.5.jar;E:\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-annotations\\2.13.5\\jackson-annotations-2.13.5.jar
--no-fallback
-o
C:\\Users\\<USER>\\Desktop\\ai_gen\\netty_proxy\\netty_multiplex_proxy\\proxy-client\\target\\proxy-client
--no-fallback
--enable-http
--enable-https
--enable-url-protocols=http,https
--initialize-at-build-time=org.slf4j
--initialize-at-build-time=ch.qos.logback
--initialize-at-run-time=io.netty
--initialize-at-run-time=io.netty.util.internal.shaded.org.jctools
--initialize-at-run-time=io.netty.buffer.PooledByteBufAllocator
--initialize-at-run-time=io.netty.util.internal.PlatformDependent
--initialize-at-run-time=io.netty.util.internal.PlatformDependent0
--allow-incomplete-classpath
-H:+UnlockExperimentalVMOptions
-H:+ReportExceptionStackTraces
-H:+AddAllCharsets
-H:IncludeResources=.*\\.properties$
-H:IncludeResources=.*\\.txt$
-H:IncludeResources=.*\\.xml$
-H:ReflectionConfigurationFiles=src/main/resources/META-INF/native-image/com.proxy.client/proxy-client/reflect-config.json
