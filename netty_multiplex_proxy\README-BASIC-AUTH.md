# Basic认证功能说明

本项目支持在proxy-client连接到proxy-server时使用Basic认证，提供额外的安全保护。

## 🔐 认证机制

### 认证流程
1. proxy-client连接到proxy-server
2. 如果服务器启用认证，client自动发送认证请求
3. 服务器验证用户名密码
4. 认证成功后，正常进行会话创建
5. 认证失败则关闭连接

### 协议扩展
在现有多路复用协议基础上添加了认证数据包：

```
TYPE_AUTH_REQUEST = 0x08   // 认证请求
TYPE_AUTH_RESPONSE = 0x09  // 认证响应
```

## ⚙️ 配置说明

### 服务器端配置

编辑 `proxy-server/src/main/resources/proxy-server.properties`：

```properties
# 认证配置
# 是否启用Basic认证
auth.enable=true

# 认证用户名和密码
auth.username=admin
auth.password=your_secure_password

# 认证超时时间（秒）
auth.timeout.seconds=30
```

### 客户端配置

编辑 `proxy-client/src/main/resources/proxy-client.properties`：

```properties
# 认证配置
# 是否启用Basic认证
auth.enable=true

# 认证用户名和密码
auth.username=admin
auth.password=your_secure_password

# 认证超时时间（秒）
auth.timeout.seconds=30
```

## 🚀 使用方法

### 1. 启用认证的服务器

```bash
# 修改服务器配置文件
cd proxy-server/src/main/resources/
# 编辑 proxy-server.properties，设置 auth.enable=true

# 启动服务器
cd proxy-server
mvn exec:java -Dexec.mainClass="com.proxy.server.ProxyServer" -Dexec.args="8888"
```

### 2. 启用认证的客户端

```bash
# 修改客户端配置文件
cd proxy-client/src/main/resources/
# 编辑 proxy-client.properties，设置 auth.enable=true

# 启动客户端
cd proxy-client
mvn exec:java -Dexec.mainClass="com.proxy.client.Socks5ProxyClient"
```

### 3. Native Image版本

认证功能完全支持GraalVM Native Image：

```cmd
# Windows
cd proxy-client
build-native-final-fix.bat
target\proxy-client.exe

# Linux/macOS
cd proxy-client
chmod +x build-native-final-fix.sh
./build-native-final-fix.sh
./target/proxy-client
```

## 🔧 配置选项详解

### 服务器端选项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `auth.enable` | `false` | 是否启用认证 |
| `auth.username` | `admin` | 认证用户名 |
| `auth.password` | `password` | 认证密码 |
| `auth.timeout.seconds` | `30` | 认证超时时间（秒） |

### 客户端选项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `auth.enable` | `false` | 是否启用认证 |
| `auth.username` | `admin` | 认证用户名 |
| `auth.password` | `password` | 认证密码 |
| `auth.timeout.seconds` | `30` | 认证超时时间（秒） |

## 🛡️ 安全建议

### 1. 密码安全
- **不要使用默认密码**：修改默认的 `password` 为强密码
- **使用复杂密码**：包含大小写字母、数字和特殊字符
- **定期更换密码**：建议定期更新认证密码

### 2. 网络安全
- **内网使用**：建议在可信内网环境中使用
- **VPN保护**：如需在公网使用，建议配合VPN
- **防火墙**：配置防火墙限制访问来源

### 3. 配置安全
- **文件权限**：确保配置文件权限设置正确
- **敏感信息**：避免在日志中输出密码信息

## 📊 认证状态监控

### 服务器端日志
```
INFO  - 连接 12345 认证成功，用户: admin
WARN  - 连接 12346 认证失败，用户: baduser，即将关闭连接
```

### 客户端日志
```
INFO  - 发送认证请求，用户名: admin
INFO  - 认证成功
ERROR - 认证失败，状态码: 4
```

## 🔍 故障排除

### 常见问题

1. **认证失败**
   - 检查用户名密码是否正确
   - 确认服务器端已启用认证
   - 查看服务器日志确认错误原因

2. **连接被拒绝**
   - 确认客户端已启用认证
   - 检查认证超时设置
   - 验证网络连接是否正常

3. **配置不生效**
   - 确认配置文件路径正确
   - 检查配置文件格式
   - 重启服务确保配置加载

### 调试方法

1. **启用详细日志**
   ```xml
   <!-- logback.xml -->
   <logger name="com.proxy.server.auth" level="DEBUG"/>
   <logger name="com.proxy.client.connection" level="DEBUG"/>
   ```

2. **检查认证状态**
   - 服务器端会输出认证统计信息
   - 客户端会显示认证成功/失败状态

## 🎯 最佳实践

1. **开发环境**：可以禁用认证方便调试
2. **测试环境**：启用认证验证功能正常
3. **生产环境**：必须启用认证并使用强密码
4. **监控告警**：监控认证失败次数，及时发现异常

## 📝 注意事项

1. **兼容性**：认证功能向后兼容，未启用时不影响现有功能
2. **性能影响**：认证过程对性能影响极小
3. **连接管理**：认证失败的连接会被自动关闭
4. **重连机制**：客户端会自动重连并重新认证

---

通过Basic认证功能，您可以为代理系统添加一层安全保护，确保只有授权用户才能使用代理服务。
