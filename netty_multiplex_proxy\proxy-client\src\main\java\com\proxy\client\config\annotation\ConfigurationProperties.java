package com.proxy.client.config.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 配置属性绑定注解
 * 类似于 Spring Boot 的 @ConfigurationProperties
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ConfigurationProperties {
    
    /**
     * 配置前缀
     */
    String prefix() default "";
    
    /**
     * 是否忽略未知属性
     */
    boolean ignoreUnknownFields() default true;
    
    /**
     * 是否忽略无效字段
     */
    boolean ignoreInvalidFields() default false;
}
