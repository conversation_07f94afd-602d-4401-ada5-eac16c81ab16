package com.xiang.proxy.server.exception;

import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 资源清理管理器
 * 确保在异常情况下正确清理资源，防止内存泄漏
 */
public class ResourceCleanupManager {
    private static final Logger logger = LoggerFactory.getLogger(ResourceCleanupManager.class);
    
    private static final ResourceCleanupManager INSTANCE = new ResourceCleanupManager();
    
    private ResourceCleanupManager() {}
    
    public static ResourceCleanupManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * 安全释放ByteBuf
     */
    public void safeRelease(ByteBuf buf, String context) {
        if (buf == null) {
            return;
        }
        
        try {
            if (buf.refCnt() > 0) {
                buf.release();
                logger.debug("ByteBuf释放成功: {} (引用计数: {})", context, buf.refCnt());
            }
        } catch (Exception e) {
            logger.warn("释放ByteBuf时发生异常: {} - {}", context, e.getMessage());
        }
    }
    
    /**
     * 安全关闭Channel
     */
    public void safeCloseChannel(Channel channel, String context) {
        if (channel == null) {
            return;
        }
        
        try {
            if (channel.isActive()) {
                channel.close().addListener(future -> {
                    if (future.isSuccess()) {
                        logger.debug("Channel关闭成功: {}", context);
                    } else {
                        logger.debug("Channel关闭失败: {} - {}", context, future.cause().getMessage());
                    }
                });
            } else {
                logger.debug("Channel已经关闭: {}", context);
            }
        } catch (Exception e) {
            logger.warn("关闭Channel时发生异常: {} - {}", context, e.getMessage());
        }
    }
    
    /**
     * 安全关闭ChannelHandlerContext
     */
    public void safeCloseContext(ChannelHandlerContext ctx, String context) {
        if (ctx == null) {
            return;
        }
        
        try {
            if (ctx.channel().isActive()) {
                ctx.close().addListener(future -> {
                    if (future.isSuccess()) {
                        logger.debug("ChannelHandlerContext关闭成功: {}", context);
                    } else {
                        logger.debug("ChannelHandlerContext关闭失败: {} - {}", context, future.cause().getMessage());
                    }
                });
            } else {
                logger.debug("ChannelHandlerContext已经关闭: {}", context);
            }
        } catch (Exception e) {
            logger.warn("关闭ChannelHandlerContext时发生异常: {} - {}", context, e.getMessage());
        }
    }
    
    /**
     * 清理会话资源
     */
    public void cleanupSessionResources(int sessionId, Channel backendChannel, ByteBuf buffer, 
                                      String connectionId) {
        logger.debug("开始清理会话资源: sessionId={}, connectionId={}", sessionId, connectionId);
        
        // 清理后端连接
        if (backendChannel != null) {
            safeCloseChannel(backendChannel, "会话" + sessionId + "后端连接");
        }
        
        // 清理缓冲区
        if (buffer != null) {
            safeRelease(buffer, "会话" + sessionId + "缓冲区");
        }
        
        logger.debug("会话资源清理完成: sessionId={}, connectionId={}", sessionId, connectionId);
    }
    
    /**
     * 清理连接相关的所有资源
     */
    public void cleanupConnectionResources(String connectionId, Channel clientChannel, 
                                         Map<Integer, Channel> sessions,
                                         Map<Integer, String> sessionHostKeys,
                                         ByteBuf buffer) {
        logger.debug("开始清理连接资源: connectionId={}", connectionId);
        
        // 清理所有会话的后端连接
        if (sessions != null) {
            sessions.forEach((sessionId, backendChannel) -> {
                if (backendChannel != null && backendChannel.isActive()) {
                    safeCloseChannel(backendChannel, "连接" + connectionId + "会话" + sessionId);
                }
            });
            sessions.clear();
        }
        
        // 清理会话主机键值映射
        if (sessionHostKeys != null) {
            sessionHostKeys.clear();
        }
        
        // 清理客户端连接
        if (clientChannel != null) {
            safeCloseChannel(clientChannel, "客户端连接" + connectionId);
        }
        
        // 清理缓冲区
        if (buffer != null) {
            safeRelease(buffer, "连接" + connectionId + "缓冲区");
        }
        
        logger.debug("连接资源清理完成: connectionId={}", connectionId);
    }
}