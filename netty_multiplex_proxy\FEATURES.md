# 🚀 功能特性详解

本文档详细介绍多路复用代理系统的核心功能特性和技术实现。

## 📋 目录

### 🌐 Proxy-Client 特性
- [多协议支持](#多协议支持)
- [UDP支持](#udp支持)
- [多组件架构](#多组件架构)
- [智能地址过滤](#智能地址过滤)
- [SSL/TLS加密](#ssltls加密)
- [认证系统](#认证系统)
- [配置管理](#配置管理)
- [GraalVM Native Image](#graalvm-native-image)

### 🛡️ 系统级特性
- [地理位置过滤系统](#地理位置过滤系统)
- [恶意内容过滤系统](#恶意内容过滤系统)
- [外部配置管理](#外部配置管理)
- [在线数据更新](#在线数据更新)
- [智能缓存系统](#智能缓存系统)
- [启动时初始化](#启动时初始化)

## 🌐 多协议支持

### 支持的协议
- **SOCKS5代理**: 完整的SOCKS5协议实现，支持TCP和UDP连接
- **HTTP CONNECT**: HTTP隧道代理协议，支持HTTPS流量
- **多路复用协议**: 自研高效多路复用协议（V2版本）

### 协议特性
``java
// SOCKS5协议处理
public class Socks5Inbound extends AbstractProxyInbound {
    @Override
    public ProxyProtocol getProtocol() {
        return ProxyProtocol.SOCKS5;
    }

    @Override
    protected ChannelInitializer<SocketChannel> createChannelInitializer() {
        return new MultiplexSocks5ProxyInitializer(
            connectionManager, addressFilter, configManager);
    }
}

// HTTP CONNECT协议处理
public class HttpInbound extends AbstractProxyInbound {
    @Override
    public ProxyProtocol getProtocol() {
        return ProxyProtocol.HTTP;
    }

    @Override
    protected ChannelInitializer<SocketChannel> createChannelInitializer() {
        return new HttpConnectProxyInitializer(
            connectionManager, addressFilter, configManager);
    }
}
```

## 📦 UDP支持

### 功能概述
系统现在支持通过SOCKS5协议的UDP ASSOCIATE命令进行UDP流量代理。UDP支持允许客户端通过代理服务器转发UDP数据包，这对于某些网络应用（如DNS查询、VoIP、在线游戏等）非常重要。

### 技术实现
- **UDP关联**: 通过SOCKS5 UDP ASSOCIATE命令建立UDP转发通道
- **数据封装**: UDP数据包通过多路复用协议在TCP连接中传输
- **地址解析**: 支持IPv4、IPv6和域名格式的目标地址
- **端口管理**: 动态管理UDP端口绑定和转发

### 实现细节
``java
// UDP会话创建
public int createUdpSession(String targetHost, int targetPort, SessionHandler handler) {
    // 生成临时sessionId
    int tempSessionId = tempSessionIdGenerator.getAndDecrement();
    pendingSessions.put(tempSessionId, handler);

    // 发送UDP连接请求
    MultiplexProtocol.Packet connectRequest =
        MultiplexProtocol.createUdpConnectRequest(targetHost, targetPort);
    sendPacket(connectRequest);

    return tempSessionId;
}

// UDP数据转发
public void sendUdpData(int sessionId, byte[] data) {
    MultiplexProtocol.Packet udpDataPacket = 
        MultiplexProtocol.createUdpDataPacket(sessionId, data);
    sendPacket(udpDataPacket);
}
```

### 数据格式
UDP数据在多路复用协议中使用特定格式进行传输：
```
[HOST_LEN][HOST][PORT][DATA_LEN][DATA]
```
其中：
- HOST_LEN: 目标主机名长度（1字节）
- HOST: 目标主机名（可变长度）
- PORT: 目标端口（2字节）
- DATA_LEN: 数据长度（2字节）
- DATA: 实际UDP数据（可变长度）

### 使用场景
- **DNS查询**: 通过代理服务器进行DNS解析
- **VoIP应用**: 语音通话数据转发
- **在线游戏**: 游戏数据包传输
- **实时通信**: 各种需要低延迟的UDP应用

## 🚀 多组件架构

### 架构优势
- **统一管理**: 所有协议共享同一个ConnectionManager
- **资源共享**: 共享地址过滤器和认证机制
- **独立端口**: 每个协议使用独立的监听端口
- **灵活配置**: 支持配置文件和命令行参数

### 组件管理
``java
public class ProxyClientManager {
    private final List<ProxyInbound> inbounds;
    private final ConnectionManager connectionManager;
    private final AddressFilter addressFilter;

    // 添加SOCKS5接入组件
    public ProxyClientManager addSocks5Inbound(int port) {
        return addInbound(new Socks5Inbound(port, connectionManager,
            addressFilter, configManager));
    }

    // 添加HTTP接入组件
    public ProxyClientManager addHttpInbound(int port) {
        return addInbound(new HttpInbound(port, connectionManager,
            addressFilter, configManager));
    }
}
```

### 配置示例
```
# 接入器配置 - 支持多个同类型接入器
inbound:
  # SOCKS5 接入器列表
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
      description: "主要SOCKS5代理"
    - name: "socks5-backup"
      port: 1083
      enabled: false
      description: "备用SOCKS5代理"

  # HTTP 接入器列表
  http:
    - name: "http-main"
      port: 1082
      enabled: true
      description: "主要HTTP代理"
```

## 🌍 智能地址过滤

### 过滤模式
- **ALL_PROXY**: 所有连接都通过proxy-server转发
- **CHINA_DIRECT**: 中国地区IP直连，其他通过proxy-server转发
- **ALL_DIRECT**: 所有连接都直连

### 地理位置判断
- **最新数据源**: 使用APNIC官方数据，每小时自动更新
- **多重数据源**: 配置文件 -> 在线数据源 -> 内置基础IP段
- **高准确性**: 覆盖99%以上的中国大陆IP地址
- **IPv4支持**: 完整支持IPv4地址判断（IPv6暂不支持）
- **私有地址**: 自动识别私有地址为中国IP

### 实现机制
``java
public class DefaultAddressFilter implements AddressFilter {
    @Override
    public ConnectionType shouldUseProxy(String host, int port) {
        switch (filterMode) {
            case ALL_PROXY:
                return ConnectionType.PROXY;
            case ALL_DIRECT:
                return ConnectionType.DIRECT;
            case CHINA_DIRECT:
                return isChineseIP(host) ? ConnectionType.DIRECT : ConnectionType.PROXY;
            default:
                return ConnectionType.PROXY;
        }
    }
}
```

## 🔒 SSL/TLS加密

### SSL功能特性
- **客户端SSL支持**: 完整的客户端SSL/TLS实现
- **多种认证模式**: 单向认证、双向认证、信任所有证书模式
- **协议版本支持**: TLSv1.2, TLSv1.3
- **证书格式支持**: PKCS12, JKS
- **开发测试友好**: 自签名证书支持

### SSL配置
```
ssl:
  enable: true  # 是否启用SSL/TLS连接到代理服务器
  trust-all: false  # 是否信任所有证书（仅用于测试）
  trust-store-path: "truststore.p12"  # 信任库路径
  trust-store-password: "password"  # 信任库密码
  trust-store-type: "PKCS12"  # 信任库类型
  key-store-path: "client.p12"  # 客户端证书路径（用于双向认证）
  key-store-password: "password"  # 客户端证书密码
  protocols:  # 支持的SSL/TLS协议版本
    - "TLSv1.2"
    - "TLSv1.3"
  verify-hostname: false  # 是否验证服务器主机名
  handshake-timeout-seconds: 30  # SSL握手超时时间
```

## 🔐 认证系统

### Basic认证
- **安全连接**: proxy-client连接到proxy-server时支持Basic认证
- **灵活配置**: 可选启用/禁用，支持自定义用户名密码
- **自动认证**: 客户端自动发送认证请求，无需手动干预
- **超时保护**: 认证超时自动断开连接，防止恶意连接

### 认证配置
```
# 认证配置
auth:
  enable: true
  username: "admin"
  password: "secure_password"
  timeout-seconds: 30
```

### 认证流程
```
public class ConnectionManager {
    private void sendAuthRequest(String username, String password) {
        MultiplexProtocol.Packet authRequest =
            MultiplexProtocol.createAuthRequestPacket(username, password);
        sendPacket(authRequest);
    }

    private void handleAuthResponse(MultiplexProtocol.Packet packet) {
        byte status = packet.getData()[0];
        if (status == MultiplexProtocol.STATUS_SUCCESS) {
            authenticated = true;
            authenticationInProgress = false;
            logger.info("认证成功");
        } else {
            logger.error("认证失败");
            disconnect();
        }
    }
}
```

## ⚙️ 配置管理

### 配置系统特性
- **分层配置**: 支持多层配置文件结构
- **外部配置**: 支持外部配置目录管理
- **YAML支持**: 完整的YAML配置文件支持
- **Properties兼容**: 向后兼容Properties格式
- **动态绑定**: 自动配置属性绑定和验证

### 配置属性类
```
@ConfigurationProperties
public class ProxyClientProperties {
    private FilterProperties filter = new FilterProperties();
    private ProxyProperties proxy = new ProxyProperties();
    private LocalProperties local = new LocalProperties();
    private AuthProperties auth = new AuthProperties();
    private InboundProperties inbound = new InboundProperties();
    private SslProperties ssl = new SslProperties();
    private OnlineDataSourcesProperties onlineDataSources = new OnlineDataSourcesProperties();
}
```

### 配置加载优先级
```
配置优先级（从高到低）：
1. 命令行参数
2. 系统属性 (-D参数)
3. 环境变量
4. YAML配置文件
5. Properties配置文件
6. 默认配置
```

### 配置文件示例
```
# 代理服务器配置
proxy:
  server:
    host: "localhost"
    port: 8888

# 地址过滤配置
filter:
  mode: "CHINA_DIRECT"  # ALL_PROXY, CHINA_DIRECT, ALL_DIRECT

# 本地监听配置
local:
  port: 1080

# 接入器配置
inbound:
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
  http:
    - name: "http-main"
      port: 1082
      enabled: true
```

## ⚡ GraalVM Native Image

### Native Image特性
- **极速启动**: 启动时间 < 100毫秒（vs JVM的2-3秒）
- **低内存占用**: 运行时内存 < 50MB（vs JVM的100-200MB）
- **单文件部署**: 无需安装Java环境，单个exe文件即可运行
- **即时响应**: 无JVM预热时间，立即达到最佳性能

### 构建配置
```
<!-- GraalVM Native Image Maven插件 -->
<plugin>
    <groupId>org.graalvm.buildtools</groupId>
    <artifactId>native-maven-plugin</artifactId>
    <version>0.9.28</version>
    <configuration>
        <imageName>proxy-client</imageName>
        <mainClass>com.proxy.client.ProxyClient</mainClass>
        <buildArgs>
            <buildArg>--no-fallback</buildArg>
            <buildArg>--enable-http</buildArg>
            <buildArg>--enable-https</buildArg>
            <buildArg>--initialize-at-run-time=io.netty</buildArg>
        </buildArgs>
    </configuration>
</plugin>
```

### 反射配置
```
// reflect-config.json
[
  {
    "name": "com.proxy.client.config.properties.ProxyClientProperties",
    "allDeclaredConstructors": true,
    "allPublicConstructors": true,
    "allDeclaredMethods": true,
    "allPublicMethods": true,
    "allDeclaredFields": true,
    "allPublicFields": true
  }
]
```

### 构建脚本
```
@echo off
echo 开始构建GraalVM Native Image...

REM 清理并编译
call mvn clean compile

REM 构建Native Image
call mvn -Pnative native:compile-no-fork

echo Native Image构建完成: target\proxy-client.exe
```

## 🌍 地理位置过滤系统

### 功能概述
基于IP地理位置的智能访问控制系统，支持精确的中国/海外IP判断和灵活的过滤策略。

### 核心特性
- **高精度IP判断**: 基于APNIC官方数据，准确识别中国IP段
- **多层过滤机制**: 域名黑名单、关键词检测、海外可疑网站阻止
- **白名单保护**: 160+个精心筛选的合法海外网站白名单
- **智能缓存**: DNS和IP地理位置缓存，提升判断性能
- **自动更新**: 定期从官方数据源更新IP段数据

### 技术实现
```
// 地理位置过滤核心逻辑
public FilterResult checkHostAccess(String host, int port) {
    // 1. 检查白名单（如果启用）
    if (configManager.isWhitelistEnabled() && isWhitelistDomain(host)) {
        return FilterResult.allowed("域名在白名单中");
    }

    // 2. 检查恶意域名黑名单（如果启用域名过滤）
    if (configManager.isDomainFilterEnabled() && isMaliciousDomain(host)) {
        return FilterResult.blocked("域名在恶意黑名单中");
    }

    // 3. 检查恶意关键词（如果启用关键词过滤）
    if (configManager.isKeywordFilterEnabled() && containsMaliciousKeywords(host)) {
        return FilterResult.blocked("域名包含恶意关键词");
    }

    // 4. 进行地理位置检查（如果启用海外可疑网站阻止）
    if (configManager.isBlockOverseasSuspicious()) {
        // IP地理位置判断逻辑
    }

    return FilterResult.allowed("通过所有过滤检查");
}
```

## 🛡️ 恶意内容过滤系统

### 功能概述
多层恶意内容检测和过滤系统，提供全面的网络安全保护。

### 过滤层级

#### 1. 恶意域名黑名单
- **数据源**: 多个在线威胁情报源
- **更新机制**: 自动从在线数据源获取最新恶意域名
- **格式支持**: hosts文件格式和纯域名列表
- **存储位置**: `malicious-domains.txt`

#### 2. 恶意关键词检测
- **智能提取**: 从恶意域名中自动提取可疑关键词
- **模式匹配**: 支持正则表达式和字符串匹配
- **关键词验证**: 智能过滤和验证关键词有效性
- **存储位置**: `malicious-keywords.txt`

#### 3. 白名单保护系统
- **精选域名**: 160+个合法海外网站
- **分类管理**: 按教育、科技、新闻、医疗等领域分类
- **定期更新**: 根据最新的合法网站信息更新
- **存储位置**: `whitelist-domains.txt`

### 白名单域名分类
```
搜索引擎 (10个): Google、Bing、DuckDuckGo等
教育机构 (51个): MIT、Stanford、Harvard、Oxford等世界顶级大学
技术开发 (25个): GitHub、云服务、开发工具
办公生产力 (20个): 微软、Google办公套件
新闻媒体 (29个): BBC、CNN、Reuters等权威媒体
科技媒体 (25个): TechCrunch、Wired、The Verge等
```

## 📁 外部配置管理

### 配置目录结构
```
config.ext.dir/
├── proxy-server.yml            # 主配置文件
├── china-ip-ranges.txt         # 中国IP段数据
├── malicious-domains.txt       # 恶意域名黑名单
├── malicious-keywords.txt      # 恶意关键词列表
└── whitelist-domains.txt       # 白名单域名
```

### 配置加载优先级
1. **配置目录** (`config.ext.dir`): 优先从指定目录加载
2. **当前目录**: 配置目录不存在时的备选方案
3. **内置配置**: 外部配置加载失败时的默认配置

### 自动发现机制
- **服务器配置**: `proxy-server.yml` > `proxy-server-dev-*.yml` > 包含`proxy-server`的yml文件
- **客户端配置**: `proxy-client.yml` > `proxy-client-dev-*.yml` > 包含`proxy-client`的yml文件

## 📡 在线数据更新

### 配置化数据源

#### 1. 服务器端数据源配置
```
geo-location-filter:
  online-data-sources:
    # 恶意域名数据源
    malicious-domains:
      - "https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts"
      - "https://someonewhocares.org/hosts/zero/hosts"
      - "https://raw.githubusercontent.com/AdguardTeam/AdguardFilters/master/BaseFilter/sections/adservers.txt"

    # 恶意关键词数据源
    malicious-keywords:
      - "https://raw.githubusercontent.com/crazy-max/WindowsSpyBlocker/master/data/hosts/spy.txt"

    # 中国IP段数据源
    china-ip-ranges:
      - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
      - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
```

#### 2. 客户端数据源配置
```
online-data-sources:
  # 中国IP段数据源
  china-ip-ranges:
    - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
    - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
```

#### 3. 动态数据源加载
```
// 服务器端获取恶意域名数据源
private static java.util.List<String> getMaliciousDomainsUrls() {
    try {
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
        return configManager.getMaliciousDomainsUrls();
    } catch (Exception e) {
        // 配置加载失败时使用默认值
        return java.util.Arrays.asList(/* 默认URL列表 */);
    }
}

// 客户端获取中国IP段数据源
private static java.util.List<String> getOnlineDataSources() {
    try {
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        return configManager.getChinaIpRangesUrls();
    } catch (Exception e) {
        // 配置加载失败时使用默认值
        return java.util.Arrays.asList(/* 默认URL列表 */);
    }
}
```

### 更新机制
- **异步更新**: 不阻塞系统启动和正常运行
- **多源备份**: 支持多个数据源，确保可用性
- **智能合并**: 自动合并多个数据源的内容
- **自动保存**: 更新成功后自动保存到配置目录

## 💾 智能缓存系统

### 缓存类型

#### 1. DNS解析缓存
- **用途**: 缓存域名到IP的解析结果
- **超时时间**: 可配置，默认10分钟
- **容量限制**: 可配置最大缓存条目数

#### 2. IP地理位置缓存
- **用途**: 缓存IP地理位置判断结果
- **超时时间**: 可配置，默认120分钟
- **容量限制**: 与DNS缓存共享容量限制

### 缓存管理
```
// 缓存清理逻辑
public void cleanupCache() {
    // 清理过期的DNS缓存
    // 清理过期的IP地理位置缓存
    // 清理最旧的缓存条目以保持大小限制
}
```

## 🚀 启动时初始化

### 初始化流程
1. **配置文件加载**: 从配置目录加载所有配置文件
2. **组件初始化**: 初始化地理位置过滤器和恶意内容加载器
3. **在线数据更新**: 异步执行在线数据源更新
4. **缓存预热**: 预加载常用的解析结果
5. **定时任务启动**: 启动定期更新和缓存清理任务

### 容错机制
- **配置文件容错**: 外部配置加载失败时使用内置默认配置
- **网络容错**: 在线更新失败时继续使用本地数据
- **异常隔离**: 单个组件初始化失败不影响整体系统启动

### 性能优化
- **异步初始化**: 耗时操作异步执行，不阻塞启动
- **并发安全**: 所有缓存和数据结构都是线程安全的
- **内存优化**: 合理的缓存大小限制和清理策略
