@echo off
chcp 65001 >nul
REM SSL Certificate Generation Script - Windows Version
REM For generating self-signed certificates for development and testing

echo ========================================
echo SSL Certificate Generation Script
echo ========================================

set SERVER_KEYSTORE=server.p12
set CLIENT_KEYSTORE=client.p12
set TRUSTSTORE=truststore.p12
set PASSWORD=xiang1
set VALIDITY=365

echo.
echo Generating server certificate...
keytool -genkeypair ^
    -alias server ^
    -keyalg RSA ^
    -keysize 2048 ^
    -storetype PKCS12 ^
    -keystore %SERVER_KEYSTORE% ^
    -storepass %PASSWORD% ^
    -keypass %PASSWORD% ^
    -validity %VALIDITY% ^
    -dname "CN=localhost,OU=Development,O=ProxyServer,L=Beijing,ST=Beijing,C=CN" ^
    -ext SAN=dns:localhost,ip:127.0.0.1

if %ERRORLEVEL% neq 0 (
    echo Server certificate generation failed!
    pause
    exit /b 1
)

echo Server certificate generated successfully: %SERVER_KEYSTORE%

echo.
echo Generating client certificate...
keytool -genkeypair ^
    -alias client ^
    -keyalg RSA ^
    -keysize 2048 ^
    -storetype PKCS12 ^
    -keystore %CLIENT_KEYSTORE% ^
    -storepass %PASSWORD% ^
    -keypass %PASSWORD% ^
    -validity %VALIDITY% ^
    -dname "CN=proxy-client,OU=Development,O=ProxyClient,L=Beijing,ST=Beijing,C=CN"

if %ERRORLEVEL% neq 0 (
    echo Client certificate generation failed!
    pause
    exit /b 1
)

echo Client certificate generated successfully: %CLIENT_KEYSTORE%

echo.
echo Creating truststore...

REM Export server certificate
keytool -exportcert ^
    -alias server ^
    -keystore %SERVER_KEYSTORE% ^
    -storepass %PASSWORD% ^
    -file server.crt

REM Export client certificate
keytool -exportcert ^
    -alias client ^
    -keystore %CLIENT_KEYSTORE% ^
    -storepass %PASSWORD% ^
    -file client.crt

REM Create truststore and import server certificate (for client use)
keytool -importcert ^
    -alias server ^
    -keystore %TRUSTSTORE% ^
    -storepass %PASSWORD% ^
    -file server.crt ^
    -noprompt

REM Import client certificate to truststore (for server mutual authentication)
keytool -importcert ^
    -alias client ^
    -keystore %TRUSTSTORE% ^
    -storepass %PASSWORD% ^
    -file client.crt ^
    -noprompt

echo Truststore created successfully: %TRUSTSTORE%

echo.
echo Copying certificates to project directories...

REM Copy server certificates to proxy-server
copy %SERVER_KEYSTORE% ..\proxy-server\src\main\resources\
copy %TRUSTSTORE% ..\proxy-server\src\main\resources\

REM Copy client certificates to proxy-client
copy %CLIENT_KEYSTORE% ..\proxy-client\src\main\resources\
copy %TRUSTSTORE% ..\proxy-client\src\main\resources\

echo.
echo Cleaning up temporary files...
del server.crt
del client.crt

echo.
echo ========================================
echo Certificate generation completed!
echo ========================================
echo Server certificate: %SERVER_KEYSTORE%
echo Client certificate: %CLIENT_KEYSTORE%
echo Truststore: %TRUSTSTORE%
echo Password: %PASSWORD%
echo Validity: %VALIDITY% days
echo.
echo Certificates have been copied to respective project resource directories
echo ========================================

pause
