package com.proxy.client.ssl;

import com.proxy.client.config.ProxyClientConfigManager;
import com.proxy.client.config.properties.ProxyClientProperties;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.X509Certificate;
import java.util.Arrays;

/**
 * 客户端SSL上下文管理器
 * 负责创建和管理客户端SSL上下文
 */
public class ClientSslContextManager {
    private static final Logger logger = LoggerFactory.getLogger(ClientSslContextManager.class);
    
    private static ClientSslContextManager instance;
    private SslContext clientSslContext;
    private final ProxyClientConfigManager configManager;
    
    private ClientSslContextManager() {
        this.configManager = ProxyClientConfigManager.getInstance();
    }
    
    public static synchronized ClientSslContextManager getInstance() {
        if (instance == null) {
            instance = new ClientSslContextManager();
        }
        return instance;
    }
    
    /**
     * 获取客户端SSL上下文
     */
    public SslContext getClientSslContext() {
        if (clientSslContext == null) {
            synchronized (this) {
                if (clientSslContext == null) {
                    clientSslContext = createClientSslContext();
                }
            }
        }
        return clientSslContext;
    }
    
    /**
     * 创建客户端SSL上下文
     */
    private SslContext createClientSslContext() {
        try {
            ProxyClientProperties.SslProperties sslConfig = configManager.getProperties().getSsl();
            
            if (!sslConfig.isEnable()) {
                logger.info("客户端SSL功能未启用");
                return null;
            }
            
            SslContextBuilder builder = SslContextBuilder.forClient();
            
            // 配置信任管理器
            if (sslConfig.isTrustAll()) {
                // 信任所有证书（仅用于测试）
                builder.trustManager(InsecureTrustManagerFactory.INSTANCE);
                logger.warn("使用不安全的信任管理器，信任所有证书（仅用于开发测试）");
            } else if (sslConfig.getTrustStorePath() != null && !sslConfig.getTrustStorePath().trim().isEmpty()) {
                // 使用配置的信任库
                configureTrustStore(builder, sslConfig);
                logger.info("使用配置的信任库: {}", sslConfig.getTrustStorePath());
            } else {
                // 使用系统默认信任库
                logger.info("使用系统默认信任库");
            }
            
            // 配置客户端证书（用于双向认证）
            if (sslConfig.getKeyStorePath() != null && !sslConfig.getKeyStorePath().trim().isEmpty()) {
                configureKeyStore(builder, sslConfig);
                logger.info("配置客户端证书: {}", sslConfig.getKeyStorePath());
            }
            
            // 配置协议版本
            if (sslConfig.getProtocols() != null && sslConfig.getProtocols().length > 0) {
                builder.protocols(sslConfig.getProtocols());
                logger.info("配置SSL协议: {}", Arrays.toString(sslConfig.getProtocols()));
            }

            // 配置密码套件
            if (sslConfig.getCipherSuites() != null && sslConfig.getCipherSuites().length > 0) {
                builder.ciphers(Arrays.asList(sslConfig.getCipherSuites()));
                logger.info("配置密码套件: {}", Arrays.toString(sslConfig.getCipherSuites()));
            }

            // 如果禁用主机名验证，需要特殊处理
            if (!sslConfig.isVerifyHostname()) {
                logger.warn("已禁用主机名验证，这在生产环境中不安全");
            }
            
            SslContext context = builder.build();
            logger.info("客户端SSL上下文创建成功");
            return context;
            
        } catch (Exception e) {
            logger.error("创建客户端SSL上下文失败", e);
            throw new RuntimeException("Failed to create client SSL context", e);
        }
    }
    
    /**
     * 配置信任库
     */
    private void configureTrustStore(SslContextBuilder builder, ProxyClientProperties.SslProperties sslConfig) throws Exception {
        KeyStore trustStore = KeyStore.getInstance(sslConfig.getTrustStoreType());
        
        try (InputStream trustStoreStream = loadKeyStoreStream(sslConfig.getTrustStorePath())) {
            trustStore.load(trustStoreStream, sslConfig.getTrustStorePassword().toCharArray());
        }
        
        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustManagerFactory.init(trustStore);
        
        builder.trustManager(trustManagerFactory);
    }
    
    /**
     * 配置密钥库（客户端证书）
     */
    private void configureKeyStore(SslContextBuilder builder, ProxyClientProperties.SslProperties sslConfig) throws Exception {
        KeyStore keyStore = KeyStore.getInstance(sslConfig.getKeyStoreType());
        
        try (InputStream keyStoreStream = loadKeyStoreStream(sslConfig.getKeyStorePath())) {
            keyStore.load(keyStoreStream, sslConfig.getKeyStorePassword().toCharArray());
        }
        
        KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        keyManagerFactory.init(keyStore, sslConfig.getKeyStorePassword().toCharArray());
        
        builder.keyManager(keyManagerFactory);
    }
    
    /**
     * 加载密钥库流
     */
    private InputStream loadKeyStoreStream(String path) throws Exception {
        // 首先尝试从类路径加载
        InputStream stream = getClass().getClassLoader().getResourceAsStream(path);
        if (stream != null) {
            logger.debug("从类路径加载密钥库: {}", path);
            return stream;
        }
        
        // 然后尝试从文件系统加载
        try {
            stream = new FileInputStream(path);
            logger.debug("从文件系统加载密钥库: {}", path);
            return stream;
        } catch (Exception e) {
            logger.error("无法加载密钥库: {}", path, e);
            throw new RuntimeException("Cannot load keystore: " + path, e);
        }
    }
    
    /**
     * 检查SSL是否启用
     */
    public boolean isSslEnabled() {
        return configManager.getProperties().getSsl().isEnable();
    }
    
    /**
     * 是否验证主机名
     */
    public boolean isVerifyHostname() {
        return configManager.getProperties().getSsl().isVerifyHostname();
    }
    
    /**
     * 获取SSL握手超时时间（秒）
     */
    public int getHandshakeTimeoutSeconds() {
        return configManager.getProperties().getSsl().getHandshakeTimeoutSeconds();
    }
    
    /**
     * 获取SSL配置摘要
     */
    public String getSslConfigSummary() {
        ProxyClientProperties.SslProperties sslConfig = configManager.getProperties().getSsl();
        if (!sslConfig.isEnable()) {
            return "Client SSL: disabled";
        }
        
        return String.format(
            "Client SSL: enabled, trustAll=%s, trustStore=%s, keyStore=%s, protocols=%s, verifyHostname=%s",
            sslConfig.isTrustAll(),
            sslConfig.getTrustStorePath().isEmpty() ? "system-default" : sslConfig.getTrustStorePath(),
            sslConfig.getKeyStorePath().isEmpty() ? "none" : sslConfig.getKeyStorePath(),
            Arrays.toString(sslConfig.getProtocols()),
            sslConfig.isVerifyHostname()
        );
    }
    
    /**
     * 重新加载SSL上下文
     */
    public synchronized void reloadSslContext() {
        logger.info("重新加载客户端SSL上下文");
        clientSslContext = null;
        // 下次调用getClientSslContext()时会重新创建
    }
    
    /**
     * 创建信任所有证书的TrustManager（仅用于测试）
     */
    public static X509TrustManager createTrustAllManager() {
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
                // 信任所有客户端证书
            }
            
            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
                // 信任所有服务器证书
            }
            
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        };
    }
}
