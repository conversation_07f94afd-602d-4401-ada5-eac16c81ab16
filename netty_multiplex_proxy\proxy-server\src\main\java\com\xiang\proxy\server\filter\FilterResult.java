package com.xiang.proxy.server.filter;

/**
 * 过滤结果类
 * 表示地理位置过滤的结果
 */
public class FilterResult {
    private final boolean allowed;
    private final String reason;
    private final BlockReason blockReason;
    
    private FilterResult(boolean allowed, String reason, BlockReason blockReason) {
        this.allowed = allowed;
        this.reason = reason;
        this.blockReason = blockReason;
    }
    
    /**
     * 创建允许访问的结果
     */
    public static FilterResult allowed(String reason) {
        return new FilterResult(true, reason, null);
    }
    
    /**
     * 创建阻止访问的结果
     */
    public static FilterResult blocked(String reason, BlockReason blockReason) {
        return new FilterResult(false, reason, blockReason);
    }
    
    /**
     * 是否允许访问
     */
    public boolean isAllowed() {
        return allowed;
    }
    
    /**
     * 是否被阻止
     */
    public boolean isBlocked() {
        return !allowed;
    }
    
    /**
     * 获取原因描述
     */
    public String getReason() {
        return reason;
    }
    
    /**
     * 获取阻止原因类型
     */
    public BlockReason getBlockReason() {
        return blockReason;
    }
    
    @Override
    public String toString() {
        return String.format("FilterResult{allowed=%s, reason='%s', blockReason=%s}", 
                           allowed, reason, blockReason);
    }
}
