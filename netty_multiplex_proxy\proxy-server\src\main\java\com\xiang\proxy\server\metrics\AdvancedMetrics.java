package com.xiang.proxy.server.metrics;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 高级性能指标收集器
 * 提供更详细的性能分析和监控功能
 */
public class AdvancedMetrics {
    private static final Logger logger = LoggerFactory.getLogger(AdvancedMetrics.class);

    private static final AdvancedMetrics INSTANCE = new AdvancedMetrics();

    // 延迟统计
    private final ConcurrentHashMap<String, LatencyTracker> latencyTrackers = new ConcurrentHashMap<>();

    // 吞吐量统计
    private final LongAdder totalRequests = new LongAdder();
    private final LongAdder totalResponses = new LongAdder();
    private final AtomicLong lastThroughputCheck = new AtomicLong(System.currentTimeMillis());
    private volatile double currentThroughput = 0.0;

    // 错误率统计
    private final ConcurrentHashMap<String, LongAdder> errorCounters = new ConcurrentHashMap<>();

    // 连接质量统计
    private final ConcurrentHashMap<String, ConnectionQualityTracker> connectionQuality = new ConcurrentHashMap<>();

    // 内存使用统计
    private final LongAdder memoryAllocated = new LongAdder();
    private final LongAdder memoryReleased = new LongAdder();

    // 协议统计
    private final ConcurrentHashMap<String, LongAdder> protocolCounters = new ConcurrentHashMap<>();

    // 连接池统计
    private final LongAdder poolHits = new LongAdder();
    private final LongAdder poolMisses = new LongAdder();
    private final LongAdder poolCreations = new LongAdder();
    private final LongAdder poolReturns = new LongAdder();

    // 数据传输统计
    private final LongAdder bytesReceived = new LongAdder();
    private final LongAdder bytesSent = new LongAdder();

    private AdvancedMetrics() {
        // 初始化基础延迟跟踪器
        latencyTrackers.put("connection", new LatencyTracker("connection"));
        latencyTrackers.put("request", new LatencyTracker("request"));
        latencyTrackers.put("response", new LatencyTracker("response"));
    }

    public static AdvancedMetrics getInstance() {
        return INSTANCE;
    }

    /**
     * 记录延迟
     */
    public void recordLatency(String operation, long latencyMs) {
        LatencyTracker tracker = latencyTrackers.computeIfAbsent(operation, LatencyTracker::new);
        tracker.record(latencyMs);
    }

    /**
     * 记录请求
     */
    public void recordRequest() {
        totalRequests.increment();
        updateThroughput();
    }

    /**
     * 记录响应
     */
    public void recordResponse() {
        totalResponses.increment();
    }

    /**
     * 记录错误
     */
    public void recordError(String errorType) {
        errorCounters.computeIfAbsent(errorType, k -> new LongAdder()).increment();
    }


    /**
     * 记录连接质量
     */
    public void recordConnectionQuality(String host, boolean success, long responseTime) {
        ConnectionQualityTracker tracker = connectionQuality.computeIfAbsent(host, k -> new ConnectionQualityTracker());
        tracker.record(success, responseTime);
    }

    /**
     * 记录内存分配
     */
    public void recordMemoryAllocation(long bytes) {
        memoryAllocated.add(bytes);
    }

    /**
     * 记录内存释放
     */
    public void recordMemoryRelease(long bytes) {
        memoryReleased.add(bytes);
    }

    /**
     * 记录协议使用
     */
    public void recordProtocolUsage(String protocol) {
        protocolCounters.computeIfAbsent(protocol, k -> new LongAdder()).increment();
    }

    /**
     * 记录连接池命中
     */
    public void recordPoolHit() {
        poolHits.increment();
    }

    /**
     * 记录连接池未命中
     */
    public void recordPoolMiss() {
        poolMisses.increment();
    }

    /**
     * 记录连接池创建
     */
    public void recordPoolCreation() {
        poolCreations.increment();
    }

    /**
     * 记录连接池归还
     */
    public void recordPoolReturn() {
        poolReturns.increment();
    }

    /**
     * 记录接收字节数
     */
    public void recordBytesReceived(long bytes) {
        bytesReceived.add(bytes);
    }

    /**
     * 记录发送字节数
     */
    public void recordBytesSent(long bytes) {
        bytesSent.add(bytes);
    }

    /**
     * 重置所有指标（仅用于测试）
     */
    public void resetAllMetrics() {
        totalRequests.reset();
        totalResponses.reset();
        lastThroughputCheck.set(System.currentTimeMillis());
        currentThroughput = 0.0;
        
        errorCounters.clear();
        connectionQuality.clear();
        latencyTrackers.clear();
        
        // 重新初始化基础延迟跟踪器
        latencyTrackers.put("connection", new LatencyTracker("connection"));
        latencyTrackers.put("request", new LatencyTracker("request"));
        latencyTrackers.put("response", new LatencyTracker("response"));
        
        memoryAllocated.reset();
        memoryReleased.reset();
        protocolCounters.clear();
        poolHits.reset();
        poolMisses.reset();
        poolCreations.reset();
        poolReturns.reset();
        bytesReceived.reset();
        bytesSent.reset();
    }

    /**
     * 更新吞吐量
     */
    private void updateThroughput() {
        long now = System.currentTimeMillis();
        long lastCheck = lastThroughputCheck.get();

        if (now - lastCheck > 1000) { // 每秒更新一次
            if (lastThroughputCheck.compareAndSet(lastCheck, now)) {
                long requests = totalRequests.sum();
                double timeSeconds = (now - lastCheck) / 1000.0;
                currentThroughput = requests / timeSeconds;
            }
        }
    }

    /**
     * 获取延迟统计
     */
    public LatencyStats getLatencyStats(String operation) {
        LatencyTracker tracker = latencyTrackers.get(operation);
        return tracker != null ? tracker.getStats() : null;
    }

    /**
     * 获取吞吐量
     */
    public double getCurrentThroughput() {
        return currentThroughput;
    }

    /**
     * 获取错误率
     */
    public double getErrorRate(String errorType) {
        LongAdder errorCounter = errorCounters.get(errorType);
        long errors = errorCounter != null ? errorCounter.sum() : 0;
        long total = totalRequests.sum();
        return total > 0 ? (double) errors / total * 100.0 : 0.0;
    }


    /**
     * 获取连接质量统计
     */
    public List<ConnectionQualityStats> getConnectionQualityStats() {
        return connectionQuality.entrySet().stream()
                .map(entry -> entry.getValue().getStats(entry.getKey()))
                .sorted((a, b) -> Double.compare(b.successRate, a.successRate))
                .collect(Collectors.toList());
    }

    /**
     * 获取内存统计
     */
    public MemoryStats getMemoryStats() {
        return new MemoryStats(memoryAllocated.sum(), memoryReleased.sum());
    }

    /**
     * 获取协议统计
     */
    public Map<String, Long> getProtocolStats() {
        return protocolCounters.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().sum()
                ));
    }

    /**
     * 获取连接池统计
     */
    public PoolStats getPoolStats() {
        return new PoolStats(
                poolHits.sum(),
                poolMisses.sum(),
                poolCreations.sum(),
                poolReturns.sum()
        );
    }

    /**
     * 获取数据传输统计
     */
    public DataTransferStats getDataTransferStats() {
        return new DataTransferStats(bytesReceived.sum(), bytesSent.sum());
    }

    /**
     * 生成性能报告
     */
    public PerformanceReport generateReport() {
        return new PerformanceReport(
                totalRequests.sum(),
                totalResponses.sum(),
                currentThroughput,
                getLatencyStats("request"),
                getTopErrors(),
                getConnectionQualityStats(),
                getMemoryStats(),
                getProtocolStats(),
                getPoolStats(),
                getDataTransferStats()
        );
    }

    /**
     * 获取主要错误类型
     */
    private Map<String, Long> getTopErrors() {
        return errorCounters.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().sum()
                ));
    }

    /**
     * 记录详细性能报告
     */
    public void logDetailedReport() {
        PerformanceReport report = generateReport();

        logger.info("=== 高级性能报告 ===");
        logger.info("请求统计: 总数={}, 响应数={}, 吞吐量={} req/s",
                report.totalRequests, report.totalResponses, String.format("%.2f", report.throughput));

        if (report.requestLatency != null) {
            logger.info("请求延迟: 平均={}ms, P50={}ms, P95={}ms, P99={}ms",
                    String.format("%.2f", report.requestLatency.average),
                    report.requestLatency.p50,
                    report.requestLatency.p95,
                    report.requestLatency.p99);
        }

        // 内存统计
        if (report.memoryStats != null) {
            logger.info("内存统计: 已分配={}MB, 已释放={}MB, 净使用={}MB",
                    String.format("%.2f", report.memoryStats.allocated / 1024.0 / 1024.0),
                    String.format("%.2f", report.memoryStats.released / 1024.0 / 1024.0),
                    String.format("%.2f", report.memoryStats.getNetUsage() / 1024.0 / 1024.0));
        }

        // 协议统计
        if (!report.protocolStats.isEmpty()) {
            logger.info("协议统计: {}", report.protocolStats);
        }

        // 连接池统计
        if (report.poolStats != null) {
            logger.info("连接池统计: 命中={}, 未命中={}, 命中率={}%, 创建={}, 归还={}",
                    report.poolStats.hits,
                    report.poolStats.misses,
                    String.format("%.2f", report.poolStats.getHitRate()),
                    report.poolStats.creations,
                    report.poolStats.returns);
        }

        // 数据传输统计
        if (report.dataTransferStats != null) {
            logger.info("数据传输: 接收={}MB, 发送={}MB, 总计={}MB",
                    String.format("%.2f", report.dataTransferStats.bytesReceived / 1024.0 / 1024.0),
                    String.format("%.2f", report.dataTransferStats.bytesSent / 1024.0 / 1024.0),
                    String.format("%.2f", report.dataTransferStats.getTotalBytes() / 1024.0 / 1024.0));
        }

        if (!report.topErrors.isEmpty()) {
            logger.info("主要错误: {}", report.topErrors);
        }

        // 连接质量前5名
        List<ConnectionQualityStats> topConnections = report.connectionQuality.stream()
                .limit(5)
                .collect(Collectors.toList());

        if (!topConnections.isEmpty()) {
            logger.info("连接质量 TOP5:");
            for (ConnectionQualityStats stats : topConnections) {
                logger.info("  {}: 成功率={}%, 平均延迟={}ms",
                        stats.host,
                        String.format("%.2f", stats.successRate),
                        String.format("%.2f", stats.averageResponseTime));
            }
        }
    }

    /**
     * 延迟跟踪器
     */
    private static class LatencyTracker {
        private final List<Long> samples = new ArrayList<>();
        private final AtomicLong totalLatency = new AtomicLong(0);
        private final AtomicLong sampleCount = new AtomicLong(0);

        public LatencyTracker(String name) {
            // name参数用于标识跟踪器类型，但在当前实现中不需要存储
        }

        public synchronized void record(long latencyMs) {
            samples.add(latencyMs);
            totalLatency.addAndGet(latencyMs);
            sampleCount.incrementAndGet();

            // 保持样本数量在合理范围内
            if (samples.size() > 1000) {
                samples.subList(0, 500).clear();
            }
        }

        public synchronized LatencyStats getStats() {
            if (samples.isEmpty()) {
                return new LatencyStats(0, 0, 0, 0, 0);
            }

            List<Long> sortedSamples = new ArrayList<>(samples);
            sortedSamples.sort(Long::compareTo);

            double average = (double) totalLatency.get() / sampleCount.get();
            long p50 = getPercentile(sortedSamples, 0.5);
            long p95 = getPercentile(sortedSamples, 0.95);
            long p99 = getPercentile(sortedSamples, 0.99);
            long max = sortedSamples.get(sortedSamples.size() - 1);

            return new LatencyStats(average, p50, p95, p99, max);
        }

        private long getPercentile(List<Long> sortedSamples, double percentile) {
            if (sortedSamples.isEmpty()) return 0;
            int index = (int) Math.floor(sortedSamples.size() * percentile);
            index = Math.max(0, Math.min(index, sortedSamples.size() - 1));
            return sortedSamples.get(index);
        }
    }

    /**
     * 连接质量跟踪器
     */
    private static class ConnectionQualityTracker {
        private final AtomicLong totalRequests = new AtomicLong(0);
        private final AtomicLong successfulRequests = new AtomicLong(0);
        private final AtomicLong totalResponseTime = new AtomicLong(0);

        public void record(boolean success, long responseTime) {
            totalRequests.incrementAndGet();
            if (success) {
                successfulRequests.incrementAndGet();
            }
            totalResponseTime.addAndGet(responseTime);
        }

        public ConnectionQualityStats getStats(String host) {
            long total = totalRequests.get();
            long successful = successfulRequests.get();
            long totalTime = totalResponseTime.get();

            double successRate = total > 0 ? (double) successful / total * 100.0 : 0.0;
            double averageResponseTime = total > 0 ? (double) totalTime / total : 0.0;

            return new ConnectionQualityStats(host, successRate, averageResponseTime);
        }
    }

    /**
     * 延迟统计
     */
    public static class LatencyStats {
        public final double average;
        public final long p50;
        public final long p95;
        public final long p99;
        public final long max;

        public LatencyStats(double average, long p50, long p95, long p99, long max) {
            this.average = average;
            this.p50 = p50;
            this.p95 = p95;
            this.p99 = p99;
            this.max = max;
        }
    }

    /**
     * 内存统计
     */
    public static class MemoryStats {
        public final long allocated;
        public final long released;

        public MemoryStats(long allocated, long released) {
            this.allocated = allocated;
            this.released = released;
        }

        public long getNetUsage() {
            return allocated - released;
        }
    }

    /**
     * 连接质量统计
     */
    public static class ConnectionQualityStats {
        public final String host;
        public final double successRate;
        public final double averageResponseTime;

        public ConnectionQualityStats(String host, double successRate, double averageResponseTime) {
            this.host = host;
            this.successRate = successRate;
            this.averageResponseTime = averageResponseTime;
        }
    }

    /**
     * 连接池统计
     */
    public static class PoolStats {
        public final long hits;
        public final long misses;
        public final long creations;
        public final long returns;

        public PoolStats(long hits, long misses, long creations, long returns) {
            this.hits = hits;
            this.misses = misses;
            this.creations = creations;
            this.returns = returns;
        }

        public double getHitRate() {
            long total = hits + misses;
            return total > 0 ? (double) hits / total * 100.0 : 0.0;
        }
    }

    /**
     * 数据传输统计
     */
    public static class DataTransferStats {
        public final long bytesReceived;
        public final long bytesSent;

        public DataTransferStats(long bytesReceived, long bytesSent) {
            this.bytesReceived = bytesReceived;
            this.bytesSent = bytesSent;
        }

        public long getTotalBytes() {
            return bytesReceived + bytesSent;
        }
    }

    /**
     * 性能报告
     */
    public static class PerformanceReport {
        public final long totalRequests;
        public final long totalResponses;
        public final double throughput;
        public final LatencyStats requestLatency;
        public final Map<String, Long> topErrors;
        public final List<ConnectionQualityStats> connectionQuality;
        public final MemoryStats memoryStats;
        public final Map<String, Long> protocolStats;
        public final PoolStats poolStats;
        public final DataTransferStats dataTransferStats;

        public PerformanceReport(long totalRequests, long totalResponses, double throughput,
                                 LatencyStats requestLatency,
                                 Map<String, Long> topErrors, List<ConnectionQualityStats> connectionQuality,
                                 MemoryStats memoryStats, Map<String, Long> protocolStats,
                                 PoolStats poolStats, DataTransferStats dataTransferStats) {
            this.totalRequests = totalRequests;
            this.totalResponses = totalResponses;
            this.throughput = throughput;
            this.requestLatency = requestLatency;
            this.topErrors = topErrors;
            this.connectionQuality = connectionQuality;
            this.memoryStats = memoryStats;
            this.protocolStats = protocolStats;
            this.poolStats = poolStats;
            this.dataTransferStats = dataTransferStats;
        }
    }
}