<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="SqlDialectMappings">
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy/server/src/main/resources/META-INF/db_user.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy/server/src/main/resources/META-INF/userdb_data.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy/server/src/main/resources/META-INF/userdb_struct.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy/server/target/classes/META-INF/db_user.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy/server/target/classes/META-INF/userdb_data.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy/server/target/classes/META-INF/userdb_struct.sql" dialect="GenericSQL" />
    <file url="file://$PROJECT_DIR$/netty_multiplex_proxy_alicloud/configs/mysql/init/01-init-databases.sql" dialect="GenericSQL" />
  </component>
</project>