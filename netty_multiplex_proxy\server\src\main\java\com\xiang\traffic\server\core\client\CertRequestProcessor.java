package com.xiang.traffic.server.core.client;

import com.xiang.traffic.AbstractComponent;
import com.xiang.traffic.ComponentException;
import com.xiang.traffic.misc.FSMessageOutboundEncoder;
import com.xiang.traffic.protocol.CertRequestMessage;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.socket.ServerSocketChannel;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.handler.timeout.IdleStateHandler;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:19
 */
class CertRequestProcessor extends AbstractComponent<ClientProcessor> {

    /**
     * 服务端口
     */
    private final int port;

    /**
     * 客户端请求处理器
     */
    private final CertRequestHandler certRequestHandler;

    /**
     * 服务绑定端口后对应的ServerSocketChannel
     */
    private volatile ServerSocketChannel serverSocketChannel;


    CertRequestProcessor(ClientProcessor processor, byte[] certFile) {
        super("CertRequestProcessor", Objects.requireNonNull(processor));
        this.port = parent.getParentComponent().getCertPort();
        this.certRequestHandler = new CertRequestHandler(certFile, parent::doAuth);
    }

    @Override
    protected void initInternal() {
        super.initInternal();
    }

    @Override
    protected void startInternal() {
        final CertRequestHandler certRequestHandler = this.certRequestHandler;
        ServerBootstrap certBoot = new ServerBootstrap();

        EventLoopGroup bossWorker = parent.getParentComponent().getBossWorker();
        EventLoopGroup childWorker = parent.getParentComponent().getChildWorker();
        Class<? extends ServerSocketChannel> channelClass = parent.getParentComponent().getServerSocketChannelClass();

        certBoot.group(bossWorker, childWorker)
                .channel(channelClass)
                .option(ChannelOption.AUTO_CLOSE, true)
                .childHandler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        ChannelPipeline cp = ch.pipeline();
                        int l = CertRequestMessage.END_MARK.length;
                        ByteBuf buf = Unpooled.buffer(l);
                        buf.writeBytes(CertRequestMessage.END_MARK);

                        cp.addLast(new IdleStateHandler(15, 0, 0));
                        cp.addLast(IdleStateEventHandler.INSTANCE);
                        cp.addLast(FSMessageOutboundEncoder.INSTANCE);
                        cp.addLast(new DelimiterBasedFrameDecoder(512 + l, buf));
                        cp.addLast(certRequestHandler);
                    }
                });

        ChannelFuture future = certBoot.bind(port).awaitUninterruptibly();
        if (!future.isSuccess()) {
            log.error("Bind port {} failure", port);
            throw new ComponentException(future.cause());
        }

        this.serverSocketChannel = (ServerSocketChannel) future.channel();
        super.startInternal();
    }

    @Override
    protected void stopInternal() {
        ServerSocketChannel channel = this.serverSocketChannel;
        if (channel != null) {
            channel.close();
        }

        super.stopInternal();
    }


    @ChannelHandler.Sharable
    private static class IdleStateEventHandler extends ChannelInboundHandlerAdapter {
        static final IdleStateEventHandler INSTANCE = new IdleStateEventHandler();

        @Override
        public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
            if (evt instanceof IdleStateEvent) {
                ctx.close();
            } else {
                ctx.fireUserEventTriggered(evt);
            }
        }
    }
}
