package com.xiang.proxy.server.config;

/**
 * 连接池配置类
 * 定义连接池相关的配置参数
 * 现在使用新的 YAML 配置管理器
 */
public class ConnectionPoolConfig {

    private static ProxyServerConfigManager getConfigManager() {
        return ProxyServerConfigManager.getInstance();
    }

    // 是否启用连接池
    public static final boolean ENABLE_CONNECTION_POOL = getConfigManager().isPoolEnabled();

    // 每个主机的最大连接数
    public static final int MAX_CONNECTIONS_PER_HOST = getConfigManager().getPoolMaxConnectionsPerHost();

    // 连接空闲超时时间（毫秒）
    public static final int CONNECTION_IDLE_TIMEOUT = getConfigManager().getPoolIdleTimeoutSeconds() * 1000;

    // 连接建立超时时间（毫秒） - 减少超时时间以提高响应性
    public static final int CONNECTION_TIMEOUT = 5000; // 5秒// 连接池清理间隔（毫秒）

    public static final int POOL_CLEANUP_INTERVAL = getConfigManager().getPoolCleanupIntervalSeconds() * 1000;
    
    // 连接最大存活时间（毫秒）
    public static final int CONNECTION_MAX_LIFETIME = getConfigManager().getPoolMaxLifetimeSeconds() * 1000;
    
    // 连接重用前的验证超时时间（毫秒） - 保持原有默认值
    public static final int CONNECTION_VALIDATION_TIMEOUT = 1000; // 1秒

    // 是否启用严格的连接状态检查 - 保持原有默认值
    public static final boolean ENABLE_STRICT_CONNECTION_CHECK = true;

    // 连接池统计信息输出间隔（毫秒） - 保持原有默认值
    public static final int POOL_STATS_INTERVAL = 60000; // 60秒

    // 域名黑名单缓存时间（毫秒） - 从配置文件获取
    public static final long BLACKLIST_CACHE_TIME = getConfigManager().getBlacklistCacheTimeoutSeconds() * 1000L;

    // 黑名单最大条目数 - 保持原有默认值
    public static final int MAX_BLACKLIST_SIZE = 1000;

    /**
     * 获取配置摘要
     */
    public static String getConfigSummary() {
        return String.format(
            "ConnectionPool Config: enabled=%s, maxPerHost=%d, idleTimeout=%dms, " +
            "cleanupInterval=%dms, maxLifetime=%dms, strictCheck=%s",
            ENABLE_CONNECTION_POOL,
            MAX_CONNECTIONS_PER_HOST,
            CONNECTION_IDLE_TIMEOUT,
            POOL_CLEANUP_INTERVAL,
            CONNECTION_MAX_LIFETIME,
            ENABLE_STRICT_CONNECTION_CHECK
        );
    }
}