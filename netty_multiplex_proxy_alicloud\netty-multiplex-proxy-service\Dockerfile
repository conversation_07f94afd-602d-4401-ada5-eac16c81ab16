# 多阶段构建 Dockerfile for Netty Multiplex Proxy Service
# 第一阶段：构建阶段
FROM maven:3.6.3-openjdk-17-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制Maven配置文件（阿里云镜像源配置）
# 国内才使用，国外不用
#COPY netty-multiplex-proxy-service/maven-settings.xml /root/.m2/settings.xml

# 首先构建并安装proxy-server依赖到本地仓库
COPY ./netty_multiplex_proxy/proxy-server /tmp/proxy-server
WORKDIR /tmp/proxy-server
RUN mvn clean install -DskipTests

WORKDIR /app
# 复制源代码
COPY ./netty_multiplex_proxy_alicloud .

# 下载依赖（利用 Docker 缓存层）
RUN mvn dependency:go-offline -B

# 构建应用
RUN mvn clean package -DskipTests -pl netty-multiplex-proxy-service -am

# 第二阶段：运行阶段
FROM openjdk:17-ea-slim

# 设置维护者信息
LABEL maintainer="proxy-server-team"
LABEL version="1.0.0"
LABEL description="Netty-based Multiplex Proxy Server"

# 安装网络工具用于健康检查
#RUN apt-get update && apt-get install -y net-tools && rm -rf /var/lib/apt/lists/*

# 创建非 root 用户，使用固定的UID/GID以避免权限问题
RUN groupadd -r proxyuser --gid=1000 && useradd -r -g proxyuser --uid=1000 proxyuser

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/logs && mkdir -p /app/config && \
    chown -R proxyuser:proxyuser /app

# 复制构建好的 JAR 文件
COPY --from=builder /app/netty-multiplex-proxy-service/target/netty-multiplex-proxy-service-0.0.1-SNAPSHOT.jar /app/netty-multiplex-proxy-service-0.0.1-SNAPSHOT.jar
RUN chown proxyuser:proxyuser /app/netty-multiplex-proxy-service-0.0.1-SNAPSHOT.jar

RUN chown proxyuser:proxyuser /app/config
# 暴露端口
EXPOSE 8888

# 健康检查
#HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
#    CMD netstat -an | grep :8888 | grep LISTEN || exit 1

# 切换到非 root 用户
USER proxyuser

#如果失败，切换root
#USER root

# 启动命令
# 通过系统属性指定配置目录，确保使用挂载的配置文件
ENTRYPOINT ["java", "-Xms256m", "-Xmx1024m", "-XX:+UseG1GC", "-XX:+UseContainerSupport", "-Dconfig.ext.dir=/app/config/", "-jar", "/app/netty-multiplex-proxy-service-0.0.1-SNAPSHOT.jar"]
