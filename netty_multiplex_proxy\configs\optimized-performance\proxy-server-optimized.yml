c/sysctl.conf
    echo 'net.core.wmem_default = 262144' >> /etc/sysctl.conf
    echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf
    
    # 文件描述符限制
    echo '* soft nofile 65535' >> /etc/security/limits.conf
    echo '* hard nofile 65535' >> /etc/security/limits.conf
    
    # 应用生效
    sysctl -p

# 性能测试配置
benchmark:
  enabled: true
  test_duration_seconds: 60
  concurrent_clients: 100
  requests_per_client: 1000
  target_throughput_mbps: 20  # 目标吞吐量 20MB/s

# 优化策略说明
optimization_notes: |
  1. 连接复用：实现类似高性能server的ActiveConnection机制
  2. 零拷贝：使用Netty的零拷贝特性，减少数据拷贝
  3. 平台优化：自动选择最优的I/O模型（Epoll/KQueue/NIO）
  4. 内存池化：使用PooledByteBufAllocator减少GC压力
  5. 线程优化：智能计算最优线程数，减少上下文切换
  6. 缓冲区优化：根据系统内存动态调整缓冲区大小
  7. 批量处理：批量处理网络I/O操作
  8. 简化架构：移除不必要的抽象层和队列
  
  预期性能提升：
  - 吞吐量：从600K/s提升到20MB/s（约33倍提升）
  - 延迟：降低50-70%
  - 内存使用：降低30-50%
  - CPU使用：降低20-40%