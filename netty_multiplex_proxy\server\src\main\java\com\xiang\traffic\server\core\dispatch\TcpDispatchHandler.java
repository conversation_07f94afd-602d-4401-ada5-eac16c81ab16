package com.xiang.traffic.server.core.dispatch;

import com.xiang.traffic.protocol.ProxyResponseMessage;
import com.xiang.traffic.server.core.ProxyTask;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/6/6 15:20
 */
class TcpDispatchHandler extends ChannelInboundHandlerAdapter {

    private static final Logger log = LoggerFactory.getLogger(TcpDispatchHandler.class);

    private final ProxyTask proxyTask;

    TcpDispatchHandler(ProxyTask task) {
        this.proxyTask = task;
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        if (cause instanceof IOException) {
            log.info("Target remote host {}:{} close, cause IOException", host(), port());
        } else {
            log.warn("DispathcerHandelr occur a exception", cause);
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        log.trace("Connection close by {}:{}", host(), port());
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        ByteBuf buf = proxyTask.getRequestMessage().getMessage();
        ctx.writeAndFlush(buf);
        ctx.fireChannelActive();
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        if (msg instanceof ByteBuf) {
            try {
                channelRead0(ctx, (ByteBuf) msg);
            } finally {
                ReferenceCountUtil.release(msg);
            }
        } else {
            ctx.fireChannelRead(msg);
        }
    }

    protected void channelRead0(ChannelHandlerContext ctx, ByteBuf msg) {
        log.trace("Receive from {}:{} response.", host(), port());

        ProxyResponseMessage prm = new ProxyResponseMessage(proxyTask.getRequestMessage().serialId());
        prm.setState(ProxyResponseMessage.State.SUCCESS);
        prm.setMessage(msg.retain());
        try {
            proxyTask.session().writeAndFlushMessage(prm);
        } catch (IllegalStateException e) {
            log.debug("Remote client connection closed", e);
            msg.release();
            ctx.close();
        }
    }


    private int port() {
        return proxyTask.getRequestMessage().getPort();
    }


    private String host() {
        return proxyTask.getRequestMessage().getHost();
    }
}
