# 代理客户端配置文件

# 地址过滤模式
# ALL_PROXY: 所有连接都通过proxy-server转发
# CHINA_DIRECT: 中国地区IP直连，其他通过proxy-server转发
# ALL_DIRECT: 所有连接都直连
filter:
  mode: ALL_PROXY

# 代理服务器配置
proxy:
  server:
#dev
    host: localhost
#    host: **************
    port: 8888

# 本地端口（向后兼容）
local:
  port: 1081

# 认证配置
auth:
  enable: true
  username: admin
  password: password11
  timeout:
    seconds: 30

# 性能配置
performance:
  # 工作线程数 (0表示自动计算，基于CPU核心数)
  worker-threads: 0
  # 直连工作线程数 (0表示自动计算，基于CPU核心数)
  direct-connection-threads: 0

# 接入器配置 - 支持多个同类型接入器
inbound:
  # SOCKS5 接入器列表
  socks5:
    - name: "socks5-main"
      port: 1081
      enabled: true
      description: "主要SOCKS5代理"
    - name: "socks5-backup"
      port: 1083
      enabled: false
      description: "备用SOCKS5代理"
    - name: "socks5-special"
      port: 1084
      enabled: false
      description: "特殊用途SOCKS5代理"
  
  # HTTP 接入器列表
  http:
    - name: "http-main"
      port: 1082
      enabled: true
      description: "主要HTTP代理"
    - name: "http-backup"
      port: 1085
      enabled: false
      description: "备用HTTP代理"
    - name: "http-special"
      port: 1086
      enabled: false
      description: "特殊用途HTTP代理"

# SSL/TLS配置 - 客户端连接到代理服务器的SSL设置
#执行scripts/gen-ssl-certs.bat 自动生成证书
ssl:
  enable: true  # 是否启用SSL/TLS连接到代理服务器
  trust-all: false  # 是否信任所有证书（仅用于测试，不推荐生产使用）
  trust-store-path: "truststore.p12"  # 信任库路径（空表示使用系统默认）
  trust-store-password: "xiang1"  # 信任库密码
  trust-store-type: "PKCS12"  # 信任库类型
  key-store-path: "client.p12"  # 客户端证书路径（用于双向认证）
  key-store-password: "xiang1"  # 客户端证书密码
  key-store-type: "PKCS12"  # 客户端证书类型
  protocols:  # 支持的SSL/TLS协议版本
    - "TLSv1.2"
    - "TLSv1.3"
  cipher-suites: []  # 密码套件（空数组表示使用默认）
  verify-hostname: false  # 是否验证服务器主机名
  handshake-timeout-seconds: 30  # SSL握手超时时间（秒）

# 在线数据源配置
online-data-sources:
  # 中国IP段数据源
  china-ip-ranges:
    - "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt"
    - "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
# 队列配置文件
queue:
  # 队列容量（数据包数量）
  capacity: 10000
  # 批处理大小
  batch-size: 100
  # 刷新间隔（毫秒）
  flush-interval-ms: 10
  # 重试配置
  retry:
    # 最大重试次数
    max-attempts: 3
    # 重试延迟（毫秒）
    delay-ms: 1000
  # 监控配置
  monitoring:
    # 是否启用队列监控
    enabled: true
    # 监控报告间隔（秒）
    report-interval-seconds: 30
    # 队列使用率警告阈值（百分比）
    warning-threshold: 80
    # 队列使用率错误阈值（百分比）
    error-threshold: 95

# Nacos 服务发现配置
nacos:
  # 是否启用 Nacos 服务发现，false则使用配置的ip和端口
  enabled: false

  # Nacos 服务器地址
  serverAddr: *************:8848
#  serverAddr: ************:8848

  # 命名空间（可选，为空表示 public 命名空间）
  namespace: ""

  # 要发现的服务名称
  serviceName: main-multiplex

  # 服务分组
  groupName: DEFAULT_GROUP

  username: nacos

  password: nacos