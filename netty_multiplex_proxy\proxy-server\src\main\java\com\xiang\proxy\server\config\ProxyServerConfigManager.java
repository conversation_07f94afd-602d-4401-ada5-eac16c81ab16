package com.xiang.proxy.server.config;

import com.xiang.proxy.server.config.binder.ConfigurationBinder;
import com.xiang.proxy.server.config.properties.ProxyServerProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.yaml.snakeyaml.Yaml;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Map;
import java.util.Properties;

/**
 * 代理服务器配置管理器
 * 使用类似 Spring Boot 的配置绑定方式
 * 支持外部配置文件加载
 */
public class ProxyServerConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(ProxyServerConfigManager.class);

    // 默认配置文件路径
    private static final String DEFAULT_CONFIG_FILE = "configs/development/proxy-server.yml";

    // 配置文件路径的环境变量名
    private static final String CONFIG_FILE_ENV = "PROXY_SERVER_CONFIG";

    // 配置文件路径的系统属性名
    private static final String CONFIG_FILE_PROPERTY = "proxy.server.config";

    private static ProxyServerConfigManager instance;
    private ProxyServerProperties properties;
    private static String configFilePath;
    
    private ProxyServerConfigManager() {
        loadConfiguration();
    }

    public static synchronized ProxyServerConfigManager getInstance() {
        if (instance == null) {
            instance = new ProxyServerConfigManager();
        }
        return instance;
    }

    /**
     * 设置配置文件路径（用于命令行参数指定）
     */
    public static void setConfigFilePath(String path) {
        configFilePath = path;
    }
    
    /**
     * 加载配置
     */
    private void loadConfiguration() {
        // 确定配置文件路径
        String configPath = determineConfigFilePath();

        // 尝试加载 YAML 配置文件
        if (loadYamlConfiguration(configPath)) {
            return;
        }

        // 如果 YAML 配置文件不存在，使用默认配置
        logger.info("未找到 YAML 配置文件，使用默认配置");
        properties = new ProxyServerProperties();
        initializeDefaultConfiguration();
    }

    /**
     * 确定配置文件路径
     * 优先级：命令行参数 > 系统属性 > 环境变量 > properties文件 > 默认路径
     */
    private String determineConfigFilePath() {
        // 1. 命令行参数指定的路径（通过 setConfigFilePath 设置）
        if (configFilePath != null && !configFilePath.trim().isEmpty()) {
            logger.info("使用命令行参数指定的配置文件: {}", configFilePath);
            return configFilePath;
        }

        // 2. 系统属性指定的路径
        String systemProperty = System.getProperty(CONFIG_FILE_PROPERTY);
        if (systemProperty != null && !systemProperty.trim().isEmpty()) {
            logger.info("使用系统属性指定的配置文件: {}", systemProperty);
            return systemProperty;
        }

        // 3. 环境变量指定的路径
        String envVariable = System.getenv(CONFIG_FILE_ENV);
        if (envVariable != null && !envVariable.trim().isEmpty()) {
            logger.info("使用环境变量指定的配置文件: {}", envVariable);
            return envVariable;
        }

        // 4. 从properties文件读取配置路径
        String propertiesPath = loadConfigPathFromProperties();
        if (propertiesPath != null && !propertiesPath.trim().isEmpty()) {
            logger.info("使用properties文件指定的配置文件: {}", propertiesPath);
            return propertiesPath;
        }

        // 5. 默认路径
        logger.info("使用默认配置文件路径: {}", DEFAULT_CONFIG_FILE);
        return DEFAULT_CONFIG_FILE;
    }

    /**
     * 从properties文件中加载配置文件路径
     */
    private String loadConfigPathFromProperties() {
        Properties props = new Properties();

        // 尝试加载application.properties
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("app.properties")) {
            if (is != null) {
                props.load(is);

                // 优先检查配置目录
                String configDir = props.getProperty("config.ext.dir");
                if (configDir != null && !configDir.trim().isEmpty()) {
                    String configPath = findConfigFileInDirectory(configDir.trim());
                    if (configPath != null) {
                        return configPath;
                    }
                }

                // 获取配置文件路径
                String configPath = props.getProperty("config.file.path");
                if (configPath != null && !configPath.trim().isEmpty()) {
                    return configPath.trim();
                }
            }
        } catch (Exception e) {
            logger.warn("读取application.properties文件失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 在指定目录中查找配置文件
     * 按优先级查找：proxy-server.yml > proxy-server-dev-*.yml > 其他.yml文件
     */
    private String findConfigFileInDirectory(String configDir) {
        File dir = new File(configDir);
        if (!dir.exists() || !dir.isDirectory()) {
            logger.warn("配置目录不存在或不是目录: {}", configDir);
            return null;
        }

        File[] files = dir.listFiles((file, name) -> name.endsWith(".yml") || name.endsWith(".yaml"));
        if (files == null || files.length == 0) {
            logger.warn("配置目录中未找到YAML配置文件: {}", configDir);
            return null;
        }

        // 按优先级排序查找配置文件
        String[] priorities = {
            "proxy-server.yml",
            "proxy-server.yaml"
        };

        // 1. 首先查找主配置文件
        for (String priority : priorities) {
            File configFile = new File(dir, priority);
            if (configFile.exists() && configFile.isFile()) {
                logger.info("在配置目录中找到主配置文件: {}", configFile.getAbsolutePath());
                return configFile.getAbsolutePath();
            }
        }

        // 2. 查找开发环境配置文件
        for (File file : files) {
            String fileName = file.getName();
            if (fileName.startsWith("proxy-server-dev-") &&
                (fileName.endsWith(".yml") || fileName.endsWith(".yaml"))) {
                logger.info("在配置目录中找到开发环境配置文件: {}", file.getAbsolutePath());
                return file.getAbsolutePath();
            }
        }

        // 3. 查找任何包含proxy-server的配置文件
        for (File file : files) {
            String fileName = file.getName();
            if (fileName.contains("proxy-server") &&
                (fileName.endsWith(".yml") || fileName.endsWith(".yaml"))) {
                logger.info("在配置目录中找到配置文件: {}", file.getAbsolutePath());
                return file.getAbsolutePath();
            }
        }

        // 4. 如果都没找到，使用第一个YAML文件
        File firstYamlFile = files[0];
        logger.info("在配置目录中使用第一个YAML文件: {}", firstYamlFile.getAbsolutePath());
        return firstYamlFile.getAbsolutePath();
    }
    
    /**
     * 加载 YAML 配置文件
     */
    private boolean loadYamlConfiguration(String configPath) {
        // 首先尝试作为外部文件加载
        if (loadExternalYamlFile(configPath)) {
            return true;
        }

        // 如果外部文件不存在，尝试从classpath加载
        return loadClasspathYamlFile(configPath);
    }

    /**
     * 从外部文件系统加载YAML配置文件
     */
    private boolean loadExternalYamlFile(String configPath) {
        File configFile = new File(configPath);
        if (!configFile.exists() || !configFile.isFile()) {
            return false;
        }

        try (InputStream is = new FileInputStream(configFile)) {
            Yaml yaml = new Yaml();
            Map<String, Object> config = yaml.load(is);

            // 使用配置绑定器绑定配置
            properties = ConfigurationBinder.bind(config, ProxyServerProperties.class);

            logger.info("成功加载外部 YAML 配置文件: {}", configFile.getAbsolutePath());
            logConfiguration();
            return true;
        } catch (Exception e) {
            logger.warn("加载外部 YAML 配置文件失败: {}", configFile.getAbsolutePath(), e);
            return false;
        }
    }

    /**
     * 从classpath加载YAML配置文件
     */
    private boolean loadClasspathYamlFile(String configPath) {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(configPath)) {
            if (is != null) {
                Yaml yaml = new Yaml();
                Map<String, Object> config = yaml.load(is);

                // 使用配置绑定器绑定配置
                properties = ConfigurationBinder.bind(config, ProxyServerProperties.class);

                logger.info("成功加载 classpath YAML 配置文件: {}", configPath);
                logConfiguration();
                return true;
            }
        } catch (Exception e) {
            logger.warn("加载 classpath YAML 配置文件失败: {}", configPath, e);
        }
        return false;
    }
    
    /**
     * 初始化默认配置
     */
    private void initializeDefaultConfiguration() {
        logger.info("使用默认配置初始化");
        logConfiguration();
    }
    
    /**
     * 记录配置信息
     */
    private void logConfiguration() {
        logger.info("配置加载完成 - 服务器端口: {}, 认证: {}, 连接池: {}, 性能监控: {}, 黑名单: {}",
                   properties.getServer().getPort(),
                   properties.getAuth().isEnable() ? "启用" : "禁用",
                   properties.getPool().isEnable() ? "启用" : "禁用",
                   properties.getMetrics().isEnable() ? "启用" : "禁用",
                   properties.getBlacklist().isEnable() ? "启用" : "禁用");

        if (properties.getAuth().isEnable()) {
            logger.info("认证配置 - 用户名: {}, 超时时间: {}秒", 
                       properties.getAuth().getUsername(), 
                       properties.getAuth().getTimeout().getSeconds());
        }
        
        if (properties.getPool().isEnable()) {
            logger.info("连接池配置 - 最大连接数/主机: {}, 空闲超时: {}秒, 清理间隔: {}秒",
                       properties.getPool().getMaxConnections().getPerHost(),
                       properties.getPool().getIdleTimeout().getSeconds(),
                       properties.getPool().getCleanupInterval().getSeconds());
        }
        
        if (properties.getMetrics().isEnable()) {
            logger.info("性能监控配置 - 报告间隔: {}秒",
                       properties.getMetrics().getReport().getInterval().getSeconds());
        }
        
        if (properties.getBlacklist().isEnable()) {
            logger.info("黑名单配置 - 失败阈值: {}, 缓存超时: {}秒",
                       properties.getBlacklist().getFailure().getThreshold(),
                       properties.getBlacklist().getCache().getTimeout().getSeconds());
        }
    }
    
    // Getter 方法
    public ProxyServerProperties getProperties() {
        return properties;
    }
    
    public int getServerPort() {
        return properties.getServer().getPort();
    }
    
    public boolean isAuthEnabled() {
        return properties.getAuth().isEnable();
    }
    
    public String getAuthUsername() {
        return properties.getAuth().getUsername();
    }
    
    public String getAuthPassword() {
        return properties.getAuth().getPassword();
    }
    
    public int getAuthTimeoutSeconds() {
        return properties.getAuth().getTimeout().getSeconds();
    }
    
    public boolean isPoolEnabled() {
        return properties.getPool().isEnable();
    }
    
    public int getPoolMaxConnectionsPerHost() {
        return properties.getPool().getMaxConnections().getPerHost();
    }
    
    public int getPoolIdleTimeoutSeconds() {
        return properties.getPool().getIdleTimeout().getSeconds();
    }
    
    public int getPoolCleanupIntervalSeconds() {
        return properties.getPool().getCleanupInterval().getSeconds();
    }
    
    public int getPoolMaxLifetimeSeconds() {
        return properties.getPool().getMaxLifetime().getSeconds();
    }
    
    public boolean isMetricsEnabled() {
        return properties.getMetrics().isEnable();
    }
    
    public int getMetricsReportIntervalSeconds() {
        return properties.getMetrics().getReport().getInterval().getSeconds();
    }
    
    public boolean isBlacklistEnabled() {
        return properties.getBlacklist().isEnable();
    }
    
    public int getBlacklistFailureThreshold() {
        return properties.getBlacklist().getFailure().getThreshold();
    }
    
    public int getBlacklistCacheTimeoutSeconds() {
        return properties.getBlacklist().getCache().getTimeout().getSeconds();
    }
    
    /**
     * 验证用户名密码
     */
    public boolean validateCredentials(String username, String password) {
        if (!isAuthEnabled()) {
            return true; // 未启用认证时，总是返回成功
        }

        if (username == null || password == null) {
            return false;
        }

        return getAuthUsername().equals(username) && getAuthPassword().equals(password);
    }

    /**
     * 获取地理位置过滤配置
     */
    public ProxyServerProperties.GeoLocationFilterProperties getGeoLocationFilterProperties() {
        return properties.getGeoLocationFilter();
    }

    /**
     * 检查地理位置过滤是否启用
     */
    public boolean isGeoLocationFilterEnabled() {
        return properties.getGeoLocationFilter().isEnable();
    }

    /**
     * 检查是否阻止海外可疑网站
     */
    public boolean isBlockOverseasSuspicious() {
        return properties.getGeoLocationFilter().isBlockOverseasSuspicious();
    }

    /**
     * 检查域名过滤是否启用
     */
    public boolean isDomainFilterEnabled() {
        return properties.getGeoLocationFilter().isEnableDomainFilter();
    }

    /**
     * 检查关键词过滤是否启用
     */
    public boolean isKeywordFilterEnabled() {
        return properties.getGeoLocationFilter().isEnableKeywordFilter();
    }

    /**
     * 检查白名单是否启用
     */
    public boolean isWhitelistEnabled() {
        return properties.getGeoLocationFilter().isEnableWhitelist();
    }

    /**
     * 获取DNS缓存超时时间（分钟）
     */
    public int getDnsCacheTimeoutMinutes() {
        return properties.getGeoLocationFilter().getDnsCacheTimeoutMinutes();
    }

    /**
     * 获取IP缓存超时时间（分钟）
     */
    public int getIpCacheTimeoutMinutes() {
        return properties.getGeoLocationFilter().getIpCacheTimeoutMinutes();
    }

    /**
     * 获取最大缓存大小
     */
    public int getMaxCacheSize() {
        return properties.getGeoLocationFilter().getMaxCacheSize();
    }

    /**
     * 检查是否自动更新IP段数据
     */
    public boolean isAutoUpdateIPRanges() {
        return properties.getGeoLocationFilter().isAutoUpdateIPRanges();
    }

    /**
     * 获取IP段数据更新间隔（小时）
     */
    public int getUpdateIntervalHours() {
        return properties.getGeoLocationFilter().getUpdateIntervalHours();
    }

    /**
     * 获取在线数据源配置
     */
    public ProxyServerProperties.OnlineDataSourcesProperties getOnlineDataSources() {
        return properties.getGeoLocationFilter().getOnlineDataSources();
    }

    /**
     * 获取恶意域名数据源URL列表
     */
    public java.util.List<String> getMaliciousDomainsUrls() {
        return properties.getGeoLocationFilter().getOnlineDataSources().getMaliciousDomains();
    }

    /**
     * 获取恶意关键词数据源URL列表
     */
    public java.util.List<String> getMaliciousKeywordsUrls() {
        return properties.getGeoLocationFilter().getOnlineDataSources().getMaliciousKeywords();
    }

    /**
     * 获取中国IP段数据源URL列表
     */
    public java.util.List<String> getChinaIpRangesUrls() {
        return properties.getGeoLocationFilter().getOnlineDataSources().getChinaIpRanges();
    }
}
