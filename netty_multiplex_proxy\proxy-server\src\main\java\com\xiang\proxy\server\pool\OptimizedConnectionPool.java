package com.xiang.proxy.server.pool;

import com.xiang.proxy.server.config.ConnectionPoolConfig;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.StampedLock;

/**
 * @TODO 没使用
 * 优化的连接池实现
 * 使用分段锁和无锁数据结构提升并发性能
 * 支持连接复用、过期清理、性能统计等功能
 */
public class OptimizedConnectionPool {
    private static final Logger logger = LoggerFactory.getLogger(OptimizedConnectionPool.class);
    
    // 连接池分段，减少锁竞争
    private static final int SEGMENT_COUNT = 16;
    private final ConnectionSegment[] segments = new ConnectionSegment[SEGMENT_COUNT];
    
    // 统计信息
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);
    
    public OptimizedConnectionPool() {
        for (int i = 0; i < SEGMENT_COUNT; i++) {
            segments[i] = new ConnectionSegment();
        }
        logger.info("优化连接池已初始化，分段数: {}", SEGMENT_COUNT);
    }
    
    /**
     * 获取连接 - 支持连接复用
     */
    public Channel getConnection(String hostKey) {
        if (!ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
            return null;
        }
        
        if (hostKey == null || hostKey.trim().isEmpty()) {
            return null;
        }
        
        ConnectionSegment segment = getSegment(hostKey);
        Channel connection = segment.getConnection(hostKey);
        
        if (connection != null) {
            hitCount.incrementAndGet();
            activeConnections.incrementAndGet();
            logger.debug("连接池命中: {}, 连接ID: {}", hostKey, connection.id());
        } else {
            missCount.incrementAndGet();
            logger.debug("连接池未命中: {}", hostKey);
        }
        
        return connection;
    }
    
    /**
     * 归还连接 - 实现连接复用
     */
    public void returnConnection(String hostKey, Channel channel) {
        if (!ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
            safeCloseChannel(channel);
            return;
        }
        
        if (hostKey == null || channel == null) {
            safeCloseChannel(channel);
            return;
        }
        
        ConnectionSegment segment = getSegment(hostKey);
        if (segment.returnConnection(hostKey, channel)) {
            activeConnections.decrementAndGet();
            logger.debug("连接已归还到连接池: {}, 连接ID: {}", hostKey, channel.id());
        } else {
            logger.debug("连接归还失败，关闭连接: {}, 连接ID: {}", hostKey, channel.id());
            safeCloseChannel(channel);
        }
    }
    
    /**
     * 获取对应的连接段
     */
    private ConnectionSegment getSegment(String hostKey) {
        int hash = hostKey.hashCode();
        int segmentIndex = (hash & Integer.MAX_VALUE) % SEGMENT_COUNT;
        return segments[segmentIndex];
    }
    
    /**
     * 安全关闭连接
     */
    private void safeCloseChannel(Channel channel) {
        if (channel != null && channel.isActive()) {
            try {
                channel.close();
                logger.debug("连接已关闭: {}", channel.id());
            } catch (Exception e) {
                logger.warn("关闭连接时发生异常: {}", channel.id(), e);
            }
        }
    }
    
    /**
     * 获取连接池统计信息
     */
    public PoolStats getStats() {
        long totalConns = 0;
        long pooledConns = 0;
        
        for (ConnectionSegment segment : segments) {
            PoolStats segmentStats = segment.getStats();
            totalConns += segmentStats.totalConnections;
            pooledConns += segmentStats.pooledConnections;
        }
        
        return new PoolStats(
            totalConns,
            pooledConns,
            activeConnections.get(),
            hitCount.get(),
            missCount.get()
        );
    }
    
    /**
     * 清理过期连接
     */
    public void cleanupExpiredConnections() {
        int totalCleaned = 0;
        for (ConnectionSegment segment : segments) {
            totalCleaned += segment.cleanupExpiredConnections();
        }
        
        if (totalCleaned > 0) {
            logger.info("清理了 {} 个过期连接", totalCleaned);
        }
    }
    
    /**
     * 关闭连接池
     */
    public void shutdown() {
        for (ConnectionSegment segment : segments) {
            segment.shutdown();
        }
        logger.info("优化连接池已关闭");
    }
    
    /**
     * 连接段实现 - 核心的分段锁逻辑
     */
    private static class ConnectionSegment {
        private final ConcurrentHashMap<String, ConcurrentLinkedQueue<PooledConnection>> connections = new ConcurrentHashMap<>();
        private final StampedLock lock = new StampedLock();
        private final AtomicInteger totalConnections = new AtomicInteger(0);
        
        /**
         * 获取连接 - 支持连接复用
         */
        public Channel getConnection(String hostKey) {
            ConcurrentLinkedQueue<PooledConnection> queue = connections.get(hostKey);
            if (queue == null || queue.isEmpty()) {
                return null;
            }
            
            // 尝试从队列中获取可用连接
            PooledConnection pooledConn;
            while ((pooledConn = queue.poll()) != null) {
                Channel channel = pooledConn.getChannel();
                
                // 检查连接是否仍然有效且可复用
                if (isConnectionValid(pooledConn, channel)) {
                    // 更新最后使用时间，准备复用
                    pooledConn.updateLastUsedTime();
                    return channel;
                } else {
                    // 连接无效，关闭并继续寻找
                    safeCloseChannel(channel);
                    totalConnections.decrementAndGet();
                }
            }
            
            return null;
        }
        
        /**
         * 归还连接 - 实现连接复用
         */
        public boolean returnConnection(String hostKey, Channel channel) {
            if (!isChannelUsable(channel)) {
                return false;
            }
            
            // 使用乐观读锁检查连接数限制
            long stamp = lock.tryOptimisticRead();
            ConcurrentLinkedQueue<PooledConnection> queue = connections.get(hostKey);
            
            if (!lock.validate(stamp)) {
                // 乐观读失败，使用读锁
                stamp = lock.readLock();
                try {
                    queue = connections.get(hostKey);
                } finally {
                    lock.unlockRead(stamp);
                }
            }
            
            if (queue == null) {
                // 需要创建新队列，使用写锁
                stamp = lock.writeLock();
                try {
                    queue = connections.computeIfAbsent(hostKey, k -> new ConcurrentLinkedQueue<>());
                } finally {
                    lock.unlockWrite(stamp);
                }
            }
            
            // 检查连接数限制
            if (queue.size() >= getMaxConnectionsPerHost()) {
                return false;
            }
            
            // 添加连接到队列，准备复用
            PooledConnection pooledConn = new PooledConnection(channel);
            queue.offer(pooledConn);
            totalConnections.incrementAndGet();
            
            return true;
        }
        
        /**
         * 检查连接是否有效且可复用
         */
        private boolean isConnectionValid(PooledConnection pooledConn, Channel channel) {
            if (!isChannelUsable(channel)) {
                return false;
            }
            
            // 检查连接是否超时
            long currentTime = System.currentTimeMillis();
            long idleTime = currentTime - pooledConn.getLastUsedTime();
            long totalLifetime = currentTime - pooledConn.getCreationTime();
            
            // 检查空闲超时
            if (idleTime > getConnectionIdleTimeout()) {
                return false;
            }
            
            // 检查连接存活时间
            if (totalLifetime > ConnectionPoolConfig.CONNECTION_MAX_LIFETIME) {
                return false;
            }
            
            return true;
        }
        
        /**
         * 检查Channel是否可用于复用
         */
        private boolean isChannelUsable(Channel channel) {
            return channel != null && channel.isActive() && channel.isWritable() && channel.isOpen();
        }
        
        /**
         * 清理过期连接
         */
        public int cleanupExpiredConnections() {
            long currentTime = System.currentTimeMillis();
            int cleanedCount = 0;
            
            for (ConcurrentLinkedQueue<PooledConnection> queue : connections.values()) {
                cleanedCount += cleanupQueue(queue, currentTime);
            }
            
            return cleanedCount;
        }
        
        /**
         * 清理队列中的过期连接
         */
        private int cleanupQueue(ConcurrentLinkedQueue<PooledConnection> queue, long currentTime) {
            int cleanedCount = 0;
            PooledConnection conn;
            
            while ((conn = queue.peek()) != null) {
                long idleTime = currentTime - conn.getLastUsedTime();
                long totalLifetime = currentTime - conn.getCreationTime();
                
                // 检查空闲超时或存活时间超时
                if (idleTime > getConnectionIdleTimeout() || 
                    totalLifetime > ConnectionPoolConfig.CONNECTION_MAX_LIFETIME ||
                    !isChannelUsable(conn.getChannel())) {
                    if (queue.remove(conn)) {
                        safeCloseChannel(conn.getChannel());
                        totalConnections.decrementAndGet();
                        cleanedCount++;
                    }
                } else {
                    // 队列是按时间顺序的，如果当前连接未过期，后面的也不会过期
                    break;
                }
            }
            
            return cleanedCount;
        }
        
        /**
         * 获取统计信息
         */
        public PoolStats getStats() {
            int pooledConnections = connections.values().stream()
                .mapToInt(ConcurrentLinkedQueue::size)
                .sum();
                
            return new PoolStats(
                totalConnections.get(),
                pooledConnections,
                0, 0, 0 // 命中统计在外层维护
            );
        }
        
        /**
         * 关闭段
         */
        public void shutdown() {
            for (ConcurrentLinkedQueue<PooledConnection> queue : connections.values()) {
                PooledConnection conn;
                while ((conn = queue.poll()) != null) {
                    safeCloseChannel(conn.getChannel());
                }
            }
            connections.clear();
        }
        
        private void safeCloseChannel(Channel channel) {
            if (channel != null && channel.isActive()) {
                try {
                    channel.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
        }
        
        private int getMaxConnectionsPerHost() {
            return ConnectionPoolConfig.MAX_CONNECTIONS_PER_HOST;
        }
        
        private long getConnectionIdleTimeout() {
            return ConnectionPoolConfig.CONNECTION_IDLE_TIMEOUT;
        }
    }

    /**
     * 池化连接包装类
     */
    private static class PooledConnection {
        private final Channel channel;
        private final long creationTime;
        private volatile long lastUsedTime;

        public PooledConnection(Channel channel) {
            this.channel = channel;
            this.creationTime = System.currentTimeMillis();
            this.lastUsedTime = this.creationTime;
        }

        public Channel getChannel() {
            return channel;
        }

        public long getCreationTime() {
            return creationTime;
        }

        public long getLastUsedTime() {
            return lastUsedTime;
        }

        public void updateLastUsedTime() {
            this.lastUsedTime = System.currentTimeMillis();
        }

        public long getIdleTime() {
            return System.currentTimeMillis() - lastUsedTime;
        }

        public boolean isExpired(long timeoutMs) {
            return getIdleTime() > timeoutMs;
        }
        
        /**
         * 检查连接是否存活时间超时
         *
         * @return 如果超时返回true
         */
        public boolean isLifetimeTimeout() {
            long currentTime = System.currentTimeMillis();
            long totalLifetime = currentTime - creationTime;
            return totalLifetime > ConnectionPoolConfig.CONNECTION_MAX_LIFETIME;
        }
        
        /**
         * 获取连接存活时间（毫秒）
         *
         * @return 连接存活时间
         */
        public long getLifetime() {
            return System.currentTimeMillis() - creationTime;
        }

        @Override
        public String toString() {
            return String.format("PooledConnection{channel=%s, idleTime=%dms}",
                               channel, getIdleTime());
        }
    }

    /**
     * 连接池统计信息
     */
    public static class PoolStats {
        public final long totalConnections;
        public final long pooledConnections;
        public final long activeConnections;
        public final long hitCount;
        public final long missCount;

        public PoolStats(long totalConnections, long pooledConnections, long activeConnections,
                        long hitCount, long missCount) {
            this.totalConnections = totalConnections;
            this.pooledConnections = pooledConnections;
            this.activeConnections = activeConnections;
            this.hitCount = hitCount;
            this.missCount = missCount;
        }

        public double getHitRate() {
            long total = hitCount + missCount;
            return total > 0 ? (double) hitCount / total * 100.0 : 0.0;
        }

        @Override
        public String toString() {
            return String.format("PoolStats{total=%d, pooled=%d, active=%d, hitRate=%.2f%%, hits=%d, misses=%d}",
                               totalConnections, pooledConnections, activeConnections,
                               getHitRate(), hitCount, missCount);
        }
    }
}
