package com.xiang.proxy.server;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * Spring Boot 启动类
 * 整合了ProxyServerV2的生命周期管理
 */
@SpringBootApplication
public class Bootstrap {
    
    private static final Logger logger = LoggerFactory.getLogger(Bootstrap.class);
    
    public static void main(String[] args) {
        logger.info("启动Spring Boot应用，ProxyServerV2将自动启动...");
        
        try {
            ConfigurableApplicationContext context = SpringApplication.run(Bootstrap.class, args);
            
            logger.info("Spring Boot应用启动完成");
            logger.info("ProxyServerV2管理接口: http://localhost:8080/api/proxy-server/status");
            logger.info("健康检查接口: http://localhost:8080/actuator/health");
            
            // 注册优雅关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("收到关闭信号，正在优雅关闭Spring Boot应用...");
                context.close();
            }));
            
        } catch (Exception e) {
            logger.error("启动Spring Boot应用失败", e);
            System.exit(1);
        }
    }
}
