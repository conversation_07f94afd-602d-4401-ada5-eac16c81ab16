# 组件化架构使用示例

## 1. 基本配置示例

### 1.1 创建和配置组件

```java
// 1. 创建路由器
Router router = new DefaultRouter();

// 2. 创建代理处理器
ProxyProcessor proxyProcessor = new ProxyProcessor(router);

// 3. 创建和注册Outbound处理器
OutboundConfig directConfig = OutboundConfig.defaultConfig();
DirectOutboundHandler directOutbound = new DirectOutboundHandler("direct", directConfig);
proxyProcessor.registerOutboundHandler(directOutbound);

// 4. 创建和注册Inbound处理器
MultiplexInboundHandler multiplexInbound = new MultiplexInboundHandler(proxyProcessor);
proxyProcessor.registerInboundHandler(multiplexInbound);
```

### 1.2 配置路由规则

```java
// 直连路由规则
RouteRule directRule = new RouteRule("direct-rule", "直连路由", 100, "direct");
directRule.addMatcher(new RouteMatcher(RouteMatcher.Type.HOST, RouteMatcher.Operator.ENDS_WITH, ".local"));
router.addRoute(directRule);

// 默认路由规则（优先级最低）
RouteRule defaultRule = new RouteRule("default-rule", "默认路由", 999, "direct");
// 没有匹配条件，匹配所有请求
router.addRoute(defaultRule);
```

## 2. 高级路由配置

### 2.1 基于主机的路由

```java
// 内网直连
RouteRule internalRule = new RouteRule("internal-rule", "内网直连", 10, "direct");
internalRule.addMatcher(new RouteMatcher(RouteMatcher.Type.HOST, RouteMatcher.Operator.REGEX, 
    "^(192\\.168\\.|10\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.).*"));
router.addRoute(internalRule);

// 特定域名路由
RouteRule domainRule = new RouteRule("domain-rule", "特定域名", 20, "proxy-outbound");
domainRule.addMatcher(new RouteMatcher(RouteMatcher.Type.HOST, RouteMatcher.Operator.CONTAINS, "example.com"));
router.addRoute(domainRule);
```

### 2.2 基于端口的路由

```java
// HTTP/HTTPS端口路由
RouteRule httpRule = new RouteRule("http-rule", "HTTP路由", 30, "http-outbound");
httpRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PORT, RouteMatcher.Operator.IN, "80,443,8080,8443"));
router.addRoute(httpRule);

// 数据库端口路由
RouteRule dbRule = new RouteRule("db-rule", "数据库路由", 40, "db-outbound");
dbRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PORT, RouteMatcher.Operator.RANGE, "3306-3310"));
router.addRoute(dbRule);
```

### 2.3 基于协议的路由

```java
// UDP路由
RouteRule udpRule = new RouteRule("udp-rule", "UDP路由", 50, "udp-outbound");
udpRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PROTOCOL, RouteMatcher.Operator.EQUALS, "UDP"));
router.addRoute(udpRule);
```

### 2.4 复合条件路由

```java
// 复合条件：特定客户端的特定主机
RouteRule complexRule = new RouteRule("complex-rule", "复合条件路由", 15, "special-outbound");
complexRule.addMatcher(new RouteMatcher(RouteMatcher.Type.CLIENT_IP, RouteMatcher.Operator.EQUALS, "*************"));
complexRule.addMatcher(new RouteMatcher(RouteMatcher.Type.HOST, RouteMatcher.Operator.ENDS_WITH, ".internal"));
router.addRoute(complexRule);
```

## 3. 多种Outbound配置

### 3.1 直连Outbound

```java
OutboundConfig directConfig = new OutboundConfig();
directConfig.setConnectTimeout(5000);
directConfig.setReadTimeout(30000);
DirectOutboundHandler directOutbound = new DirectOutboundHandler("direct", directConfig);
proxyProcessor.registerOutboundHandler(directOutbound);
```

### 3.2 代理链Outbound（示例接口）

```java
// 这是一个示例，实际需要实现ProxyChainOutboundHandler
OutboundConfig proxyConfig = new OutboundConfig();
proxyConfig.setProperty("proxy.host", "upstream.proxy.com");
proxyConfig.setProperty("proxy.port", 8080);
proxyConfig.setProperty("proxy.type", "http");

ProxyChainOutboundHandler proxyOutbound = new ProxyChainOutboundHandler("proxy-chain", proxyConfig);
proxyProcessor.registerOutboundHandler(proxyOutbound);
```

### 3.3 负载均衡Outbound（示例接口）

```java
// 这是一个示例，实际需要实现LoadBalancerOutboundHandler
OutboundConfig lbConfig = new OutboundConfig();
lbConfig.setProperty("backends", Arrays.asList("server1:8080", "server2:8080", "server3:8080"));
lbConfig.setProperty("strategy", "round_robin");

LoadBalancerOutboundHandler lbOutbound = new LoadBalancerOutboundHandler("load-balancer", lbConfig);
proxyProcessor.registerOutboundHandler(lbOutbound);
```

## 4. 处理请求示例

### 4.1 处理单个请求

```java
// 创建代理请求
ProxyRequest request = ProxyRequest.builder()
    .protocol(ProxyRequest.Protocol.TCP)
    .target("example.com", 80)
    .clientChannel(clientChannel)
    .sessionId(1)
    .clientId("client-001")
    .build();

// 处理请求
proxyProcessor.processRequest(request)
    .whenComplete((response, throwable) -> {
        if (throwable != null) {
            logger.error("请求处理失败", throwable);
        } else if (response.isSuccess()) {
            logger.info("请求处理成功: {}", response.getConnection().getConnectionId());
        } else {
            logger.warn("请求处理失败: {}", response.getMessage());
        }
    });
```

### 4.2 发送数据

```java
// 假设已经有了连接
OutboundConnection connection = response.getConnection();
ByteBuf data = Unpooled.copiedBuffer("GET / HTTP/1.1\r\nHost: example.com\r\n\r\n", StandardCharsets.UTF_8);

ProxyRequest dataRequest = ProxyRequest.builder()
    .protocol(ProxyRequest.Protocol.TCP)
    .target("example.com", 80)
    .data(data)
    .clientChannel(clientChannel)
    .sessionId(1)
    .clientId("client-001")
    .build();

proxyProcessor.sendDataToOutbound(dataRequest, connection)
    .whenComplete((result, throwable) -> {
        if (throwable != null) {
            logger.error("数据发送失败", throwable);
        } else {
            logger.info("数据发送成功");
        }
    });
```

## 5. 监控和统计

### 5.1 路由统计

```java
RouteStatistics routeStats = router.getStatistics();
logger.info("路由统计: {}", routeStats);
```

### 5.2 Outbound统计

```java
OutboundHandler outbound = proxyProcessor.getOutboundHandler("direct");
OutboundStatistics outboundStats = outbound.getStatistics();
logger.info("Outbound统计: {}", outboundStats);
```

### 5.3 健康检查

```java
outbound.healthCheck()
    .whenComplete((status, throwable) -> {
        if (throwable != null) {
            logger.error("健康检查失败", throwable);
        } else {
            logger.info("Outbound健康状态: {}", status);
        }
    });
```

## 6. 动态配置更新

### 6.1 动态添加路由规则

```java
// 运行时添加新的路由规则
RouteRule newRule = new RouteRule("new-rule", "新路由规则", 25, "new-outbound");
newRule.addMatcher(new RouteMatcher(RouteMatcher.Type.HOST, RouteMatcher.Operator.STARTS_WITH, "api."));

// 验证规则
ValidationResult validation = router.validateRule(newRule);
if (validation.isValid()) {
    router.addRoute(newRule);
    logger.info("新路由规则添加成功");
} else {
    logger.error("路由规则验证失败: {}", validation.getMessage());
}
```

### 6.2 动态启用/禁用路由规则

```java
// 禁用某个路由规则
router.setRouteEnabled("old-rule", false);

// 启用某个路由规则
router.setRouteEnabled("new-rule", true);
```

### 6.3 动态注册新的Outbound

```java
// 运行时添加新的Outbound处理器
OutboundConfig newConfig = OutboundConfig.fastConfig();
DirectOutboundHandler newOutbound = new DirectOutboundHandler("new-outbound", newConfig);
proxyProcessor.registerOutboundHandler(newOutbound);
```

## 7. 错误处理和故障转移

### 7.1 连接失败处理

```java
proxyProcessor.processRequest(request)
    .whenComplete((response, throwable) -> {
        if (throwable != null) {
            // 连接失败，尝试故障转移
            logger.warn("主连接失败，尝试故障转移: {}", throwable.getMessage());
            
            // 可以修改请求的路由属性，强制使用备用outbound
            request.setAttribute("force.outbound", "backup-outbound");
            
            // 重试请求
            proxyProcessor.processRequest(request)
                .whenComplete((retryResponse, retryThrowable) -> {
                    if (retryThrowable != null) {
                        logger.error("故障转移也失败了", retryThrowable);
                    } else {
                        logger.info("故障转移成功");
                    }
                });
        }
    });
```

## 8. 资源清理

### 8.1 优雅关闭

```java
// 关闭代理处理器
proxyProcessor.shutdown();

// 清理路由器
router.clearRoutes();
```

这个组件化架构提供了：

1. **灵活的路由配置** - 支持多种匹配条件和复合规则
2. **可扩展的Outbound** - 易于添加新的出站处理方式
3. **统一的请求模型** - 简化了不同协议间的处理
4. **动态配置** - 支持运行时修改路由规则
5. **监控和统计** - 内置的性能指标收集
6. **错误处理** - 完善的异常处理和故障转移机制