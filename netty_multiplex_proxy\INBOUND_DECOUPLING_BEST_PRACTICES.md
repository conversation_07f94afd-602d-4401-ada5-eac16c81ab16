e_co
nfigs:
  - job_name: 'proxy-client'
    static_configs:
      - targets: ['proxy-client:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 15s

rule_files:
  - "proxy_alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 告警规则
```yaml
# proxy_alerts.yml
groups:
- name: proxy-client-alerts
  rules:
  - alert: ProxyClientDown
    expr: up{job="proxy-client"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Proxy client is down"
      description: "Proxy client has been down for more than 1 minute"

  - alert: HighQueueUsage
    expr: proxy_queue_size / proxy_queue_capacity > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High queue usage detected"
      description: "Queue usage is above 80% for more than 5 minutes"

  - alert: HighErrorRate
    expr: rate(proxy_requests_total{status="error"}[5m]) / rate(proxy_requests_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is above 10% for more than 2 minutes"

  - alert: ConnectionPoolExhausted
    expr: proxy_connection_pool_size == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Connection pool exhausted"
      description: "No available connections in the pool"
```

### 3. 自动化运维

#### 自动扩缩容脚本
```bash
#!/bin/bash
# auto-scale.sh

NAMESPACE="proxy"
DEPLOYMENT="proxy-client"
MIN_REPLICAS=2
MAX_REPLICAS=10
CPU_THRESHOLD=70
MEMORY_THRESHOLD=80

# 获取当前指标
CPU_USAGE=$(kubectl top pods -n $NAMESPACE -l app=$DEPLOYMENT --no-headers | awk '{sum+=$2} END {print sum/NR}' | sed 's/m//')
MEMORY_USAGE=$(kubectl top pods -n $NAMESPACE -l app=$DEPLOYMENT --no-headers | awk '{sum+=$3} END {print sum/NR}' | sed 's/Mi//')
CURRENT_REPLICAS=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE -o jsonpath='{.spec.replicas}')

echo "当前状态: CPU=${CPU_USAGE}m, Memory=${MEMORY_USAGE}Mi, Replicas=${CURRENT_REPLICAS}"

# 扩容逻辑
if [ $CPU_USAGE -gt $CPU_THRESHOLD ] || [ $MEMORY_USAGE -gt $MEMORY_THRESHOLD ]; then
    if [ $CURRENT_REPLICAS -lt $MAX_REPLICAS ]; then
        NEW_REPLICAS=$((CURRENT_REPLICAS + 1))
        echo "触发扩容: $CURRENT_REPLICAS -> $NEW_REPLICAS"
        kubectl scale deployment $DEPLOYMENT -n $NAMESPACE --replicas=$NEW_REPLICAS
    fi
# 缩容逻辑
elif [ $CPU_USAGE -lt 30 ] && [ $MEMORY_USAGE -lt 40 ]; then
    if [ $CURRENT_REPLICAS -gt $MIN_REPLICAS ]; then
        NEW_REPLICAS=$((CURRENT_REPLICAS - 1))
        echo "触发缩容: $CURRENT_REPLICAS -> $NEW_REPLICAS"
        kubectl scale deployment $DEPLOYMENT -n $NAMESPACE --replicas=$NEW_REPLICAS
    fi
fi
```

#### 健康检查和自愈脚本
```bash
#!/bin/bash
# health-check.sh

SERVICE_NAME="proxy-client"
HEALTH_ENDPOINT="http://localhost:8080/actuator/health"
MAX_RETRIES=3
RETRY_INTERVAL=10

check_health() {
    local retry_count=0
    
    while [ $retry_count -lt $MAX_RETRIES ]; do
        if curl -f -s $HEALTH_ENDPOINT > /dev/null; then
            echo "$(date): $SERVICE_NAME 健康检查通过"
            return 0
        else
            retry_count=$((retry_count + 1))
            echo "$(date): $SERVICE_NAME 健康检查失败 (尝试 $retry_count/$MAX_RETRIES)"
            
            if [ $retry_count -lt $MAX_RETRIES ]; then
                sleep $RETRY_INTERVAL
            fi
        fi
    done
    
    return 1
}

restart_service() {
    echo "$(date): 尝试重启 $SERVICE_NAME"
    
    # Docker环境
    if command -v docker &> /dev/null; then
        docker restart $SERVICE_NAME
    # Kubernetes环境
    elif command -v kubectl &> /dev/null; then
        kubectl rollout restart deployment/$SERVICE_NAME
    # Systemd环境
    elif command -v systemctl &> /dev/null; then
        systemctl restart $SERVICE_NAME
    fi
    
    # 等待服务启动
    sleep 30
    
    if check_health; then
        echo "$(date): $SERVICE_NAME 重启成功"
        return 0
    else
        echo "$(date): $SERVICE_NAME 重启失败"
        return 1
    fi
}

# 主逻辑
if ! check_health; then
    echo "$(date): $SERVICE_NAME 健康检查失败，尝试自愈"
    
    if restart_service; then
        echo "$(date): $SERVICE_NAME 自愈成功"
    else
        echo "$(date): $SERVICE_NAME 自愈失败，需要人工介入"
        # 发送告警通知
        curl -X POST "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK" \
             -H 'Content-type: application/json' \
             --data "{\"text\":\"🚨 $SERVICE_NAME 自愈失败，需要人工介入\"}"
    fi
fi
```

## 🧪 测试最佳实践

### 1. 单元测试

#### Inbound组件测试
```java
@ExtendWith(MockitoExtension.class)
class Socks5InboundTest {
    
    @Mock
    private IConnectionManager connectionManager;
    
    @Mock
    private AddressFilter addressFilter;
    
    @Mock
    private ProxyClientConfigManager configManager;
    
    private Socks5Inbound socks5Inbound;
    
    @BeforeEach
    void setUp() {
        socks5Inbound = new Socks5Inbound(1080, connectionManager, addressFilter, configManager);
    }
    
    @Test
    void testStartAndStop() throws Exception {
        // 测试启动
        ChannelFuture startFuture = socks5Inbound.start();
        assertNotNull(startFuture);
        
        // 等待启动完成
        startFuture.sync();
        assertTrue(socks5Inbound.isRunning());
        
        // 测试状态
        ProxyInbound.InboundStatus status = socks5Inbound.getStatus();
        assertTrue(status.isRunning());
        assertEquals(1080, status.getPort());
        
        // 测试停止
        socks5Inbound.stop();
        assertFalse(socks5Inbound.isRunning());
    }
    
    @Test
    void testPortConflict() {
        // 启动第一个实例
        Socks5Inbound first = new Socks5Inbound(1080, connectionManager, addressFilter, configManager);
        assertDoesNotThrow(() -> first.start().sync());
        
        // 尝试启动第二个实例（相同端口）
        Socks5Inbound second = new Socks5Inbound(1080, connectionManager, addressFilter, configManager);
        assertThrows(Exception.class, () -> second.start().sync());
        
        first.stop();
    }
}
```

#### 队列功能测试
```java
@ExtendWith(MockitoExtension.class)
class QueuedConnectionManagerTest {
    
    @Mock
    private ConnectionManager connectionManager;
    
    @Mock
    private SessionHandler sessionHandler;
    
    private QueuedConnectionManager queuedManager;
    
    @BeforeEach
    void setUp() {
        queuedManager = new QueuedConnectionManager(connectionManager, 1000, 10, 100, 3, 1000);
    }
    
    @Test
    void testQueueBuffering() throws InterruptedException {
        queuedManager.start();
        
        // 模拟连接不可用
        when(connectionManager.isConnectionStable()).thenReturn(false);
        
        // 发送数据包
        for (int i = 0; i < 100; i++) {
            queuedManager.sendData(i, ("test data " + i).getBytes());
        }
        
        // 验证数据包被缓存
        PacketQueue.QueueStats stats = queuedManager.getQueueStats();
        assertTrue(stats.getQueueSize() > 0);
        assertTrue(stats.getEnqueuedCount() >= 100);
        
        queuedManager.stop();
    }
    
    @Test
    void testRetryMechanism() throws InterruptedException {
        queuedManager.start();
        
        // 模拟发送失败
        when(connectionManager.isConnectionStable()).thenReturn(true);
        doThrow(new RuntimeException("Network error"))
            .doNothing() // 第二次成功
            .when(connectionManager).sendPacket(any());
        
        // 发送数据包
        queuedManager.sendData(1, "test data".getBytes());
        
        // 等待重试
        Thread.sleep(2000);
        
        // 验证重试发生
        verify(connectionManager, atLeast(2)).sendPacket(any());
        
        queuedManager.stop();
    }
}
```

### 2. 集成测试

#### 端到端测试
```java
@SpringBootTest
@TestPropertySource(properties = {
    "proxy-client.network.proxy.server.host=localhost",
    "proxy-client.network.proxy.server.port=8888",
    "proxy-client.protocols.socks5.instances[0].port=0", // 随机端口
    "proxy-client.protocols.http.instances[0].port=0"
})
class ProxyClientIntegrationTest {
    
    @Autowired
    private ProxyClientManager clientManager;
    
    @MockBean
    private ProxyServerMock proxyServerMock;
    
    @Test
    void testFullProxyFlow() throws Exception {
        // 启动proxy-server mock
        proxyServerMock.start(8888);
        
        // 启动proxy-client
        clientManager.start().get(10, TimeUnit.SECONDS);
        
        // 获取实际监听端口
        List<ProxyInbound> socks5Inbounds = clientManager.getInboundsByProtocol(ProxyInbound.ProxyProtocol.SOCKS5);
        int socks5Port = socks5Inbounds.get(0).getPort();
        
        // 测试SOCKS5连接
        try (Socket socket = new Socket("localhost", socks5Port)) {
            // 发送SOCKS5握手
            OutputStream out = socket.getOutputStream();
            InputStream in = socket.getInputStream();
            
            // SOCKS5握手
            out.write(new byte[]{0x05, 0x01, 0x00}); // VER, NMETHODS, METHODS
            byte[] response = new byte[2];
            in.read(response);
            
            assertEquals(0x05, response[0]); // VER
            assertEquals(0x00, response[1]); // METHOD
            
            // SOCKS5连接请求
            String targetHost = "httpbin.org";
            byte[] connectRequest = buildSocks5ConnectRequest(targetHost, 80);
            out.write(connectRequest);
            
            // 读取连接响应
            byte[] connectResponse = new byte[10];
            in.read(connectResponse);
            assertEquals(0x05, connectResponse[0]); // VER
            assertEquals(0x00, connectResponse[1]); // REP (成功)
            
            // 发送HTTP请求
            String httpRequest = "GET /ip HTTP/1.1\r\nHost: httpbin.org\r\n\r\n";
            out.write(httpRequest.getBytes());
            
            // 读取HTTP响应
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            String responseLine = reader.readLine();
            assertTrue(responseLine.startsWith("HTTP/1.1 200"));
        }
        
        // 验证统计信息
        PacketQueue.QueueStats stats = clientManager.getQueueStats();
        assertTrue(stats.getProcessedCount() > 0);
        
        clientManager.stop();
        proxyServerMock.stop();
    }
    
    private byte[] buildSocks5ConnectRequest(String host, int port) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        baos.write(0x05); // VER
        baos.write(0x01); // CMD (CONNECT)
        baos.write(0x00); // RSV
        baos.write(0x03); // ATYP (DOMAINNAME)
        baos.write(host.length()); // Domain length
        baos.writeBytes(host.getBytes()); // Domain
        baos.write((port >> 8) & 0xFF); // Port high byte
        baos.write(port & 0xFF); // Port low byte
        return baos.toByteArray();
    }
}
```

### 3. 性能测试

#### 负载测试
```java
@Test
void testHighConcurrencyLoad() throws Exception {
    int threadCount = 50;
    int requestsPerThread = 100;
    CountDownLatch latch = new CountDownLatch(threadCount);
    AtomicInteger successCount = new AtomicInteger(0);
    AtomicInteger errorCount = new AtomicInteger(0);
    
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);
    
    for (int i = 0; i < threadCount; i++) {
        executor.submit(() -> {
            try {
                for (int j = 0; j < requestsPerThread; j++) {
                    if (sendProxyRequest()) {
                        successCount.incrementAndGet();
                    } else {
                        errorCount.incrementAndGet();
                    }
                }
            } finally {
                latch.countDown();
            }
        });
    }
    
    // 等待所有请求完成
    assertTrue(latch.await(60, TimeUnit.SECONDS));
    
    // 验证结果
    int totalRequests = threadCount * requestsPerThread;
    double successRate = (double) successCount.get() / totalRequests;
    
    logger.info("负载测试结果: 总请求={}, 成功={}, 失败={}, 成功率={:.2f}%", 
               totalRequests, successCount.get(), errorCount.get(), successRate * 100);
    
    assertTrue(successRate > 0.95, "成功率应该大于95%");
    
    executor.shutdown();
}
```

## 📚 文档和知识管理

### 1. 架构文档维护

#### 架构决策记录 (ADR)
```markdown
# ADR-001: 采用队列化连接管理器

## 状态
已接受

## 背景
原有的直接连接管理器在网络不稳定或proxy-server未启动时会丢失数据包，影响用户体验。

## 决策
采用队列化连接管理器包装原有连接管理器，提供数据包缓冲和重试机制。

## 后果
### 正面影响
- 提高了系统的可靠性
- 改善了用户体验
- 支持更复杂的网络环境

### 负面影响
- 增加了内存使用
- 增加了系统复杂度
- 需要额外的监控和调优

## 实施细节
- 使用装饰器模式包装ConnectionManager
- 实现有界队列防止内存溢出
- 提供配置化的重试机制
```

#### API文档
```java
/**
 * Inbound组件接口
 * 
 * <p>该接口定义了代理接入组件的标准规范，支持多协议扩展。
 * 
 * <h3>使用示例</h3>
 * <pre>{@code
 * // 创建SOCKS5接入组件
 * ProxyInbound socks5 = new Socks5Inbound(1080, connectionManager, addressFilter, configManager);
 * 
 * // 启动组件
 * ChannelFuture future = socks5.start();
 * future.sync(); // 等待启动完成
 * 
 * // 检查状态
 * if (socks5.isRunning()) {
 *     InboundStatus status = socks5.getStatus();
 *     System.out.println("活跃连接数: " + status.getActiveConnections());
 * }
 * 
 * // 停止组件
 * socks5.stop();
 * }</pre>
 * 
 * <h3>实现要求</h3>
 * <ul>
 *   <li>实现类必须是线程安全的</li>
 *   <li>start()方法可以被多次调用，但只有第一次调用生效</li>
 *   <li>stop()方法必须能够优雅地关闭所有资源</li>
 *   <li>getStatus()方法必须返回实时状态信息</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @see AbstractProxyInbound
 * @see Socks5Inbound
 * @see HttpInbound
 */
public interface ProxyInbound {
    // 接口方法...
}
```

### 2. 运维手册

#### 故障排除指南
```markdown
# 故障排除指南

## 常见问题

### 1. 端口绑定失败
**症状**: 启动时报错 "Address already in use"
**原因**: 端口被其他进程占用
**解决方案**:
```bash
# 查找占用端口的进程
netstat -tulpn | grep :1080
# 或使用lsof
lsof -i :1080

# 终止占用进程
kill -9 <PID>

# 或修改配置使用其他端口
```

### 2. 队列溢出
**症状**: 日志显示 "队列已满，丢弃数据包"
**原因**: 队列容量不足或处理速度过慢
**解决方案**:
```yaml
# 增加队列容量
queue:
  capacity: 50000  # 从10000增加到50000

# 或优化处理参数
queue:
  batch-size: 200  # 增加批处理大小
  flush-interval-ms: 5  # 减少刷新间隔
```

### 3. 连接池耗尽
**症状**: 日志显示 "No available connections in pool"
**原因**: 连接池配置过小或连接泄漏
**解决方案**:
```yaml
# 增加连接池大小
connection-pool:
  max-connections-per-host: 50  # 从20增加到50

# 启用连接池监控
monitoring:
  connection-pool:
    enabled: true
    cleanup-interval: 30s
```
```

#### 性能调优指南
```markdown
# 性能调优指南

## JVM参数优化

### 内存设置
```bash
# 生产环境推荐设置
JAVA_OPTS="-Xms2g -Xmx4g -XX:NewRatio=1 -XX:SurvivorRatio=8"

# 启用G1垃圾收集器
JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# 启用GC日志
JAVA_OPTS="$JAVA_OPTS -Xloggc:gc.log -XX:+PrintGCDetails -XX:+PrintGCTimeStamps"
```

### 网络参数优化
```bash
# 增加TCP缓冲区大小
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' >> /etc/sysctl.conf

# 应用设置
sysctl -p
```

### 应用参数调优
```yaml
# 高吞吐量场景
performance:
  worker-threads: 16  # 2倍CPU核心数
  queue:
    capacity: 100000
    batch-size: 500
    flush-interval-ms: 1

# 低延迟场景
performance:
  worker-threads: 8
  queue:
    capacity: 5000
    batch-size: 10
    flush-interval-ms: 1
```
```

## 📋 总结

通过以上最佳实践的实施，可以确保inbound组件与后端连接解耦功能的：

### ✅ 架构优势
1. **高度解耦**: 组件间职责清晰，易于维护和扩展
2. **高可靠性**: 队列缓冲和重试机制提高系统稳定性
3. **高性能**: 批处理和异步处理优化系统性能
4. **高可观测性**: 完善的监控和日志系统

### 🎯 实施建议
1. **渐进式采用**: 从核心功能开始，逐步应用最佳实践
2. **持续监控**: 建立完善的监控体系，及时发现和解决问题
3. **定期优化**: 根据实际使用情况调整配置和参数
4. **文档维护**: 保持文档的及时更新和完善

### 📈 预期收益
- **开发效率**: 提高20-30%的开发和维护效率
- **系统稳定性**: 减少90%以上的因网络问题导致的数据丢失
- **运维成本**: 降低50%的运维工作量
- **用户体验**: 显著改善网络不稳定环境下的使用体验

---

**最后更新**: 2025年1月8日  
**文档版本**: v1.0  
**适用版本**: proxy-client v1.0+