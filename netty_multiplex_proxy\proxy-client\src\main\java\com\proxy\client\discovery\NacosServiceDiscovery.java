package com.proxy.client.discovery;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.api.naming.listener.NamingEvent;
import com.alibaba.nacos.api.naming.listener.EventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Nacos 服务发现客户端
 * 用于从 Nacos 注册中心获取代理服务器地址
 */
public class NacosServiceDiscovery {
    private static final Logger logger = LoggerFactory.getLogger(NacosServiceDiscovery.class);

    private final String serverAddr;
    private final String namespace;
    private final String serviceName;
    private final String groupName;
    private final String username;
    private final String password;

    private NamingService namingService;
    private final AtomicReference<List<Instance>> cachedInstances = new AtomicReference<>();
    private final List<ServiceChangeListener> listeners = new CopyOnWriteArrayList<>();

    private volatile boolean started = false;

    /**
     * 服务变更监听器接口
     */
    public interface ServiceChangeListener {
        /**
         * 服务实例变更回调
         *
         * @param serviceName 服务名
         * @param instances   当前可用实例列表
         */
        void onServiceChange(String serviceName, List<Instance> instances);
    }

    /**
     * 构造函数
     *
     * @param serverAddr  Nacos 服务器地址，格式：host:port
     * @param namespace   命名空间，可以为空
     * @param serviceName 服务名称
     * @param groupName   分组名称，默认为 DEFAULT_GROUP
     */
    public NacosServiceDiscovery(String serverAddr, String namespace, String serviceName, String groupName, String username, String password) {
        this.serverAddr = serverAddr;
        this.namespace = namespace;
        this.serviceName = serviceName;
        this.groupName = groupName != null ? groupName : "DEFAULT_GROUP";
        this.username = username;
        this.password = password;
    }
    /**
     * 启动服务发现
     */
    public synchronized void start() throws NacosException {
        if (started) {
            logger.warn("Nacos 服务发现已经启动");
            return;
        }

        logger.info("启动 Nacos 服务发现 - 服务器: {}, 命名空间: {}, 服务名: {}, 分组: {}, 用户名：{}",
                serverAddr, namespace, serviceName, groupName, username);

        try {
            // 创建 Nacos 配置
            Properties properties = new Properties();
            properties.setProperty("serverAddr", serverAddr);

            if (namespace != null && !namespace.trim().isEmpty()) {
                properties.setProperty("namespace", namespace);
            }

            properties.setProperty("groupName", groupName);

            // 只有当用户名和密码都不为空时才设置认证信息
            if (username != null && !username.trim().isEmpty() &&
                    password != null && !password.trim().isEmpty()) {
                properties.setProperty("username", username);
                properties.setProperty("password", password);
                logger.info("使用用户名/密码认证: {}", username);
            } else {
                logger.warn("未设置用户名/密码，将使用匿名访问");
            }

            // 设置连接超时相关参数
            properties.setProperty("configLongPollTimeout", "10000");
            properties.setProperty("configRetryTime", "3000");
            properties.setProperty("maxRetry", "3");

            // 创建 NamingService
            namingService = NacosFactory.createNamingService(properties);

            // 获取初始服务实例列表
            refreshServiceInstances();

            // 注册服务变更监听器
            namingService.subscribe(serviceName, groupName, new EventListener() {
                @Override
                public void onEvent(com.alibaba.nacos.api.naming.listener.Event event) {
                    if (event instanceof NamingEvent) {
                        NamingEvent namingEvent = (NamingEvent) event;
                        logger.info("服务 {} 实例发生变更，当前实例数: {}",
                                namingEvent.getServiceName(), namingEvent.getInstances().size());

                        // 更新缓存的实例列表
                        cachedInstances.set(namingEvent.getInstances());

                        // 通知监听器
                        notifyListeners(namingEvent.getServiceName(), namingEvent.getInstances());
                    }
                }
            });

            started = true;
            logger.info("Nacos 服务发现启动成功");

        } catch (Exception e) {
            logger.error("启动 Nacos 服务发现失败", e);
            throw new NacosException(NacosException.SERVER_ERROR, "启动 Nacos 服务发现失败: " + e.getMessage());
        }
    }

    /**
     * 停止服务发现
     */
    public synchronized void stop() {
        if (!started) {
            return;
        }

        logger.info("停止 Nacos 服务发现");

        try {
            if (namingService != null) {
                // 取消订阅
                namingService.unsubscribe(serviceName, groupName, new EventListener() {
                    @Override
                    public void onEvent(com.alibaba.nacos.api.naming.listener.Event event) {
                        // 空实现，用于取消订阅
                    }
                });

                // 关闭 NamingService
                namingService.shutDown();
            }
        } catch (Exception e) {
            logger.error("停止 Nacos 服务发现时发生错误", e);
        } finally {
            started = false;
            cachedInstances.set(null);
            listeners.clear();
            logger.info("Nacos 服务发现已停止");
        }
    }

    /**
     * 获取一个可用的服务实例（负载均衡）
     *
     * @return 服务实例，如果没有可用实例则返回 null
     */
    public Instance getAvailableInstance() {
        List<Instance> instances = getHealthyInstances();
        if (instances == null || instances.isEmpty()) {
            logger.warn("没有可用的服务实例: {}", serviceName);
            return null;
        }

        // 简单的随机负载均衡
        int index = ThreadLocalRandom.current().nextInt(instances.size());
        Instance instance = instances.get(index);

        logger.debug("选择服务实例: {}:{}", instance.getIp(), instance.getPort());
        return instance;
    }

    /**
     * 获取所有健康的服务实例
     *
     * @return 健康的服务实例列表
     */
    public List<Instance> getHealthyInstances() {
        if (!started) {
            logger.warn("Nacos 服务发现未启动");
            return null;
        }

        try {
            // 优先使用缓存的实例列表
            List<Instance> cached = cachedInstances.get();
            if (cached != null && !cached.isEmpty()) {
                return cached.stream()
                        .filter(Instance::isHealthy)
                        .filter(Instance::isEnabled)
                        .collect(java.util.stream.Collectors.toList());
            }

            // 如果缓存为空，主动查询
            refreshServiceInstances();
            cached = cachedInstances.get();
            if (cached != null) {
                return cached.stream()
                        .filter(Instance::isHealthy)
                        .filter(Instance::isEnabled)
                        .collect(java.util.stream.Collectors.toList());
            }

        } catch (Exception e) {
            logger.error("获取健康服务实例失败", e);
        }

        return null;
    }

    /**
     * 刷新服务实例列表
     */
    private void refreshServiceInstances() throws NacosException {
        if (namingService == null) {
            return;
        }

        List<Instance> instances = namingService.getAllInstances(serviceName, groupName);
        cachedInstances.set(instances);

        logger.info("刷新服务实例列表: {} 个实例", instances.size());
        for (Instance instance : instances) {
            logger.debug("服务实例: {}:{} (健康: {}, 启用: {})",
                    instance.getIp(), instance.getPort(),
                    instance.isHealthy(), instance.isEnabled());
        }
    }

    /**
     * 添加服务变更监听器
     */
    public void addServiceChangeListener(ServiceChangeListener listener) {
        if (listener != null) {
            listeners.add(listener);
        }
    }

    /**
     * 移除服务变更监听器
     */
    public void removeServiceChangeListener(ServiceChangeListener listener) {
        listeners.remove(listener);
    }

    /**
     * 通知所有监听器服务发生变更
     */
    private void notifyListeners(String serviceName, List<Instance> instances) {
        for (ServiceChangeListener listener : listeners) {
            try {
                listener.onServiceChange(serviceName, instances);
            } catch (Exception e) {
                logger.error("通知服务变更监听器失败", e);
            }
        }
    }

    /**
     * 检查服务发现是否已启动
     */
    public boolean isStarted() {
        return started;
    }

    /**
     * 获取服务名称
     */
    public String getServiceName() {
        return serviceName;
    }

    /**
     * 获取分组名称
     */
    public String getGroupName() {
        return groupName;
    }

    /**
     * 获取 Nacos 服务器地址
     */
    public String getServerAddr() {
        return serverAddr;
    }

    /**
     * 获取命名空间
     */
    public String getNamespace() {
        return namespace;
    }

    /**
     * 获取用户名
     *
     * @return
     */
    public String getUsername() {
        return username;
    }

    /**
     * 获取密码
     *
     * @return
     */
    public String getPassword() {
        return password;
    }
}