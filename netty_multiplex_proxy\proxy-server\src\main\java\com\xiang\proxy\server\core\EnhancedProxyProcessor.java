package com.xiang.proxy.server.core;

import com.xiang.proxy.server.config.ProxyProcessorConfig;
import com.xiang.proxy.server.router.Router;
import com.xiang.proxy.server.router.RouteResult;
import com.xiang.proxy.server.outbound.OutboundHandler;
import com.xiang.proxy.server.outbound.OutboundConnection;
import io.netty.channel.Channel;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 增强的代理处理器 - 直接复用OutboundConnection实现连接复用
 * 避免重复路由，提高连接复用效率
 * 支持多路复用协议和各种连接类型
 */
public class EnhancedProxyProcessor extends ProxyProcessor {
    private static final Logger logger = LoggerFactory.getLogger(EnhancedProxyProcessor.class);

    /**
     * OutboundConnection复用缓存
     * Key: host:port:protocol:clientId
     * Value: OutboundConnection
     */
    private final Map<String, OutboundConnection> connectionCache = new ConcurrentHashMap<>();

    public EnhancedProxyProcessor(Router router) {
        super(router);
        logger.info("创建增强代理处理器（默认配置）");
    }

    public EnhancedProxyProcessor(Router router, ProxyProcessorConfig config) {
        super(router, config);
        logger.info("创建增强代理处理器: {}", config);
    }

    /**
     * 重写队列处理逻辑，使用OutboundConnection复用机制
     */
    @Override
    protected void processQueue(int queueIndex) {
        logger.debug("增强队列 {} 工作线程启动", queueIndex);
        BlockingQueue<QueuedRequest> queue = getRequestQueue(queueIndex);

        while (getRunningState().get()) {
            try {
                QueuedRequest queuedRequest = queue.poll(1, TimeUnit.SECONDS);
                if (queuedRequest != null) {
                    processQueuedRequestWithConnectionReuse(queuedRequest, queueIndex);
                } else {
                    // 空闲时处理连接维护
                    performConnectionMaintenance();
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.info("增强队列 {} 工作线程被中断", queueIndex);
                break;
            } catch (Exception e) {
                logger.error("增强队列 {} 处理请求时发生异常", queueIndex, e);
            }
        }

        logger.debug("增强队列 {} 工作线程结束", queueIndex);
    }

    /**
     * 使用OutboundConnection复用处理请求
     */
    private void processQueuedRequestWithConnectionReuse(QueuedRequest queuedRequest, int queueIndex) {
        ProxyRequest request = queuedRequest.getRequest();
        CompletableFuture<ProxyResponse> future = queuedRequest.getFuture();

        try {
            // 生成连接缓存键
            String connectionKey = buildConnectionKey(request);

            // 尝试获取已缓存的OutboundConnection
            OutboundConnection cachedConnection = connectionCache.get(connectionKey);

            if (cachedConnection != null && isConnectionUsable(cachedConnection)) {
                // 连接可用，直接使用
                logger.debug("复用已缓存的连接: {}", connectionKey);
                handleCachedConnection(request, future, cachedConnection);
            } else {
                // 连接不可用或不存在，需要建立新连接
                if (cachedConnection != null) {
                    // 移除无效连接
                    connectionCache.remove(connectionKey);
                    logger.debug("移除无效连接: {}", connectionKey);
                }

                // 建立新连接
                establishNewConnectionWithCaching(request, future, connectionKey);
            }

        } catch (Exception e) {
            logger.error("处理连接复用请求时发生异常: {}", request.getRequestId(), e);
            future.complete(ProxyResponse.error(request.getRequestId(), e.getMessage()));
        }
    }

    /**
     * 执行连接维护 - 清理无效连接
     */
    private void performConnectionMaintenance() {
        try {
            // 清理无效连接
            connectionCache.entrySet().removeIf(entry -> {
                OutboundConnection connection = entry.getValue();
                if (!isConnectionUsable(connection)) {
                    logger.debug("清理无效连接: {}", entry.getKey());
                    return true;
                }
                return false;
            });

            if (logger.isTraceEnabled()) {
                logger.trace("连接维护检查完成，当前缓存连接数: {}", connectionCache.size());
            }
        } catch (Exception e) {
            logger.warn("执行连接维护时发生异常", e);
        }
    }


    /**
     * 生成连接缓存键
     */
    private String buildConnectionKey(ProxyRequest request) {
        return request.getTargetHost() + ":" + request.getTargetPort() + ":" +
                request.getProtocol() + ":" + request.getClientId();
    }

    /**
     * 检查OutboundConnection是否可用
     */
    private boolean isConnectionUsable(OutboundConnection connection) {
        if (connection == null) {
            return false;
        }

        Channel channel = connection.getBackendChannel();
        return channel != null && channel.isActive() && channel.isWritable();
    }

    /**
     * 处理已缓存的连接
     */
    private void handleCachedConnection(ProxyRequest request, CompletableFuture<ProxyResponse> future,
                                        OutboundConnection connection) {
        try {
            // 发送数据（如果有）
            if (request.hasData()) {
                Channel channel = connection.getBackendChannel();
                channel.writeAndFlush(request.getData().retain());
                logger.debug("通过复用连接发送数据: {}", request.getRequestId());
            }

            // 更新连接活跃时间
            connection.markActive();

            // 返回成功响应，包含连接信息
            future.complete(ProxyResponse.success(request.getRequestId(), connection));

        } catch (Exception e) {
            logger.error("处理缓存连接时发生异常: {}", request.getRequestId(), e);
            future.complete(ProxyResponse.error(request.getRequestId(), e.getMessage()));
        }
    }

    /**
     * 建立新连接并缓存
     */
    private void establishNewConnectionWithCaching(ProxyRequest request, CompletableFuture<ProxyResponse> future,
                                                   String connectionKey) {
        try {
            // 路由请求
            RouteResult routeResult = getRouter().route(request);
            if (!routeResult.isSuccess()) {
                future.complete(ProxyResponse.failure(request.getRequestId(), routeResult.getReason()));
                return;
            }

            // 获取出站处理器
            OutboundHandler outboundHandler = getOutboundHandler(routeResult.getOutboundId());
            if (outboundHandler == null) {
                future.complete(ProxyResponse.failure(request.getRequestId(),
                        "未找到出站处理器: " + routeResult.getOutboundId()));
                return;
            }

            // 建立连接
                CompletableFuture<OutboundConnection> connectFuture = outboundHandler.connect(request);

            // 处理连接结果
            connectFuture.whenComplete((outboundConnection, throwable) -> {
                if (throwable != null) {
                    logger.warn("建立连接失败: {}:{}", request.getTargetHost(), request.getTargetPort(), throwable);
                    future.complete(ProxyResponse.failure(request.getRequestId(), "连接失败: " + throwable.getMessage()));
                } else {
                    logger.debug("连接建立成功: {}:{}", request.getTargetHost(), request.getTargetPort());

                    // 缓存连接以供复用
                    connectionCache.put(connectionKey, outboundConnection);
                    activeConnections.put(outboundConnection.getConnectionId(), outboundConnection);
                    logger.debug("连接已缓存: {}", connectionKey);

                    // 发送数据（如果有）
                    if (request.hasData()) {
                        Channel channel = outboundConnection.getBackendChannel();
                        if (channel != null && channel.isActive()) {
                            channel.writeAndFlush(request.getData().retain());
                            logger.debug("通过新连接发送数据: {}", request.getRequestId());
                        }
                    }

                    // 返回成功响应，包含连接信息
                    future.complete(ProxyResponse.success(request.getRequestId(), outboundConnection));
                }
            });

        } catch (Exception e) {
            logger.error("建立新连接时发生异常: {}", request.getRequestId(), e);
            future.complete(ProxyResponse.error(request.getRequestId(), e.getMessage()));
        }
    }

}
