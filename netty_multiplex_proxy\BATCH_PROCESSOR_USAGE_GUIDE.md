# BatchProxyProcessor 使用指南

## 概述

`BatchProxyProcessor` 是 `ProxyProcessor` 的优化版本，通过批量处理请求来显著提升高并发场景下的性能。它继承了原有处理器的所有功能，同时增加了批量处理能力。

## 核心优势

### 1. 批量处理
- **减少线程切换**: 批量处理多个请求，减少频繁的线程唤醒
- **提高吞吐量**: 在高负载下可提升 30-50% 的处理能力
- **更好的资源利用**: CPU 和内存使用更加高效

### 2. 智能批次收集
- **动态批次大小**: 根据队列中的请求数量动态调整批次
- **超时机制**: 避免低负载时的延迟增加
- **并行处理**: 批次内的请求并行处理

## 快速开始

### 基本使用

```java
// 1. 创建配置
ProxyProcessorConfig config = ProxyProcessorConfig.defaultConfig()
    .setBatchSize(15)        // 批次大小
    .setBatchTimeoutMs(50);  // 批次超时

// 2. 创建处理器
Router router = new MyRouter();
BatchProxyProcessor processor = new BatchProxyProcessor(router, config);

// 3. 启动处理器
processor.start();

// 4. 处理请求
CompletableFuture<ProxyResponse> future = processor.processRequest(request);
```

### 使用工厂类

```java
// 创建高性能批量处理器
BatchProxyProcessor processor = ProxyProcessorFactory.createHighPerformanceProcessor(router);

// 或者使用构建器
ProxyProcessor processor = ProxyProcessorFactory.builder()
    .router(router)
    .type(ProcessorType.BATCH)
    .batchSize(20)
    .queueCount(8)
    .autoStart(true)
    .build();
```

## 配置参数详解

### 批量处理参数

| 参数 | 默认值 | 说明 | 推荐值 |
|------|--------|------|--------|
| batchSize | 10 | 每个批次的最大请求数 | 高QPS: 20-50<br>中QPS: 10-20<br>低QPS: 5-10 |
| batchTimeoutMs | 50 | 批次收集超时时间(毫秒) | 低延迟: 10-30ms<br>平衡: 50-100ms<br>高吞吐: 100-200ms |

### 队列参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| queueCount | CPU核心数×2 | 队列数量 |
| queueCapacity | 10000 | 每个队列容量 |

## 性能调优

### 1. 批次大小调优

```java
// 高QPS场景 (>10000 QPS)
config.setBatchSize(30).setBatchTimeoutMs(30);

// 中等QPS场景 (1000-10000 QPS)  
config.setBatchSize(15).setBatchTimeoutMs(50);

// 低QPS场景 (<1000 QPS)
config.setBatchSize(5).setBatchTimeoutMs(100);
```

### 2. 根据业务特点调优

```java
// 延迟敏感型业务
config.setBatchSize(5).setBatchTimeoutMs(20);

// 吞吐量优先型业务
config.setBatchSize(50).setBatchTimeoutMs(100);

// 平衡型业务
config.setBatchSize(15).setBatchTimeoutMs(50);
```

### 3. 自动推荐配置

```java
// 根据预期QPS获取推荐配置
ProcessorRecommendation recommendation = 
    ProxyProcessorFactory.getRecommendation(5000, 1024);

ProxyProcessor processor = ProxyProcessorFactory.createProcessor(
    recommendation.getType(), 
    router, 
    recommendation.getConfig()
);
```

## 监控和统计

### 基本监控

```java
// 获取队列统计
ProxyProcessor.QueueStats queueStats = processor.getQueueStats();
System.out.println("队列状态: " + queueStats);

// 获取批处理统计
BatchProxyProcessor.BatchStats batchStats = processor.getBatchStats();
System.out.println("批处理状态: " + batchStats);
```

### 详细监控

```java
// 定期监控
ScheduledExecutorService monitor = Executors.newSingleThreadScheduledExecutor();
monitor.scheduleAtFixedRate(() -> {
    QueueStats stats = processor.getQueueStats();
    BatchStats batchStats = processor.getBatchStats();
    
    logger.info("=== 性能监控 ===");
    logger.info("总队列大小: {}", stats.getTotalQueueSize());
    logger.info("已处理请求: {}", stats.getProcessedRequests());
    logger.info("批次大小: {}", batchStats.getBatchSize());
    logger.info("批次超时: {}ms", batchStats.getBatchTimeoutMs());
    
    // 检查队列负载
    double loadRatio = (double) stats.getTotalQueueSize() / 
        (processor.getQueueCount() * processor.getConfig().getQueueCapacity());
    
    if (loadRatio > 0.8) {
        logger.warn("队列负载过高: {:.2f}%", loadRatio * 100);
    }
    
}, 0, 10, TimeUnit.SECONDS);
```

## 性能测试

### 基准测试

```java
public class BatchProcessorBenchmark {
    
    public static void main(String[] args) throws Exception {
        Router router = new MockRouter();
        
        // 测试不同配置的性能
        testConfiguration("小批次", 5, 20);
        testConfiguration("中批次", 15, 50);  
        testConfiguration("大批次", 30, 100);
    }
    
    private static void testConfiguration(String name, int batchSize, long timeout) 
            throws Exception {
        
        ProxyProcessorConfig config = ProxyProcessorConfig.defaultConfig()
            .setBatchSize(batchSize)
            .setBatchTimeoutMs(timeout);
            
        BatchProxyProcessor processor = new BatchProxyProcessor(router, config);
        processor.start();
        
        // 执行性能测试
        long duration = performanceTest(processor, 10000);
        
        System.out.printf("%s配置 (批次:%d, 超时:%dms) - 耗时: %dms%n", 
            name, batchSize, timeout, duration);
            
        processor.shutdown();
    }
}
```

### 压力测试

```java
// 持续压力测试
BatchProxyProcessorExample.stressTest(processor, 60); // 60秒压力测试
```

## 最佳实践

### 1. 配置选择

```java
// 生产环境推荐配置
ProxyProcessorConfig productionConfig = ProxyProcessorConfig.builder()
    .setQueueCount(Runtime.getRuntime().availableProcessors() * 2)
    .setQueueCapacity(20000)
    .setBatchSize(20)
    .setBatchTimeoutMs(50)
    .setShutdownTimeoutSeconds(30)
    .build();
```

### 2. 错误处理

```java
processor.processRequest(request)
    .whenComplete((response, throwable) -> {
        if (throwable != null) {
            logger.error("请求处理失败: {}", request.getRequestId(), throwable);
            // 实现重试逻辑
        } else if (!response.isSuccess()) {
            logger.warn("请求处理不成功: {} - {}", 
                request.getRequestId(), response.getMessage());
        }
    });
```

### 3. 优雅关闭

```java
// 注册关闭钩子
Runtime.getRuntime().addShutdownHook(new Thread(() -> {
    logger.info("开始关闭处理器...");
    processor.shutdown();
    logger.info("处理器关闭完成");
}));
```

### 4. 资源管理

```java
// 确保ByteBuf正确释放
try {
    ByteBuf data = Unpooled.wrappedBuffer(requestData);
    ProxyRequest request = ProxyRequest.builder()
        .data(data)
        // ... 其他配置
        .build();
    
    processor.processRequest(request);
} finally {
    // 在适当的时候释放资源
    // data.release(); // 注意：通常由框架自动管理
}
```

## 故障排查

### 常见问题

1. **队列满载**
   ```java
   // 症状：请求被拒绝，日志显示 "Request queue is full"
   // 解决：增加队列容量或优化处理速度
   config.setQueueCapacity(50000);
   ```

2. **批次超时过长**
   ```java
   // 症状：低负载时延迟增加
   // 解决：减少批次超时时间
   config.setBatchTimeoutMs(20);
   ```

3. **内存使用过高**
   ```java
   // 症状：内存持续增长
   // 解决：减少队列容量和批次大小
   config.setQueueCapacity(5000).setBatchSize(10);
   ```

### 性能分析

```java
// 启用详细日志
Logger batchLogger = LoggerFactory.getLogger(BatchProxyProcessor.class);
// 设置日志级别为 DEBUG

// 监控关键指标
- 队列深度变化
- 批次处理时间
- 请求成功率
- 内存使用情况
```

## 与标准处理器对比

| 特性 | 标准处理器 | 批量处理器 |
|------|------------|------------|
| 处理模式 | 单个请求 | 批量请求 |
| 吞吐量 | 基准 | +30-50% |
| 延迟 | 低负载时更低 | 高负载时更优 |
| 资源使用 | 标准 | 更高效 |
| 复杂度 | 简单 | 中等 |
| 适用场景 | 低-中负载 | 中-高负载 |

## 总结

`BatchProxyProcessor` 通过批量处理机制显著提升了高并发场景下的性能，特别适合：

- **高QPS场景** (>1000 QPS)
- **吞吐量优先的业务**
- **资源受限的环境**

合理配置批次大小和超时时间，可以在延迟和吞吐量之间找到最佳平衡点。建议在生产环境部署前进行充分的性能测试和调优。
</text>