package com.xiang.proxy.server.inbound;

import com.xiang.proxy.server.core.ProxyRequest;
import io.netty.channel.Channel;

/**
 * Inbound处理器接口
 * 负责接收各种协议的入站流量，并将其标准化为统一的ProxyRequest
 * 
 * 注意：这个接口主要用于协议层面的处理，实际的网络服务器由InboundServer负责
 */
public interface InboundHandler {
    
    /**
     * 处理入站请求
     * @param channel 客户端连接通道
     * @param message 接收到的消息
     */
    void handleInbound(Channel channel, Object message);
    
    /**
     * 检查是否支持指定协议
     * @param protocol 协议名称
     * @return 是否支持
     */
    boolean supports(String protocol);
    
    /**
     * 获取协议名称
     * @return 协议名称
     */
    String getProtocolName();
    
    /**
     * 获取处理器优先级
     * 数字越小优先级越高
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
    
    /**
     * 初始化处理器
     */
    default void initialize() {
        // 默认空实现
    }
    
    /**
     * 销毁处理器
     */
    default void destroy() {
        // 默认空实现
    }
    
    /**
     * 检查连接是否有效
     * @param channel 客户端连接通道
     * @return 是否有效
     */
    default boolean isValidConnection(Channel channel) {
        return channel != null && channel.isActive();
    }
    
    /**
     * 获取处理器统计信息
     * @return 统计信息
     */
    default InboundHandlerStatistics getStatistics() {
        return new InboundHandlerStatistics();
    }
    
    /**
     * 重置统计信息
     */
    default void resetStatistics() {
        // 默认空实现
    }
}