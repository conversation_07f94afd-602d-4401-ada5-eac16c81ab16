package com.xiang.proxy.server.outbound;

/**
 * 输出处理器类型枚举
 */
public enum OutboundHandlerType {
    TCP_DIRECT("tcp_direct", "TCP直连"),
    UDP_DIRECT("udp_direct", "UDP直连");
//    PROXY_CHAIN("proxy_chain", "代理链"),
//    LOAD_BALANCER("load_balancer", "负载均衡"),
//    PROXY_HTTP("proxy_http", "HTTP代理"),
//    PROXY_SOCKS5("proxy_socks5", "SOCKS5代理"),
//    PROXY_SOCKS4("proxy_socks4", "SOCKS4代理"),

    private String type;
    private String name;

    OutboundHandlerType(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}