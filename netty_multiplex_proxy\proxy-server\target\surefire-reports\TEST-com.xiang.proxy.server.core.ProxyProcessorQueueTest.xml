<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.317" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\target\test-classes;C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\target\classes;E:\.m2\repository\io\netty\netty-all\4.1.100.Final\netty-all-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-buffer\4.1.100.Final\netty-buffer-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec\4.1.100.Final\netty-codec-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-dns\4.1.100.Final\netty-codec-dns-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-haproxy\4.1.100.Final\netty-codec-haproxy-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-http\4.1.100.Final\netty-codec-http-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-http2\4.1.100.Final\netty-codec-http2-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-memcache\4.1.100.Final\netty-codec-memcache-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-mqtt\4.1.100.Final\netty-codec-mqtt-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-redis\4.1.100.Final\netty-codec-redis-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-smtp\4.1.100.Final\netty-codec-smtp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-socks\4.1.100.Final\netty-codec-socks-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-stomp\4.1.100.Final\netty-codec-stomp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-xml\4.1.100.Final\netty-codec-xml-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-common\4.1.100.Final\netty-common-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler\4.1.100.Final\netty-handler-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.100.Final\netty-transport-native-unix-common-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler-proxy\4.1.100.Final\netty-handler-proxy-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler-ssl-ocsp\4.1.100.Final\netty-handler-ssl-ocsp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver\4.1.100.Final\netty-resolver-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver-dns\4.1.100.Final\netty-resolver-dns-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport\4.1.100.Final\netty-transport-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-rxtx\4.1.100.Final\netty-transport-rxtx-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-sctp\4.1.100.Final\netty-transport-sctp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-udt\4.1.100.Final\netty-transport-udt-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.100.Final\netty-transport-classes-epoll-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-classes-kqueue\4.1.100.Final\netty-transport-classes-kqueue-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.100.Final\netty-resolver-dns-classes-macos-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-native-epoll\4.1.100.Final\netty-transport-native-epoll-4.1.100.Final-linux-x86_64.jar;E:\.m2\repository\io\netty\netty-transport-native-epoll\4.1.100.Final\netty-transport-native-epoll-4.1.100.Final-linux-aarch_64.jar;E:\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.100.Final\netty-transport-native-kqueue-4.1.100.Final-osx-x86_64.jar;E:\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.100.Final\netty-transport-native-kqueue-4.1.100.Final-osx-aarch_64.jar;E:\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.100.Final\netty-resolver-dns-native-macos-4.1.100.Final-osx-x86_64.jar;E:\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.100.Final\netty-resolver-dns-native-macos-4.1.100.Final-osx-aarch_64.jar;E:\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;E:\.m2\repository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;E:\.m2\repository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;E:\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;E:\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;E:\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;E:\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;E:\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;E:\.m2\repository\org\mockito\mockito-core\5.5.0\mockito-core-5.5.0.jar;E:\.m2\repository\net\bytebuddy\byte-buddy\1.14.6\byte-buddy-1.14.6.jar;E:\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.6\byte-buddy-agent-1.14.6.jar;E:\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;E:\.m2\repository\org\mockito\mockito-junit-jupiter\5.5.0\mockito-junit-jupiter-5.5.0.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\graalvm-jdk-21.0.8+12.1\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire17198468607037144957\surefirebooter-20250822095406399_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire17198468607037144957 2025-08-22T09-54-06_274-jvmRun1 surefire-20250822095406399_1tmp surefire_0-20250822095406399_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="ProxyProcessorQueueTest"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\target\test-classes;C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\target\classes;E:\.m2\repository\io\netty\netty-all\4.1.100.Final\netty-all-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-buffer\4.1.100.Final\netty-buffer-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec\4.1.100.Final\netty-codec-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-dns\4.1.100.Final\netty-codec-dns-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-haproxy\4.1.100.Final\netty-codec-haproxy-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-http\4.1.100.Final\netty-codec-http-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-http2\4.1.100.Final\netty-codec-http2-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-memcache\4.1.100.Final\netty-codec-memcache-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-mqtt\4.1.100.Final\netty-codec-mqtt-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-redis\4.1.100.Final\netty-codec-redis-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-smtp\4.1.100.Final\netty-codec-smtp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-socks\4.1.100.Final\netty-codec-socks-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-stomp\4.1.100.Final\netty-codec-stomp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-xml\4.1.100.Final\netty-codec-xml-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-common\4.1.100.Final\netty-common-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler\4.1.100.Final\netty-handler-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.100.Final\netty-transport-native-unix-common-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler-proxy\4.1.100.Final\netty-handler-proxy-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler-ssl-ocsp\4.1.100.Final\netty-handler-ssl-ocsp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver\4.1.100.Final\netty-resolver-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver-dns\4.1.100.Final\netty-resolver-dns-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport\4.1.100.Final\netty-transport-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-rxtx\4.1.100.Final\netty-transport-rxtx-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-sctp\4.1.100.Final\netty-transport-sctp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-udt\4.1.100.Final\netty-transport-udt-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.100.Final\netty-transport-classes-epoll-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-classes-kqueue\4.1.100.Final\netty-transport-classes-kqueue-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.100.Final\netty-resolver-dns-classes-macos-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-native-epoll\4.1.100.Final\netty-transport-native-epoll-4.1.100.Final-linux-x86_64.jar;E:\.m2\repository\io\netty\netty-transport-native-epoll\4.1.100.Final\netty-transport-native-epoll-4.1.100.Final-linux-aarch_64.jar;E:\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.100.Final\netty-transport-native-kqueue-4.1.100.Final-osx-x86_64.jar;E:\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.100.Final\netty-transport-native-kqueue-4.1.100.Final-osx-aarch_64.jar;E:\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.100.Final\netty-resolver-dns-native-macos-4.1.100.Final-osx-x86_64.jar;E:\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.100.Final\netty-resolver-dns-native-macos-4.1.100.Final-osx-aarch_64.jar;E:\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;E:\.m2\repository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;E:\.m2\repository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;E:\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;E:\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;E:\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;E:\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;E:\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;E:\.m2\repository\org\mockito\mockito-core\5.5.0\mockito-core-5.5.0.jar;E:\.m2\repository\net\bytebuddy\byte-buddy\1.14.6\byte-buddy-1.14.6.jar;E:\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.6\byte-buddy-agent-1.14.6.jar;E:\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;E:\.m2\repository\org\mockito\mockito-junit-jupiter\5.5.0\mockito-junit-jupiter-5.5.0.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\graalvm-jdk-21.0.8+12.1"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="jdk.internal.vm.ci.enabled" value="true"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire17198468607037144957\surefirebooter-20250822095406399_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.8+12-LTS-jvmci-23.1-b72"/>
    <property name="user.name" value="xiang"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Oracle GraalVM 21.0.8+12.1"/>
    <property name="localRepository" value="E:\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.8"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="C:\Program Files\Java\graalvm-jdk-21.0.8+12.1\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;E:\Program Files (x86)\VMware\bin\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;E:\nodejs22\;D:\xshell\;C:\Program Files\Java\graalvm-jdk-21.0.8+12.1\bin;D:\Git\cmd;F:\Windows Kits\10\Windows Performance Toolkit\;E:\maven-3.9.9\bin;D:\Lingma\bin;D:\golang\bin;D:\mingw64\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;E:\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Ollama;D:\cursor\resources\app\bin;D:\Windsurf\bin;D:\Kiro\bin;D:\Comate\bin;C:\Users\<USER>\go\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.8+12-LTS-jvmci-23.1-b72"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="testQueueDistribution" classname="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.273">
    <system-out><![CDATA[09:54:07.158 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
09:54:07.158 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=4, queueCapacity=1000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
09:54:07.158 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
09:54:07.177 [main] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
09:54:07.177 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
09:54:07.178 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 4
队列 0 请求数: 24
队列 1 请求数: 24
队列 2 请求数: 26
队列 3 请求数: 26
]]></system-out>
  </testcase>
  <testcase name="testQueueIndexConsistency" classname="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.013">
    <system-out><![CDATA[09:54:07.241 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
09:54:07.241 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=4, queueCapacity=1000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
09:54:07.242 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
09:54:07.243 [main] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
09:54:07.243 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
09:54:07.243 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 4
]]></system-out>
  </testcase>
  <testcase name="testConcurrentRequestProcessing" classname="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.012">
    <system-out><![CDATA[09:54:07.245 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
09:54:07.245 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=4, queueCapacity=1000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
09:54:07.246 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
09:54:07.246 [main] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
09:54:07.246 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
09:54:07.246 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 4
09:54:07.250 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 edf69cd1-7110-426c-ab7a-0f7b3ef3fa55 处理失败: Outbound handler not found: test-outbound
09:54:07.250 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 622d4809-f43b-4180-ac13-6de2b2b65cc5 处理失败: Outbound handler not found: test-outbound
09:54:07.250 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8a8eec38-5a6a-42d9-ac18-b8c1839d709b 处理失败: Outbound handler not found: test-outbound
09:54:07.250 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 ae1346ff-50f7-4fb9-8344-48e342fe9f8e 处理失败: Outbound handler not found: test-outbound
09:54:07.251 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 ae5f8d9f-59a5-46a1-8e0f-8b8696406dff 处理失败: Outbound handler not found: test-outbound
09:54:07.251 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 bc6a60be-f36c-46fe-bea7-029ddb6ffa70 处理失败: Outbound handler not found: test-outbound
09:54:07.251 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8be3d70a-7d24-4e80-9390-8be4b1bd1cdd 处理失败: Outbound handler not found: test-outbound
09:54:07.251 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 948f288c-abc5-4b9e-a8e9-d6a0f689d451 处理失败: Outbound handler not found: test-outbound
09:54:07.251 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 26c1bb03-e8f1-469f-a875-7df828cb9af9 处理失败: Outbound handler not found: test-outbound
09:54:07.251 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 b29ab3c9-7177-44e8-9ec6-4222cf44d69c 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 1465ed8e-f070-4c93-a28b-edf81b6d21ca 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 1cc9a823-812a-4ac9-b9b6-a5948951acc5 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 133083da-36b6-4293-b4e6-8cf0cf9fbe8f 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 2d5ae080-7d15-4035-95a7-693242f845a2 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 7a733341-ee1f-4799-9859-0a60254a6f75 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 95c6b561-b69d-423e-92f2-28553ba532c0 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 72e5ed17-6f31-40ff-ae14-41e0d22462ef 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 532a7c89-5f71-4285-947e-7c55675df4b8 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 9a8b5217-147b-4b4f-a4ce-9b8b937a3d5d 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 3fdf9b09-a297-45c8-9c7c-46732fed26a8 处理失败: Outbound handler not found: test-outbound
09:54:07.252 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 bd39c28a-ae89-4b54-9457-c76267907d81 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 99fb0984-875f-4b21-9107-09bfb61dd233 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d418fa67-7c2e-43e2-8676-6ab221264518 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 ed6a029f-eead-4370-82d0-834881358b93 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 6e88d974-e335-433a-ad6f-7faecd0fe597 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 f53cf332-01d0-40ac-a93c-58937e1aa13f 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 32d7fba9-1ac5-4641-9a72-9877323b5366 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 35a91c0c-d213-4c88-9f52-f55959867f8d 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 5701dfd0-8049-4924-8080-a4af47ef430a 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d22fab56-2648-45af-8694-a415deb36d8d 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 7a7eec59-4bf7-4b90-834c-97c4d4bcf9ad 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 ea91b133-c49f-4128-90ee-e4ca297b3e7b 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 0ee2deca-fc21-40e2-8c83-98db4e579401 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 53ad3cc4-dc64-4a15-bf0b-30322facdada 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 e9ca8c20-4df6-4b8e-bc71-ed928615a566 处理失败: Outbound handler not found: test-outbound
09:54:07.253 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 30b0269e-273b-4a84-83ec-66284e923036 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8d83e0a1-0aef-4383-9517-df4539b9c1f8 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 e52a208b-8127-4b14-a858-629343ec37d0 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 a9ce5f80-62f8-46eb-a64a-f0808365472e 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 2d71ca10-aabf-4958-8928-c8d8dbdb5460 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8d7c14b9-dc1f-4fce-8c76-e38930f82100 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d3b50d4d-6983-4cbf-a148-e4f06e934212 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 9f8c86e3-c1f9-4e9a-8862-7a78ac1083a5 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 9edaad4e-15b2-4c7e-975d-ba5066fec3f9 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 92c1e7f2-6f47-43f6-93d3-b83ea7162bc0 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 f8f15892-4f1d-441b-a443-651832eeb325 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 4392e30f-ae88-4f1a-a911-b8cb3a5bc7d4 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 46dde15d-1157-41f5-a23e-9c6eceafc11a 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 4a7f2c13-14aa-463c-9089-f1cc84a0b517 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 b85b8a93-fe8f-41cc-baa2-c6fca67e4e64 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 278a0090-1bd6-4f80-b68b-ed20211ed3c2 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 27bc6c36-865c-447b-9429-cd1f80cfc4f5 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 11850884-6630-4876-badc-e4962ff30bf9 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 90b75349-48fd-4ab9-884a-d655909cd8e4 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 6d241944-d7fd-4272-99cd-0a4619208781 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 48000380-8467-4a53-aea9-64a33c08afdb 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 773cae95-f0dc-4053-8ba1-01c18a53c0a2 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 b16032aa-d262-4c91-bf6b-a188f41a0cfe 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 963fc4a3-20d1-4f61-ac4a-dd369c79ef82 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 98e3fd9d-1d03-48d6-acad-14b23d415c46 处理失败: Outbound handler not found: test-outbound
09:54:07.254 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8e4b9701-9ec1-4eb3-8e67-c77f1774b7da 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 bef4c225-3cc9-48f9-989b-11daca2a64dc 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 b15a46ce-dbe4-49bd-993e-c69c56c87e02 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 eb810e80-1774-4521-ac8a-106c95acbbd9 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 e92c09ea-6c16-471d-b960-53f4ec737470 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 9b62b9d5-008f-4539-9bcf-85500c5ad340 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 c97216d9-5568-427d-9295-32584100a246 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 4085560b-9d7d-426e-8325-882b8573081e 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 be46f0e7-97b4-4fea-9198-83131be1dfc1 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 ae5e078c-99f7-49d2-8851-0ae2396a8fb5 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 77af263a-d5c8-4fce-8476-458973a045d9 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 befd3f83-5b06-4bb4-947f-ec5333960991 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 2abc3b32-f7b8-4eac-afcc-3c11b3464556 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 427ac38e-8630-48d2-a6cd-830087641a55 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 052d0954-537b-408e-9f26-03d5fef99663 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 1674a51b-7e49-4740-8781-89bf86e2a269 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 71adcd90-7150-43b4-98f5-51aac577f6e8 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 b56b5285-00c7-410f-8e80-d916a407bfae 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 c536cc1a-268f-473f-a0d9-af8aa0b0130c 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 b4b95ed7-23d7-430e-818f-5ef0a630aff9 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 cfea57f6-ddba-4dfd-aed1-b64c8908c538 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 0fda4140-3a37-458c-be88-22d6153b81e8 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 3a5acb71-8272-4646-9e7e-c64eeeb98067 处理失败: Outbound handler not found: test-outbound
09:54:07.255 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 37bfce7a-079e-4369-ac8b-f1640432444c 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 354c8532-cd4c-4c12-bddc-c30d3e478481 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 4dee1cf1-d3d2-4636-9922-d86baf2d1844 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 35a4195c-5658-4f56-a4d4-a094e5ef7e97 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 814ad1f6-5ad0-4b42-9748-8f8fb14bcd32 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 da7bf68f-6d41-4024-972c-035d3c5be0ea 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 f2624efd-17d1-44c9-8448-e2ddf1248f1a 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8818a9fe-7a85-486c-88cb-374bb2d9b4c2 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 66fb88d4-49e8-4c8c-83cb-db6cb7b1546d 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 6145171c-12c0-413d-8cf9-7db4c17b6903 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 7d02fb49-5f5a-400f-8728-db95087e8436 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 55dd4a4b-a334-4aff-b1b7-f55f0b2be5c4 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 da41e5ce-8163-424d-bd04-9e944de091cc 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 7e6ffc6c-d0ca-4b7a-9128-9b4f830e85f8 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 27fe4292-f12b-4a71-b671-c0d3eee43e45 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 b0877302-8fb9-469f-839f-3395891f3b0b 处理失败: Outbound handler not found: test-outbound
09:54:07.256 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 611b1ee4-2fbb-41e6-9049-ba04c0109b04 处理失败: Outbound handler not found: test-outbound
并发测试结果: 成功=0, 失败=100
]]></system-out>
  </testcase>
  <testcase name="testQueueIndexCalculation" classname="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.003">
    <system-out><![CDATA[09:54:07.259 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
09:54:07.259 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=4, queueCapacity=1000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
09:54:07.259 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
09:54:07.259 [main] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
09:54:07.260 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
09:54:07.260 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 4
]]></system-out>
  </testcase>
  <testcase name="testDifferentConnectionsRouteToDifferentQueues" classname="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.001">
    <system-out><![CDATA[09:54:07.260 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
09:54:07.260 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=4, queueCapacity=1000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
09:54:07.260 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
09:54:07.261 [main] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
09:54:07.261 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
09:54:07.261 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 4
]]></system-out>
  </testcase>
</testsuite>