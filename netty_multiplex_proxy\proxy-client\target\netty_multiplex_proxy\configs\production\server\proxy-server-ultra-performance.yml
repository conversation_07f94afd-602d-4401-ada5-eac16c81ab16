# 超高性能代理服务器配置文件
# 适用于极高并发、超大流量的生产环境
# 建议配合高性能硬件使用 (16+ CPU cores, 32GB+ RAM)

# 服务器配置
server:
  port: 8888

# 认证配置 - 生产环境优化
auth:
  enable: true
  username: admin
  password: password11
  timeout:
    seconds: 60  # 增加超时时间，减少频繁重认证

# 连接池配置 - 超高性能优化
pool:
  enable: true
  max-connections:
    per-host: 100  # 大幅增加每主机最大连接数
  idle-timeout:
    seconds: 300   # 5分钟空闲超时，最大化连接复用
  cleanup-interval:
    seconds: 120   # 2分钟清理间隔，减少清理频率
  max-lifetime:
    seconds: 7200  # 连接最大存活时间2小时

# 性能监控配置 - 优化监控开销
metrics:
  enable: true
  report:
    interval:
      seconds: 300  # 5分钟报告一次，降低监控开销

# 黑名单配置 - 智能优化
blacklist:
  enable: true
  failure:
    threshold: 8  # 增加失败阈值，减少误判
  cache:
    timeout:
      seconds: 600  # 10分钟黑名单缓存，减少重复检查

# 超高性能线程池配置
performance:
  # Boss线程数 (高并发场景使用多个Boss线程)
  boss-threads: 4
  # 工作线程数 (0表示自动计算，推荐让系统自动计算)
  worker-threads: 0
  # I/O操作与CPU操作的比例，代理服务器I/O密集，设置最高值
  io-ratio: 90
  # 启用智能线程优化
  enable-thread-optimization: true
  # 最大工作线程数 (0表示使用默认计算值)
  max-worker-threads: 0
  # 最小工作线程数 (确保基础并发能力)
  min-worker-threads: 16

# SSL配置 (如果需要)
ssl:
  enable: false
  keystore:
    path: ""
    password: ""
    type: "JKS"
  truststore:
    path: ""
    password: ""
    type: "JKS"

# 地理位置过滤配置
geo-filter:
  enable: false
  china-only: false
  block-overseas-suspicious: false
  
# 恶意内容过滤配置  
malicious-filter:
  enable: false
  domain-filter:
    enable: false
  keyword-filter:
    enable: false

# 超高性能JVM参数建议 (在启动脚本中使用):
# 内存配置:
# -Xms8g -Xmx16g                      # 设置大内存堆
# -XX:NewRatio=1                       # 年轻代与老年代比例1:1
# -XX:MaxDirectMemorySize=8g           # 设置直接内存大小

# 垃圾收集器配置:
# -XX:+UseG1GC                         # 使用G1垃圾收集器
# -XX:MaxGCPauseMillis=100             # 限制GC暂停时间100ms
# -XX:G1HeapRegionSize=16m             # 设置G1区域大小
# -XX:+G1UseAdaptiveIHOP               # 启用自适应IHOP
# -XX:G1MixedGCCountTarget=8           # 混合GC目标次数

# 编译优化:
# -XX:+UseStringDeduplication          # 启用字符串去重
# -XX:+OptimizeStringConcat            # 优化字符串连接
# -XX:+UseFastAccessorMethods          # 使用快速访问器方法
# -XX:+AggressiveOpts                  # 启用激进优化

# Netty优化:
# -Dio.netty.allocator.type=pooled     # 使用池化内存分配器
# -Dio.netty.allocator.numDirectArenas=16  # 设置直接内存区域数
# -Dio.netty.allocator.numHeapArenas=16    # 设置堆内存区域数
# -Dio.netty.allocator.pageSize=8192   # 设置页面大小
# -Dio.netty.allocator.maxOrder=11     # 设置最大阶数
# -Dio.netty.recycler.maxCapacityPerThread=0  # 禁用对象回收器
# -Dio.netty.leakDetection.level=DISABLED     # 禁用内存泄漏检测(生产环境)

# 系统优化:
# -Djava.net.preferIPv4Stack=true      # 优先使用IPv4
# -Dsun.net.useExclusiveBind=false     # 允许端口复用
# -Djava.security.egd=file:/dev/./urandom  # 使用更快的随机数生成器

# 监控和调试 (可选):
# -XX:+PrintGC                         # 打印GC信息
# -XX:+PrintGCDetails                  # 打印详细GC信息
# -XX:+PrintGCTimeStamps               # 打印GC时间戳
# -Xloggc:gc.log                       # GC日志文件
# -XX:+UseGCLogFileRotation            # 启用GC日志轮转
# -XX:NumberOfGCLogFiles=5             # GC日志文件数量
# -XX:GCLogFileSize=100M               # GC日志文件大小

# 操作系统级别优化建议:
# 1. 增加文件描述符限制: ulimit -n 1048576
# 2. 优化TCP参数:
#    net.core.somaxconn = 65535
#    net.core.netdev_max_backlog = 5000
#    net.ipv4.tcp_max_syn_backlog = 65535
#    net.ipv4.tcp_fin_timeout = 30
#    net.ipv4.tcp_keepalive_time = 1200
#    net.ipv4.tcp_rmem = 4096 65536 16777216
#    net.ipv4.tcp_wmem = 4096 65536 16777216
# 3. 禁用swap: swapoff -a
# 4. 使用高性能网络驱动和网卡
# 5. 绑定进程到特定CPU核心: taskset

# 硬件建议:
# - CPU: 16+ 核心，高主频 (3.0GHz+)
# - 内存: 32GB+ DDR4
# - 网络: 万兆网卡
# - 存储: NVMe SSD
# - 操作系统: Linux (推荐 Ubuntu 20.04+ 或 CentOS 8+)
