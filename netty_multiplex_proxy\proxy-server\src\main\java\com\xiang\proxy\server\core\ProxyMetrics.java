package com.xiang.proxy.server.core;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 代理性能指标收集器
 * 收集和统计代理处理的各种性能指标
 */
public class ProxyMetrics {
    
    // 请求统计
    private final LongAdder totalRequests = new LongAdder();
    private final LongAdder successfulRequests = new LongAdder();
    private final LongAdder failedRequests = new LongAdder();
    private final LongAdder rejectedRequests = new LongAdder();
    
    // 延迟统计
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    private final AtomicLong minProcessingTime = new AtomicLong(Long.MAX_VALUE);
    
    // 队列统计
    private final ConcurrentHashMap<Integer, QueueMetrics> queueMetrics = new ConcurrentHashMap<>();
    
    // 连接统计
    private final LongAdder totalConnections = new LongAdder();
    private final LongAdder activeConnections = new LongAdder();
    private final LongAdder connectionErrors = new LongAdder();
    
    // 流量统计
    private final LongAdder bytesReceived = new LongAdder();
    private final LongAdder bytesSent = new LongAdder();
    
    private final long startTime = System.currentTimeMillis();

    /**
     * 记录请求开始
     */
    public RequestTimer startRequest() {
        totalRequests.increment();
        return new RequestTimer();
    }

    /**
     * 记录请求成功
     */
    public void recordSuccess(RequestTimer timer) {
        successfulRequests.increment();
        recordProcessingTime(timer.getElapsedTime());
    }

    /**
     * 记录请求失败
     */
    public void recordFailure(RequestTimer timer) {
        failedRequests.increment();
        recordProcessingTime(timer.getElapsedTime());
    }

    /**
     * 记录请求被拒绝
     */
    public void recordRejection() {
        rejectedRequests.increment();
    }

    /**
     * 记录处理时间
     */
    private void recordProcessingTime(long processingTime) {
        totalProcessingTime.addAndGet(processingTime);
        
        // 更新最大处理时间
        long currentMax = maxProcessingTime.get();
        while (processingTime > currentMax) {
            if (maxProcessingTime.compareAndSet(currentMax, processingTime)) {
                break;
            }
            currentMax = maxProcessingTime.get();
        }
        
        // 更新最小处理时间
        long currentMin = minProcessingTime.get();
        while (processingTime < currentMin) {
            if (minProcessingTime.compareAndSet(currentMin, processingTime)) {
                break;
            }
            currentMin = minProcessingTime.get();
        }
    }

    /**
     * 记录队列指标
     */
    public void recordQueueMetrics(int queueIndex, int queueSize, long waitTime) {
        queueMetrics.computeIfAbsent(queueIndex, k -> new QueueMetrics())
                .record(queueSize, waitTime);
    }

    /**
     * 记录连接指标
     */
    public void recordConnectionCreated() {
        totalConnections.increment();
        activeConnections.increment();
    }

    public void recordConnectionClosed() {
        activeConnections.decrement();
    }

    public void recordConnectionError() {
        connectionErrors.increment();
    }

    /**
     * 记录流量
     */
    public void recordBytesReceived(long bytes) {
        bytesReceived.add(bytes);
    }

    public void recordBytesSent(long bytes) {
        bytesSent.add(bytes);
    }

    /**
     * 获取性能统计报告
     */
    public MetricsReport getReport() {
        long uptime = System.currentTimeMillis() - startTime;
        long totalReqs = totalRequests.sum();
        
        double successRate = totalReqs > 0 ? (double) successfulRequests.sum() / totalReqs * 100 : 0;
        double avgProcessingTime = successfulRequests.sum() > 0 ? 
                (double) totalProcessingTime.get() / successfulRequests.sum() : 0;
        double requestsPerSecond = uptime > 0 ? (double) totalReqs / (uptime / 1000.0) : 0;
        
        return new MetricsReport(
                totalReqs,
                successfulRequests.sum(),
                failedRequests.sum(),
                rejectedRequests.sum(),
                successRate,
                avgProcessingTime,
                maxProcessingTime.get(),
                minProcessingTime.get() == Long.MAX_VALUE ? 0 : minProcessingTime.get(),
                requestsPerSecond,
                totalConnections.sum(),
                activeConnections.sum(),
                connectionErrors.sum(),
                bytesReceived.sum(),
                bytesSent.sum(),
                uptime
        );
    }

    /**
     * 重置统计数据
     */
    public void reset() {
        totalRequests.reset();
        successfulRequests.reset();
        failedRequests.reset();
        rejectedRequests.reset();
        totalProcessingTime.set(0);
        maxProcessingTime.set(0);
        minProcessingTime.set(Long.MAX_VALUE);
        queueMetrics.clear();
        totalConnections.reset();
        activeConnections.reset();
        connectionErrors.reset();
        bytesReceived.reset();
        bytesSent.reset();
    }

    /**
     * 请求计时器
     */
    public static class RequestTimer {
        private final long startTime = System.nanoTime();

        public long getElapsedTime() {
            return (System.nanoTime() - startTime) / 1_000_000; // 转换为毫秒
        }
    }

    /**
     * 队列指标
     */
    private static class QueueMetrics {
        private final LongAdder totalRequests = new LongAdder();
        private final AtomicLong totalWaitTime = new AtomicLong(0);
        private final AtomicLong maxQueueSize = new AtomicLong(0);

        public void record(int queueSize, long waitTime) {
            totalRequests.increment();
            totalWaitTime.addAndGet(waitTime);
            
            long currentMax = maxQueueSize.get();
            while (queueSize > currentMax) {
                if (maxQueueSize.compareAndSet(currentMax, queueSize)) {
                    break;
                }
                currentMax = maxQueueSize.get();
            }
        }

        public double getAverageWaitTime() {
            long total = totalRequests.sum();
            return total > 0 ? (double) totalWaitTime.get() / total : 0;
        }

        public long getMaxQueueSize() {
            return maxQueueSize.get();
        }
    }

    /**
     * 指标报告
     */
    public static class MetricsReport {
        private final long totalRequests;
        private final long successfulRequests;
        private final long failedRequests;
        private final long rejectedRequests;
        private final double successRate;
        private final double avgProcessingTime;
        private final long maxProcessingTime;
        private final long minProcessingTime;
        private final double requestsPerSecond;
        private final long totalConnections;
        private final long activeConnections;
        private final long connectionErrors;
        private final long bytesReceived;
        private final long bytesSent;
        private final long uptime;

        public MetricsReport(long totalRequests, long successfulRequests, long failedRequests,
                           long rejectedRequests, double successRate, double avgProcessingTime,
                           long maxProcessingTime, long minProcessingTime, double requestsPerSecond,
                           long totalConnections, long activeConnections, long connectionErrors,
                           long bytesReceived, long bytesSent, long uptime) {
            this.totalRequests = totalRequests;
            this.successfulRequests = successfulRequests;
            this.failedRequests = failedRequests;
            this.rejectedRequests = rejectedRequests;
            this.successRate = successRate;
            this.avgProcessingTime = avgProcessingTime;
            this.maxProcessingTime = maxProcessingTime;
            this.minProcessingTime = minProcessingTime;
            this.requestsPerSecond = requestsPerSecond;
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
            this.connectionErrors = connectionErrors;
            this.bytesReceived = bytesReceived;
            this.bytesSent = bytesSent;
            this.uptime = uptime;
        }

        // Getters
        public long getTotalRequests() { return totalRequests; }
        public long getSuccessfulRequests() { return successfulRequests; }
        public long getFailedRequests() { return failedRequests; }
        public long getRejectedRequests() { return rejectedRequests; }
        public double getSuccessRate() { return successRate; }
        public double getAvgProcessingTime() { return avgProcessingTime; }
        public long getMaxProcessingTime() { return maxProcessingTime; }
        public long getMinProcessingTime() { return minProcessingTime; }
        public double getRequestsPerSecond() { return requestsPerSecond; }
        public long getTotalConnections() { return totalConnections; }
        public long getActiveConnections() { return activeConnections; }
        public long getConnectionErrors() { return connectionErrors; }
        public long getBytesReceived() { return bytesReceived; }
        public long getBytesSent() { return bytesSent; }
        public long getUptime() { return uptime; }

        @Override
        public String toString() {
            return String.format(
                "MetricsReport{\n" +
                "  总请求数: %d\n" +
                "  成功请求: %d\n" +
                "  失败请求: %d\n" +
                "  拒绝请求: %d\n" +
                "  成功率: %.2f%%\n" +
                "  平均处理时间: %.2fms\n" +
                "  最大处理时间: %dms\n" +
                "  最小处理时间: %dms\n" +
                "  每秒请求数: %.2f\n" +
                "  总连接数: %d\n" +
                "  活跃连接: %d\n" +
                "  连接错误: %d\n" +
                "  接收字节: %d\n" +
                "  发送字节: %d\n" +
                "  运行时间: %dms\n" +
                "}",
                totalRequests, successfulRequests, failedRequests, rejectedRequests,
                successRate, avgProcessingTime, maxProcessingTime, minProcessingTime,
                requestsPerSecond, totalConnections, activeConnections, connectionErrors,
                bytesReceived, bytesSent, uptime
            );
        }
    }
}