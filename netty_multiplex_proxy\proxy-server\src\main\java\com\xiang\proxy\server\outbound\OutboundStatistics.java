package com.xiang.proxy.server.outbound;

/**
 * Outbound统计信息
 */
public class OutboundStatistics {
    private final long totalConnections;
    private final long successfulConnections;
    private final long failedConnections;
    private final long activeConnections;
    private final long timestamp;

    public OutboundStatistics() {
        this(0, 0, 0, 0);
    }

    public OutboundStatistics(long totalConnections, long successfulConnections, 
                            long failedConnections, long activeConnections) {
        this.totalConnections = totalConnections;
        this.successfulConnections = successfulConnections;
        this.failedConnections = failedConnections;
        this.activeConnections = activeConnections;
        this.timestamp = System.currentTimeMillis();
    }

    public long getTotalConnections() {
        return totalConnections;
    }

    public long getSuccessfulConnections() {
        return successfulConnections;
    }

    public long getFailedConnections() {
        return failedConnections;
    }

    public long getActiveConnections() {
        return activeConnections;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public double getSuccessRate() {
        return totalConnections > 0 ? (double) successfulConnections / totalConnections : 0.0;
    }

    public double getFailureRate() {
        return totalConnections > 0 ? (double) failedConnections / totalConnections : 0.0;
    }

    @Override
    public String toString() {
        return String.format("OutboundStatistics{total=%d, success=%d, failed=%d, active=%d, successRate=%.2f%%}",
                totalConnections, successfulConnections, failedConnections, activeConnections, 
                getSuccessRate() * 100);
    }
}