<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="auth-service" />
        <module name="common" />
        <module name="proxy-server" />
        <module name="netty-multiplex-proxy-service" />
        <module name="springcloud-alibaba-mybaits" />
        <module name="gateway" />
        <module name="proxy-client" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="proxy-client-nacos" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="auth-service" options="-parameters" />
      <module name="common" options="-parameters" />
      <module name="gateway" options="-parameters" />
      <module name="netty-multiplex-proxy-service" options="-parameters" />
      <module name="netty_multiplex_proxy_alicloud" options="-parameters" />
      <module name="springcloud-alibaba-mybaits" options="-parameters" />
    </option>
  </component>
</project>