# 高性能代理服务器配置文件
# 适用于高并发、大流量的生产环境

# 服务器配置
server:
  port: 8888

# 认证配置
auth:
  enable: true
  username: admin
  password: password11
  timeout:
    seconds: 30

# 连接池配置 - 高性能优化
pool:
  enable: true
  max-connections:
    per-host: 50  # 增加每主机最大连接数
  idle-timeout:
    seconds: 120  # 增加空闲超时时间，减少连接重建
  cleanup-interval:
    seconds: 60   # 增加清理间隔，减少清理频率
  max-lifetime:
    seconds: 3600  # 连接最大存活时间1小时

# 性能监控配置
metrics:
  enable: true
  report:
    interval:
      seconds: 180  # 减少报告频率，降低监控开销

# 黑名单配置
blacklist:
  enable: true
  failure:
    threshold: 5  # 增加失败阈值，减少误判
  cache:
    timeout:
      seconds: 300  # 增加黑名单缓存时间

# 高性能线程池配置
performance:
  # Boss线程数 (高并发场景可以使用2个)
  boss-threads: 2
  # 工作线程数 (0表示自动计算，推荐让系统自动计算)
  worker-threads: 0
  # I/O操作与CPU操作的比例，代理服务器I/O密集，设置较高值
  io-ratio: 80
  # 启用智能线程优化
  enable-thread-optimization: true
  # 最大工作线程数 (设置为CPU核心数的6倍，平衡性能和资源)
  max-worker-threads: 0  # 0表示使用默认计算值(CPU核心数*8)
  # 最小工作线程数 (确保基础并发能力)
  min-worker-threads: 8


# 高性能JVM参数建议 (在启动脚本中使用):
# -Xms2g -Xmx4g                    # 设置合适的堆内存
# -XX:+UseG1GC                     # 使用G1垃圾收集器
# -XX:MaxGCPauseMillis=200         # 限制GC暂停时间
# -XX:+UseStringDeduplication      # 启用字符串去重
# -XX:+OptimizeStringConcat        # 优化字符串连接
# -Dio.netty.allocator.type=pooled # 使用池化内存分配器
# -Dio.netty.recycler.maxCapacityPerThread=0  # 禁用对象回收器以减少内存占用
