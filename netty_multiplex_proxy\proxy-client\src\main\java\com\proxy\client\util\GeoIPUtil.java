package com.proxy.client.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * IP地理位置判断工具
 * 用于判断IP地址是否属于中国地区
 */
public class GeoIPUtil {
    private static final Logger logger = LoggerFactory.getLogger(GeoIPUtil.class);

    // 中国IP段配置文件名
    private static final String CHINA_IP_RANGES_FILE = "china-ip-ranges.txt";

    // 中国大陆主要IP段（从最新APNIC数据加载）
    private static final Set<String> CHINA_IP_RANGES = new HashSet<>();

    // 在线数据源URL - 从配置文件加载
    private static java.util.List<String> getOnlineDataSources() {
        try {
            com.proxy.client.config.ProxyClientConfigManager configManager =
                com.proxy.client.config.ProxyClientConfigManager.getInstance();
            return configManager.getChinaIpRangesUrls();
        } catch (Exception e) {
            // 如果配置加载失败，使用默认值
            return java.util.Arrays.asList(
                "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt",
                "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
            );
        }
    }

    // 线程池用于异步更新IP段数据
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "GeoIPUtil-Client-Scheduler");
        t.setDaemon(true);
        return t;
    });

    static {
        // 初始化中国IP段
        initializeChinaIPRanges();
    }

    /**
     * 初始化中国IP段
     * 优先级：china-ip-ranges.txt文件 -> 在线数据源（成功后写入文件） -> 内置基础IP段
     */
    private static void initializeChinaIPRanges() {
        // 首先尝试从china-ip-ranges.txt文件加载
        if (loadChinaIPRangesFromSpecificFile(CHINA_IP_RANGES_FILE)) {
            logger.info("成功从{}加载 {} 个中国IP段", CHINA_IP_RANGES_FILE, CHINA_IP_RANGES.size());
            return;
        }

        // 如果文件不存在，尝试从在线数据源加载
        if (loadChinaIPRangesFromOnline()) {
            logger.info("成功从在线数据源加载 {} 个中国IP段", CHINA_IP_RANGES.size());
            // 网络加载成功后，保存到china-ip-ranges.txt文件
            saveChinaIPRangesToFile();
            return;
        }

        // 如果都失败，使用内置的基础IP段
        loadBuiltinChinaIPRanges();
        logger.info("使用内置IP段，已加载 {} 个中国IP段", CHINA_IP_RANGES.size());
    }

    /**
     * 从指定的配置文件加载中国IP段
     * @param fileName 配置文件名
     * @return 是否加载成功
     */
    private static boolean loadChinaIPRangesFromSpecificFile(String fileName) {
        // 首先尝试从文件系统加载
        if (loadIPRangesFromFileSystem(fileName)) {
            logger.info("成功从文件系统加载中国IP段: {}", fileName);
            return true;
        }

        // 如果文件系统中没有，尝试从资源文件加载
        if (loadIPRangesFromResource(fileName)) {
            logger.info("成功从资源文件加载中国IP段: {}", fileName);
            return true;
        }

        logger.debug("未找到配置文件: {}", fileName);
        return false;
    }

    /**
     * 从文件系统加载IP段
     * @param fileName 文件名
     * @return 是否加载成功
     */
    private static boolean loadIPRangesFromFileSystem(String fileName) {
        // 1. 首先尝试从配置目录加载
        if (loadFromConfigDirectory(fileName)) {
            return true;
        }

        // 2. 然后尝试从当前目录加载
        File file = new File(fileName);
        if (!file.exists() || !file.isFile()) {
            return false;
        }

        try (BufferedReader reader = new BufferedReader(new FileReader(file, StandardCharsets.UTF_8))) {
            String line;
            int loadedCount = 0;

            while ((line = reader.readLine()) != null) {
                line = line.trim();

                // 跳过空行和注释行
                if (line.isEmpty() || line.startsWith("#") || line.startsWith("//")) {
                    continue;
                }

                // 验证并添加IP段
                if (isValidIPRange(line)) {
                    CHINA_IP_RANGES.add(line);
                    loadedCount++;
                } else {
                    logger.debug("跳过无效的IP段: {}", line);
                }
            }

            logger.info("从文件系统 {} 加载了 {} 个IP段", fileName, loadedCount);
            return loadedCount > 0;

        } catch (IOException e) {
            logger.warn("读取文件系统文件失败: {}", fileName, e);
            return false;
        }
    }

    /**
     * 从配置目录加载IP段文件
     */
    private static boolean loadFromConfigDirectory(String filename) {
        try {
            // 获取配置目录路径
            String configDir = getConfigDirectory();
            if (configDir == null || configDir.trim().isEmpty()) {
                return false;
            }

            File configFile = new File(configDir, filename);
            if (!configFile.exists() || !configFile.isFile()) {
                logger.debug("配置目录中未找到文件: {}", configFile.getAbsolutePath());
                return false;
            }

            try (BufferedReader reader = new BufferedReader(new FileReader(configFile, StandardCharsets.UTF_8))) {
                String line;
                int loadedCount = 0;

                while ((line = reader.readLine()) != null) {
                    line = line.trim();

                    // 跳过空行和注释行
                    if (line.isEmpty() || line.startsWith("#") || line.startsWith("//")) {
                        continue;
                    }

                    // 验证并添加IP段
                    if (isValidIPRange(line)) {
                        CHINA_IP_RANGES.add(line);
                        loadedCount++;
                    } else {
                        logger.debug("跳过无效的IP段: {}", line);
                    }
                }

                if (loadedCount > 0) {
                    logger.info("成功从配置目录加载IP段文件: {} ({} 个IP段)", configFile.getAbsolutePath(), loadedCount);
                    return true;
                }
            }
        } catch (Exception e) {
            logger.warn("从配置目录加载IP段文件失败: {}", filename, e);
        }
        return false;
    }

    /**
     * 获取配置目录路径
     */
    private static String getConfigDirectory() {
        try {
            // 从 app.properties 读取配置目录
            Properties props = new Properties();
            try (InputStream is = GeoIPUtil.class.getClassLoader().getResourceAsStream("app.properties")) {
                if (is != null) {
                    props.load(is);
                    String configDir = props.getProperty("config.ext.dir");
                    if (configDir != null && !configDir.trim().isEmpty()) {
                        return configDir.trim();
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("获取配置目录失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从资源文件加载IP段
     */
    private static boolean loadIPRangesFromResource(String resourceName) {
        try (InputStream is = GeoIPUtil.class.getClassLoader().getResourceAsStream(resourceName)) {
            if (is == null) {
                logger.debug("配置文件不存在: {}", resourceName);
                return false;
            }

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, "UTF-8"))) {
                String line;
                int loadedCount = 0;

                while ((line = reader.readLine()) != null) {
                    line = line.trim();

                    // 跳过空行和注释行
                    if (line.isEmpty() || line.startsWith("#") || line.startsWith("//")) {
                        continue;
                    }

                    // 支持多种格式：
                    // 1. CIDR格式：***********/24
                    // 2. 范围格式：***********-*************
                    // 3. 单个IP：***********
                    if (isValidIPRange(line)) {
                        CHINA_IP_RANGES.add(line);
                        loadedCount++;
                    } else {
                        logger.debug("跳过无效的IP段: {}", line);
                    }
                }

                logger.info("从 {} 加载了 {} 个IP段", resourceName, loadedCount);
                return loadedCount > 0;

            }
        } catch (IOException e) {
            logger.warn("读取配置文件失败: {}", resourceName, e);
            return false;
        }
    }

    /**
     * 验证IP段格式是否有效
     */
    private static boolean isValidIPRange(String ipRange) {
        if (ipRange == null || ipRange.trim().isEmpty()) {
            return false;
        }

        // CIDR格式验证
        if (ipRange.contains("/")) {
            String[] parts = ipRange.split("/");
            if (parts.length == 2) {
                try {
                    InetAddress.getByName(parts[0]);
                    int prefix = Integer.parseInt(parts[1]);
                    return prefix >= 0 && prefix <= 32;
                } catch (Exception e) {
                    return false;
                }
            }
        }

        // 范围格式验证（暂不实现，可以后续扩展）
        if (ipRange.contains("-")) {
            // TODO: 实现IP范围格式验证
            return false;
        }

        // 单个IP验证
        try {
            InetAddress.getByName(ipRange);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从在线数据源加载中国IP段
     * 支持从GitHub等在线源获取最新数据
     */
    private static boolean loadChinaIPRangesFromOnline() {
        java.util.List<String> onlineDataSources = getOnlineDataSources();
        for (String dataSource : onlineDataSources) {
            try {
                logger.info("尝试从在线数据源加载: {}", dataSource);

                URL url = new URL(dataSource);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000); // 10秒连接超时
                connection.setReadTimeout(30000);    // 30秒读取超时
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Java GeoIP Client)");

                int responseCode = connection.getResponseCode();
                if (responseCode == 200) {
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(connection.getInputStream(), "UTF-8"))) {

                        String line;
                        int loadedCount = 0;

                        while ((line = reader.readLine()) != null) {
                            line = line.trim();

                            // 跳过空行和注释行
                            if (line.isEmpty() || line.startsWith("#") || line.startsWith("//")) {
                                continue;
                            }

                            // 验证并添加IP段
                            if (isValidIPRange(line)) {
                                CHINA_IP_RANGES.add(line);
                                loadedCount++;
                            }
                        }

                        if (loadedCount > 0) {
                            logger.info("从在线数据源 {} 成功加载 {} 个IP段", dataSource, loadedCount);
                            return true;
                        }
                    }
                } else {
                    logger.warn("在线数据源返回错误状态码: {} - {}", responseCode, dataSource);
                }

            } catch (Exception e) {
                logger.warn("从在线数据源加载失败: {} - {}", dataSource, e.getMessage());
            }
        }

        logger.warn("所有在线数据源都加载失败");
        return false;
    }

    /**
     * 将中国IP段数据保存到china-ip-ranges.txt文件
     */
    private static void saveChinaIPRangesToFile() {
        // 优先保存到配置目录
        if (saveToConfigDirectory()) {
            return;
        }

        // 如果配置目录保存失败，保存到当前目录
        PrintWriter writer = null;
        try {
            writer = new PrintWriter(new FileWriter(CHINA_IP_RANGES_FILE, StandardCharsets.UTF_8));
            writer.println("# 中国IP段数据");
            writer.println("# 自动生成于: " + new java.util.Date());
            writer.println("# 总计: " + CHINA_IP_RANGES.size() + " 个IP段");
            writer.println();

            for (String ipRange : CHINA_IP_RANGES) {
                writer.println(ipRange);
            }

            // 确保数据立即写入磁盘
            writer.flush();

            logger.info("成功将 {} 个IP段保存到当前目录文件: {}", CHINA_IP_RANGES.size(), CHINA_IP_RANGES_FILE);

        } catch (IOException e) {
            logger.warn("保存IP段数据到文件失败: {}", CHINA_IP_RANGES_FILE, e);
        } finally {
            // 确保文件被正确关闭
            if (writer != null) {
                writer.close();
            }
        }
    }

    /**
     * 保存IP段数据到配置目录
     */
    private static boolean saveToConfigDirectory() {
        try {
            String configDir = getConfigDirectory();
            if (configDir == null || configDir.trim().isEmpty()) {
                return false;
            }

            File configDirFile = new File(configDir);
            if (!configDirFile.exists()) {
                if (!configDirFile.mkdirs()) {
                    logger.warn("无法创建配置目录: {}", configDir);
                    return false;
                }
            }

            File configFile = new File(configDirFile, CHINA_IP_RANGES_FILE);
            PrintWriter writer = null;
            try {
                writer = new PrintWriter(new FileWriter(configFile, StandardCharsets.UTF_8));
                writer.println("# 中国IP段数据");
                writer.println("# 自动生成于: " + new java.util.Date());
                writer.println("# 总计: " + CHINA_IP_RANGES.size() + " 个IP段");
                writer.println();

                for (String ipRange : CHINA_IP_RANGES) {
                    writer.println(ipRange);
                }

                // 确保数据立即写入磁盘
                writer.flush();

                logger.info("成功将 {} 个IP段保存到配置目录: {} ({} 个IP段)",
                           CHINA_IP_RANGES.size(), configFile.getAbsolutePath(), CHINA_IP_RANGES.size());
                return true;

            } finally {
                if (writer != null) {
                    writer.close();
                }
            }
        } catch (Exception e) {
            logger.warn("保存中国IP段数据到配置目录失败", e);
            return false;
        }
    }

    /**
     * 从在线数据源更新中国IP段数据
     */
    private static void updateChinaIPRangesFromOnline() {
        try {
            logger.info("开始从在线数据源更新中国IP段数据");

            // 临时保存当前数据
            Set<String> oldRanges = new HashSet<>(CHINA_IP_RANGES);
            int oldSize = CHINA_IP_RANGES.size();

            // 清空当前数据
            CHINA_IP_RANGES.clear();

            // 尝试从在线源加载新数据
            if (loadChinaIPRangesFromOnline()) {
                // 更新成功，保存到文件
                saveChinaIPRangesToFile();
                logger.info("中国IP段数据更新成功: {} -> {} 个IP段", oldSize, CHINA_IP_RANGES.size());
            } else {
                // 更新失败，恢复旧数据
                CHINA_IP_RANGES.addAll(oldRanges);
                logger.warn("中国IP段数据更新失败，继续使用旧数据 ({} 个IP段)", oldSize);
            }
        } catch (Exception e) {
            logger.error("从在线数据源更新中国IP段数据时发生异常", e);
        }
    }

    /**
     * 手动更新中国IP段数据
     * 可以在运行时调用此方法来更新IP段数据
     *
     * @return 更新是否成功
     */
    public static boolean updateChinaIPRanges() {
        logger.info("开始手动更新中国IP段数据...");

        // 清空现有数据
        int oldSize = CHINA_IP_RANGES.size();
        CHINA_IP_RANGES.clear();

        // 重新初始化
        initializeChinaIPRanges();

        int newSize = CHINA_IP_RANGES.size();
        logger.info("IP段数据更新完成: {} -> {} 个IP段", oldSize, newSize);

        return newSize > 0;
    }

    /**
     * 获取当前加载的IP段数量
     *
     * @return IP段数量
     */
    public static int getLoadedIPRangeCount() {
        return CHINA_IP_RANGES.size();
    }

    /**
     * 加载内置的中国IP段（基础版本）
     * 这些是一些主要的中国IP段，用作备用方案
     */
    private static void loadBuiltinChinaIPRanges() {
        // 中国电信主要IP段
        CHINA_IP_RANGES.add("**********/11");
        CHINA_IP_RANGES.add("***********/12");
        CHINA_IP_RANGES.add("**********/14");
        CHINA_IP_RANGES.add("**********/12");
        CHINA_IP_RANGES.add("**********/11");
        CHINA_IP_RANGES.add("**********/10");
        CHINA_IP_RANGES.add("***********/13");
        CHINA_IP_RANGES.add("***********/12");
        CHINA_IP_RANGES.add("***********/11");

        // 中国联通主要IP段
        CHINA_IP_RANGES.add("*********/11");
        CHINA_IP_RANGES.add("**********/11");
        CHINA_IP_RANGES.add("**********/10");
        CHINA_IP_RANGES.add("***********/9");
        CHINA_IP_RANGES.add("**********/11");
        CHINA_IP_RANGES.add("***********/9");
        CHINA_IP_RANGES.add("*********/11");
        CHINA_IP_RANGES.add("**********/11");
        CHINA_IP_RANGES.add("**********/10");
        CHINA_IP_RANGES.add("***********/9");

        // 中国移动主要IP段
        CHINA_IP_RANGES.add("*********/11");
        CHINA_IP_RANGES.add("**********/11");
        CHINA_IP_RANGES.add("**********/10");
        CHINA_IP_RANGES.add("***********/9");

        // 其他主要运营商IP段
        CHINA_IP_RANGES.add("*******/8");
        CHINA_IP_RANGES.add("*******/8");    // 添加8.x.x.x段，包含***********
        CHINA_IP_RANGES.add("********/8");
        CHINA_IP_RANGES.add("********/8");
        CHINA_IP_RANGES.add("********/8");
        CHINA_IP_RANGES.add("********/8");
        CHINA_IP_RANGES.add("********/8");
        CHINA_IP_RANGES.add("********/8");
        CHINA_IP_RANGES.add("********/8");
        CHINA_IP_RANGES.add("********/8");
        CHINA_IP_RANGES.add("60.0.0.0/8");
        CHINA_IP_RANGES.add("********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("110.0.0.0/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("120.0.0.0/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
        CHINA_IP_RANGES.add("*********/8");
    }

    /**
     * 判断IP地址是否属于中国
     *
     * @param ip IP地址字符串
     * @return true如果是中国IP，false否则
     */
    public boolean isChinaIP(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }

        try {
            // 检查是否为私有地址
            InetAddress address = InetAddress.getByName(ip);
            if (address.isSiteLocalAddress() || address.isLoopbackAddress()) {
                logger.debug("IP {} 是私有地址，视为中国IP", ip);
                return true;
            }

            // 检查IPv4地址
            if (address.getAddress().length == 4) {
                return isChinaIPv4(ip);
            }

            // IPv6地址暂时返回false（可以后续扩展）
            logger.debug("IPv6地址 {} 暂不支持地理位置判断", ip);
            return false;

        } catch (UnknownHostException e) {
            logger.warn("无效的IP地址: {}", ip);
            return false;
        }
    }

    /**
     * 判断IPv4地址是否属于中国
     */
    private boolean isChinaIPv4(String ip) {
        for (String range : CHINA_IP_RANGES) {
            if (isIPInRange(ip, range)) {
                logger.debug("IP {} 匹配中国IP段: {}", ip, range);
                return true;
            }
        }
        return false;
    }

    /**
     * 检查IP是否在指定的CIDR范围内
     */
    private boolean isIPInRange(String ip, String cidr) {
        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return false;
            }

            InetAddress targetAddr = InetAddress.getByName(ip);
            InetAddress rangeAddr = InetAddress.getByName(parts[0]);
            int prefixLength = Integer.parseInt(parts[1]);

            byte[] targetBytes = targetAddr.getAddress();
            byte[] rangeBytes = rangeAddr.getAddress();

            if (targetBytes.length != rangeBytes.length) {
                return false;
            }

            int bytesToCheck = prefixLength / 8;
            int bitsToCheck = prefixLength % 8;

            // 检查完整字节
            for (int i = 0; i < bytesToCheck; i++) {
                if (targetBytes[i] != rangeBytes[i]) {
                    return false;
                }
            }

            // 检查剩余位
            if (bitsToCheck > 0 && bytesToCheck < targetBytes.length) {
                int mask = 0xFF << (8 - bitsToCheck);
                return (targetBytes[bytesToCheck] & mask) == (rangeBytes[bytesToCheck] & mask);
            }

            return true;

        } catch (Exception e) {
            logger.debug("检查IP范围时发生异常: {} in {}", ip, cidr, e);
            return false;
        }
    }
}
