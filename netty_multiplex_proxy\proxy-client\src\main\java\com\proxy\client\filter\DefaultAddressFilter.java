package com.proxy.client.filter;

import com.proxy.client.util.GeoIPUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 默认地址过滤器实现
 * 支持三种过滤模式的地址过滤逻辑
 */
public class DefaultAddressFilter implements AddressFilter {
    private static final Logger logger = LoggerFactory.getLogger(DefaultAddressFilter.class);
    
    private volatile FilterMode filterMode = FilterMode.ALL_PROXY;
    private final GeoIPUtil geoIPUtil;
    
    public DefaultAddressFilter() {
        this.geoIPUtil = new GeoIPUtil();
    }
    
    public DefaultAddressFilter(FilterMode filterMode) {
        this();
        this.filterMode = filterMode;
    }
    
    @Override
    public ConnectionType shouldUseProxy(String targetHost, int targetPort) {
        switch (filterMode) {
            case ALL_PROXY:
                logger.debug("过滤模式: ALL_PROXY, 目标: {}:{} -> PROXY", targetHost, targetPort);
                return ConnectionType.PROXY;
                
            case ALL_DIRECT:
                logger.debug("过滤模式: ALL_DIRECT, 目标: {}:{} -> DIRECT", targetHost, targetPort);
                return ConnectionType.DIRECT;
                
            case CHINA_DIRECT:
                return shouldUseProxyForChinaMode(targetHost, targetPort);
                
            default:
                logger.warn("未知的过滤模式: {}, 默认使用代理", filterMode);
                return ConnectionType.PROXY;
        }
    }
    
    /**
     * 中国地区直连模式的判断逻辑
     */
    private ConnectionType shouldUseProxyForChinaMode(String targetHost, int targetPort) {
        try {
            String ip;

            // 检查targetHost是否已经是IP地址格式
            if (isIPAddress(targetHost)) {
                ip = targetHost;
                logger.debug("目标主机已是IP地址: {}", ip);
            } else {
                // 解析域名为IP地址
                InetAddress address = InetAddress.getByName(targetHost);
                ip = address.getHostAddress();
                logger.debug("域名 {} 解析为IP地址: {}", targetHost, ip);
            }

            // 判断是否为中国IP
            boolean isChinaIP = geoIPUtil.isChinaIP(ip);

            ConnectionType result = isChinaIP ? ConnectionType.DIRECT : ConnectionType.PROXY;
            logger.debug("过滤模式: CHINA_DIRECT, 目标: {}:{} (IP: {}), 中国IP: {} -> {}",
                        targetHost, targetPort, ip, isChinaIP, result);

            return result;

        } catch (UnknownHostException e) {
            logger.warn("无法解析主机名: {}, 默认使用代理", targetHost, e);
            return ConnectionType.PROXY;
        } catch (Exception e) {
            logger.error("判断地理位置时发生异常: {}, 默认使用代理", targetHost, e);
            return ConnectionType.PROXY;
        }
    }
    
    @Override
    public FilterMode getFilterMode() {
        return filterMode;
    }
    
    @Override
    public void setFilterMode(FilterMode mode) {
        if (mode != null) {
            FilterMode oldMode = this.filterMode;
            this.filterMode = mode;
            logger.info("地址过滤模式已更改: {} -> {}", oldMode, mode);
        }
    }
    
    /**
     * 检查字符串是否为IP地址格式
     * 支持IPv4和IPv6地址
     */
    private boolean isIPAddress(String host) {
        if (host == null || host.trim().isEmpty()) {
            return false;
        }

        try {
            InetAddress address = InetAddress.getByName(host);
            // 如果解析成功且解析后的地址字符串与原字符串相同（或标准化后相同），则认为是IP地址
            String normalizedHost = address.getHostAddress();

            // 对于IPv4地址，直接比较
            if (address.getAddress().length == 4) {
                return host.equals(normalizedHost);
            }

            // 对于IPv6地址，需要考虑不同的表示形式
            if (address.getAddress().length == 16) {
                return host.contains(":"); // IPv6地址包含冒号
            }

            return false;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 检查IP地址是否为私有地址
     * 私有地址通常应该直连
     */
    private boolean isPrivateIP(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address.isSiteLocalAddress() || address.isLoopbackAddress();
        } catch (UnknownHostException e) {
            return false;
        }
    }
}
