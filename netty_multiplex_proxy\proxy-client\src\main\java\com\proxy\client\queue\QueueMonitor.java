package com.proxy.client.queue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 队列监控器
 * 监控PacketQueue的运行状态和性能指标
 */
public class QueueMonitor {
    private static final Logger logger = LoggerFactory.getLogger(QueueMonitor.class);
    
    private static volatile QueueMonitor instance;
    
    private final ScheduledExecutorService scheduler;
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    // 监控配置
    private final int reportIntervalSeconds;
    private final int warningThreshold;
    private final int errorThreshold;
    
    // 被监控的队列
    private volatile QueuedConnectionManager queuedConnectionManager;
    
    private QueueMonitor() {
        this(30, 80, 95); // 默认配置
    }
    
    private QueueMonitor(int reportIntervalSeconds, int warningThreshold, int errorThreshold) {
        this.reportIntervalSeconds = reportIntervalSeconds;
        this.warningThreshold = warningThreshold;
        this.errorThreshold = errorThreshold;
        
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "QueueMonitor");
            t.setDaemon(true);
            return t;
        });
        
        logger.info("QueueMonitor初始化完成 - 报告间隔: {}秒, 警告阈值: {}%, 错误阈值: {}%",
                   reportIntervalSeconds, warningThreshold, errorThreshold);
    }
    
    /**
     * 获取监控器实例
     */
    public static QueueMonitor getInstance() {
        if (instance == null) {
            synchronized (QueueMonitor.class) {
                if (instance == null) {
                    instance = new QueueMonitor();
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取监控器实例（自定义配置）
     */
    public static QueueMonitor getInstance(int reportIntervalSeconds, int warningThreshold, int errorThreshold) {
        if (instance == null) {
            synchronized (QueueMonitor.class) {
                if (instance == null) {
                    instance = new QueueMonitor(reportIntervalSeconds, warningThreshold, errorThreshold);
                }
            }
        }
        return instance;
    }
    
    /**
     * 设置被监控的队列化连接管理器
     */
    public void setQueuedConnectionManager(QueuedConnectionManager queuedConnectionManager) {
        this.queuedConnectionManager = queuedConnectionManager;
        logger.debug("设置监控目标: QueuedConnectionManager");
    }
    
    /**
     * 启动监控
     */
    public void start() {
        if (running.compareAndSet(false, true)) {
            logger.info("启动队列监控器");
            
            // 启动定期监控任务
            scheduler.scheduleWithFixedDelay(this::generateReport, 
                reportIntervalSeconds, reportIntervalSeconds, TimeUnit.SECONDS);
        }
    }
    
    /**
     * 停止监控
     */
    public void stop() {
        if (running.compareAndSet(true, false)) {
            logger.info("停止队列监控器");
            
            // 生成最终报告
            generateReport();
            
            // 关闭调度器
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                scheduler.shutdownNow();
            }
        }
    }
    
    /**
     * 生成监控报告
     */
    private void generateReport() {
        if (!running.get() || queuedConnectionManager == null) {
            return;
        }
        
        try {
            PacketQueue.QueueStats stats = queuedConnectionManager.getQueueStats();
            
            // 计算队列使用率
            int queueCapacity = getQueueCapacity(); // 需要从配置中获取
            double usagePercentage = queueCapacity > 0 ? 
                (double) stats.getQueueSize() / queueCapacity * 100 : 0;
            
            // 计算处理效率
            long totalProcessed = stats.getProcessedCount() + stats.getDroppedCount();
            double successRate = totalProcessed > 0 ? 
                (double) stats.getProcessedCount() / totalProcessed * 100 : 100;
            
            // 生成报告
            logger.info("=== 队列监控报告 ===");
            logger.info("队列状态: {}", stats.isRunning() ? "运行中" : "已停止");
            logger.info("队列大小: {} / {} ({}%)",
                       stats.getQueueSize(), queueCapacity, String.format("%.2f", usagePercentage));
            logger.info("入队总数: {}", stats.getEnqueuedCount());
            logger.info("处理总数: {}", stats.getProcessedCount());
            logger.info("丢弃总数: {}", stats.getDroppedCount());
            logger.info("成功率: {}%", String.format("%.2f", successRate));
            logger.info("==================");
            
            // 检查告警条件
            checkAlerts(usagePercentage, successRate, stats);
            
        } catch (Exception e) {
            logger.error("生成队列监控报告时发生异常", e);
        }
    }
    
    /**
     * 检查告警条件
     */
    private void checkAlerts(double usagePercentage, double successRate, PacketQueue.QueueStats stats) {
        // 队列使用率告警
        if (usagePercentage >= errorThreshold) {
            logger.error("🚨 队列使用率过高: {:.1f}% (错误阈值: {}%)", usagePercentage, errorThreshold);
        } else if (usagePercentage >= warningThreshold) {
            logger.warn("⚠️ 队列使用率较高: {:.1f}% (警告阈值: {}%)", usagePercentage, warningThreshold);
        }
        
        // 成功率告警
        if (successRate < 95.0) {
            logger.warn("⚠️ 队列处理成功率较低: {:.2f}%", successRate);
        }
        
        // 丢弃数据包告警
        if (stats.getDroppedCount() > 0) {
            long totalProcessed = stats.getProcessedCount() + stats.getDroppedCount();
            double dropRate = (double) stats.getDroppedCount() / totalProcessed * 100;
            if (dropRate > 1.0) {
                logger.warn("⚠️ 数据包丢弃率: {:.2f}% (丢弃: {})", dropRate, stats.getDroppedCount());
            }
        }
        
        // 队列停止告警
        if (!stats.isRunning()) {
            logger.error("🚨 队列已停止运行");
        }
    }
    
    /**
     * 获取队列容量（从配置中读取，这里使用默认值）
     */
    private int getQueueCapacity() {
        // TODO: 从配置文件中读取
        return 10000;
    }
    
    /**
     * 获取当前监控状态
     */
    public MonitorStatus getStatus() {
        if (queuedConnectionManager == null) {
            return new MonitorStatus(running.get(), null, "未设置监控目标");
        }
        
        try {
            PacketQueue.QueueStats stats = queuedConnectionManager.getQueueStats();
            return new MonitorStatus(running.get(), stats, null);
        } catch (Exception e) {
            return new MonitorStatus(running.get(), null, e.getMessage());
        }
    }
    
    /**
     * 监控状态信息
     */
    public static class MonitorStatus {
        private final boolean running;
        private final PacketQueue.QueueStats queueStats;
        private final String error;
        
        public MonitorStatus(boolean running, PacketQueue.QueueStats queueStats, String error) {
            this.running = running;
            this.queueStats = queueStats;
            this.error = error;
        }
        
        public boolean isRunning() { return running; }
        public PacketQueue.QueueStats getQueueStats() { return queueStats; }
        public String getError() { return error; }
        
        @Override
        public String toString() {
            if (error != null) {
                return String.format("MonitorStatus{running=%s, error='%s'}", running, error);
            }
            return String.format("MonitorStatus{running=%s, queueStats=%s}", running, queueStats);
        }
    }
}