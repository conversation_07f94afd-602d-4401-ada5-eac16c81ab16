package com.xiang.proxy.server.inbound;

import com.xiang.proxy.server.core.ProxyProcessor;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;

import java.util.concurrent.CompletableFuture;

/**
 * Inbound服务器接口
 * 代表一个完整的Netty服务端，负责监听特定端口并处理入站连接
 */
public interface InboundServer {
    
    /**
     * 启动服务器
     * @return 启动结果的Future
     */
    CompletableFuture<Void> start();
    
    /**
     * 停止服务器
     * @return 停止结果的Future
     */
    CompletableFuture<Void> stop();
    
    /**
     * 获取服务器ID
     * @return 服务器标识符
     */
    String getServerId();
    
    /**
     * 获取监听端口
     * @return 端口号
     */
    int getPort();
    
    /**
     * 获取服务器类型
     * @return 服务器类型名称
     */
    String getServerType();
    
    /**
     * 检查服务器是否正在运行
     * @return 是否运行中
     */
    boolean isRunning();
    
    /**
     * 获取服务器配置
     * @return 配置对象
     */
    InboundServerConfig getConfig();
    
    /**
     * 获取关联的代理处理器
     * @return 代理处理器
     */
    ProxyProcessor getProxyProcessor();
    
    /**
     * 创建Channel初始化器
     * @return Channel初始化器
     */
    ChannelInitializer<SocketChannel> createChannelInitializer();
    
    /**
     * 获取服务器统计信息
     * @return 统计信息
     */
    default InboundServerStatistics getStatistics() {
        return new InboundServerStatistics();
    }
    
    /**
     * 重置统计信息
     */
    default void resetStatistics() {
        // 默认空实现
    }
    
    /**
     * 获取当前连接数
     * @return 连接数
     */
    default int getCurrentConnections() {
        return 0;
    }
    
    /**
     * 获取服务器健康状态
     * @return 健康状态
     */
    default ServerHealthStatus getHealthStatus() {
        return isRunning() ? ServerHealthStatus.HEALTHY : ServerHealthStatus.STOPPED;
    }
    
    /**
     * 服务器健康状态枚举
     */
    enum ServerHealthStatus {
        HEALTHY,    // 健康运行
        DEGRADED,   // 降级运行
        UNHEALTHY,  // 不健康
        STOPPED     // 已停止
    }
}