# Netty Multiplex Proxy Service - Alicloud Deployment

This directory contains the Alicloud deployment configuration for the Netty Multiplex Proxy Service. The proxy-server has been refactored to use dependency imports instead of containing the source code directly.

## Architecture

- **proxy-server**: Core proxy functionality (imported as Maven dependency from `../netty_multiplex_proxy/proxy-server`)
- **netty-multiplex-proxy-service**: Spring Boot wrapper service that integrates with Nacos and provides REST APIs
- **MySQL**: Database for storing configuration and user data
- **Nacos**: Service discovery and configuration management

## Prerequisites

- Docker and Docker Compose
- Maven 3.6+
- Java 17+
- Access to the proxy-server source code at `../netty_multiplex_proxy/proxy-server`

## Quick Start

### Option 1: Using Build Scripts (Recommended)

**Linux/macOS:**
```bash
chmod +x build-and-deploy.sh
./build-and-deploy.sh
```

**Windows:**
```cmd
build-and-deploy.bat
```

### Option 2: Manual Build

1. **Build proxy-server dependency:**
   ```bash
   cd ../netty_multiplex_proxy/proxy-server
   mvn clean install -DskipTests
   ```

2. **Build and start services:**
   ```bash
   cd ../../netty_multiplex_proxy_alicloud
   docker-compose build --no-cache proxy-server
   docker-compose up -d
   ```

## Service Endpoints

- **Proxy Server**: http://localhost:8888
- **Nacos Console**: http://localhost:8848/nacos (nacos/nacos)
- **MySQL**: localhost:3306 (root/123456)

## Configuration

- **Proxy Server Config**: Mounted from `../netty_multiplex_proxy/configs/production/server`
- **Application Config**: `configs/netty-multiplex-proxy-service-dev.yml`
- **MySQL Init**: `configs/mysql/init/01-init-databases.sql`

## Monitoring

Check service status:
```bash
docker-compose ps
docker-compose logs -f proxy-server
```

Health check endpoint:
```bash
curl http://localhost:8888/actuator/health
```

## Troubleshooting

1. **Build fails**: Ensure proxy-server dependency is built first
2. **Port conflicts**: Check if ports 3306, 8848, 8888 are available
3. **Configuration issues**: Verify config files in the mounted volumes

## Stopping Services

```bash
docker-compose down
```

To remove volumes:
```bash
docker-compose down -v
```