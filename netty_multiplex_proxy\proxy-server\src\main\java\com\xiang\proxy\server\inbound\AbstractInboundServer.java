package com.xiang.proxy.server.inbound;

import com.xiang.proxy.server.core.ProxyProcessor;
import com.xiang.proxy.server.ssl.SslContextManager;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.epoll.Epoll;
import io.netty.channel.epoll.EpollEventLoopGroup;
import io.netty.channel.epoll.EpollServerSocketChannel;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.ServerSocketChannel;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.ssl.SslContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 抽象Inbound服务器基类
 * 提供通用的Netty服务器启动和管理功能
 */
public abstract class AbstractInboundServer implements InboundServer {
    private static final Logger logger = LoggerFactory.getLogger(AbstractInboundServer.class);

    protected final String serverId;
    protected final InboundServerConfig config;
    protected final ProxyProcessor proxyProcessor;
    protected final InboundServerStatistics statistics;
    protected final AtomicBoolean running = new AtomicBoolean(false);
    protected final AtomicInteger currentConnections = new AtomicInteger(0);

    protected EventLoopGroup bossGroup;
    protected EventLoopGroup workerGroup;
    protected Channel serverChannel;
    protected SslContext sslContext;
    private Class<? extends ServerSocketChannel> serverChannelClazz;

    public AbstractInboundServer(String serverId, InboundServerConfig config, ProxyProcessor proxyProcessor) {
        this.serverId = serverId;
        this.config = config;
        this.proxyProcessor = proxyProcessor;
        this.statistics = new InboundServerStatistics();
    }

    @Override
    public CompletableFuture<Void> start() {
        if (running.get()) {
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<Void> future = new CompletableFuture<>();

        try {
            logger.info("启动Inbound服务器: {} ({}:{})", serverId, config.getBindAddress(), config.getPort());

            // 初始化SSL上下文
            if (config.isEnableSsl()) {
                this.sslContext = SslContextManager.getInstance().getServerSslContext();
                logger.info("SSL上下文初始化完成: {}", serverId);
            }

            // 创建事件循环组
            createEventLoopGroups();

            // 创建服务器引导
            ServerBootstrap bootstrap = createServerBootstrap();

            // 绑定端口并启动
            ChannelFuture bindFuture = bootstrap.bind(config.getBindAddress(), config.getPort());

            bindFuture.addListener((ChannelFutureListener) channelFuture -> {
                if (channelFuture.isSuccess()) {
                    serverChannel = channelFuture.channel();
                    running.set(true);

                    logger.info("Inbound服务器启动成功: {} ({}:{})",
                            serverId, config.getBindAddress(), config.getPort());

                    // 添加关闭监听器
                    serverChannel.closeFuture().addListener(closeFuture -> {
                        logger.info("Inbound服务器关闭: {}", serverId);
                        running.set(false);
                    });

                    future.complete(null);
                } else {
                    logger.error("Inbound服务器启动失败: {}", serverId, channelFuture.cause());
                    cleanup();
                    future.completeExceptionally(channelFuture.cause());
                }
            });

        } catch (Exception e) {
            logger.error("启动Inbound服务器时发生异常: {}", serverId, e);
            cleanup();
            future.completeExceptionally(e);
        }

        return future;
    }

    @Override
    public CompletableFuture<Void> stop() {
        if (!running.get()) {
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<Void> future = new CompletableFuture<>();

        logger.info("停止Inbound服务器: {}", serverId);

        try {
            if (serverChannel != null) {
                serverChannel.close().addListener((ChannelFutureListener) closeFuture -> {
                    cleanup();
                    if (closeFuture.isSuccess()) {
                        logger.info("Inbound服务器停止成功: {}", serverId);
                        future.complete(null);
                    } else {
                        logger.warn("Inbound服务器停止时发生异常: {}", serverId, closeFuture.cause());
                        future.completeExceptionally(closeFuture.cause());
                    }
                });
            } else {
                cleanup();
                future.complete(null);
            }
        } catch (Exception e) {
            logger.error("停止Inbound服务器时发生异常: {}", serverId, e);
            cleanup();
            future.completeExceptionally(e);
        }

        return future;
    }

    /**
     * 创建事件循环组
     */
    protected void createEventLoopGroups() {
        int bossThreads = config.getBossThreads();
        int workerThreads = config.getWorkerThreads();

        if (workerThreads <= 0) {
            workerThreads = Runtime.getRuntime().availableProcessors() * 2;
        }

        if (Epoll.isAvailable()) {
            bossGroup = new EpollEventLoopGroup(bossThreads, r -> {
                Thread t = new Thread(r, serverId + "-boss");
                t.setDaemon(true);
                return t;
            });

            workerGroup = new EpollEventLoopGroup(workerThreads, r -> {
                Thread t = new Thread(r, serverId + "-worker");
                t.setDaemon(true);
                return t;
            });

            serverChannelClazz = EpollServerSocketChannel.class;
        } else {
            //PlatformDependent.isWindows()
            bossGroup = new NioEventLoopGroup(bossThreads, r -> {
                Thread t = new Thread(r, serverId + "-boss");
                t.setDaemon(true);
                return t;
            });

            workerGroup = new NioEventLoopGroup(workerThreads, r -> {
                Thread t = new Thread(r, serverId + "-worker");
                t.setDaemon(true);
                return t;
            });

            serverChannelClazz = NioServerSocketChannel.class;
        }

        logger.debug("创建事件循环组: {} (boss={}, worker={})", serverId, bossThreads, workerThreads);
    }

    /**
     * 创建服务器引导
     */
    protected ServerBootstrap createServerBootstrap() {
        ServerBootstrap bootstrap = new ServerBootstrap();

        bootstrap.group(bossGroup, workerGroup)
                .channel(serverChannelClazz)
                .childHandler(createChannelInitializer())
                // 服务器选项
                .option(ChannelOption.SO_BACKLOG, config.getBacklog())
                .option(ChannelOption.SO_REUSEADDR, config.isReuseAddress())
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                // 客户端连接选项
                .childOption(ChannelOption.TCP_NODELAY, config.isTcpNoDelay())
                .childOption(ChannelOption.SO_KEEPALIVE, config.isKeepAlive())
                .childOption(ChannelOption.SO_REUSEADDR, config.isReuseAddress())
                .childOption(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                .childOption(ChannelOption.SO_RCVBUF, config.getReceiveBufferSize())
                .childOption(ChannelOption.SO_SNDBUF, config.getSendBufferSize())
                .childOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, config.getConnectTimeout())
                .childOption(ChannelOption.WRITE_BUFFER_WATER_MARK,
                        new WriteBufferWaterMark(8 * 1024, 32 * 1024));

        return bootstrap;
    }

    /**
     * 创建连接处理器
     */
    protected ChannelHandler createConnectionHandler() {
        return new ChannelInboundHandlerAdapter() {
            @Override
            public void channelActive(ChannelHandlerContext ctx) throws Exception {
                int current = currentConnections.incrementAndGet();
                statistics.incrementTotalConnections();
                statistics.incrementCurrentConnections();

                logger.debug("新连接建立: {} (当前连接数: {})", ctx.channel().remoteAddress(), current);

                // 检查连接数限制
                if (current > config.getMaxConnections()) {
                    logger.warn("连接数超过限制，关闭连接: {} (当前: {}, 限制: {})",
                            ctx.channel().remoteAddress(), current, config.getMaxConnections());
                    ctx.close();
                    return;
                }

                super.channelActive(ctx);
            }

            @Override
            public void channelInactive(ChannelHandlerContext ctx) throws Exception {
                int current = currentConnections.decrementAndGet();
                statistics.decrementCurrentConnections();

                logger.debug("连接断开: {} (当前连接数: {})", ctx.channel().remoteAddress(), current);
                super.channelInactive(ctx);
            }

            @Override
            public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
                statistics.incrementConnectionErrors();
                logger.error("连接异常: {}", ctx.channel().remoteAddress(), cause);
                ctx.close();
            }
        };
    }

    /**
     * 清理资源
     */
    protected void cleanup() {
        running.set(false);

        if (bossGroup != null) {
            bossGroup.shutdownGracefully();
            bossGroup = null;
        }

        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
            workerGroup = null;
        }

        serverChannel = null;
        sslContext = null;
    }

    // 实现接口方法
    @Override
    public String getServerId() {
        return serverId;
    }

    @Override
    public int getPort() {
        return config.getPort();
    }

    @Override
    public boolean isRunning() {
        return running.get();
    }

    @Override
    public InboundServerConfig getConfig() {
        return config;
    }

    @Override
    public ProxyProcessor getProxyProcessor() {
        return proxyProcessor;
    }

    @Override
    public InboundServerStatistics getStatistics() {
        return statistics;
    }

    @Override
    public void resetStatistics() {
        statistics.reset();
    }

    @Override
    public int getCurrentConnections() {
        return currentConnections.get();
    }

    @Override
    public ServerHealthStatus getHealthStatus() {
        if (!isRunning()) {
            return ServerHealthStatus.STOPPED;
        }

        // 基于连接错误率判断健康状态
        double errorRate = statistics.getConnectionErrorRate();
        if (errorRate > 0.1) { // 10%以上错误率
            return ServerHealthStatus.UNHEALTHY;
        } else if (errorRate > 0.05) { // 5%以上错误率
            return ServerHealthStatus.DEGRADED;
        } else {
            return ServerHealthStatus.HEALTHY;
        }
    }

    /**
     * 抽象方法：子类需要实现具体的Channel初始化器
     */
    @Override
    public abstract ChannelInitializer<SocketChannel> createChannelInitializer();

    /**
     * 抽象方法：子类需要实现具体的服务器类型
     */
    @Override
    public abstract String getServerType();
}