package com.xiang.proxy.server.config.properties;

import com.xiang.proxy.server.config.annotation.ConfigurationProperties;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ProxyServerV2配置模型 - 基于实际配置文件结构
 */
@ConfigurationProperties
public class ProxyServerV2Properties {
    private GlobalProperties global = new GlobalProperties();
    private List<InboundProperties> inbounds = new ArrayList<>();
    private List<OutboundProperties> outbounds = new ArrayList<>();
    private RoutingProperties routing = new RoutingProperties();
    private AuthProperties auth = new AuthProperties();
    private PoolProperties pool = new PoolProperties();
    private MetricsProperties metrics = new MetricsProperties();
    private BlacklistProperties blacklist = new BlacklistProperties();
    private PerformanceProperties performance = new PerformanceProperties();
    private SslProperties ssl = new SslProperties();
    private GeoLocationFilterProperties geoLocationFilter = new GeoLocationFilterProperties();

    // Getters and Setters
    public GlobalProperties getGlobal() {
        return global;
    }

    public void setGlobal(GlobalProperties global) {
        this.global = global;
    }

    public List<InboundProperties> getInbounds() {
        return inbounds;
    }

    public void setInbounds(List<InboundProperties> inbounds) {
        this.inbounds = inbounds != null ? inbounds : new ArrayList<>();
    }

    public List<OutboundProperties> getOutbounds() {
        return outbounds;
    }

    public void setOutbounds(List<OutboundProperties> outbounds) {
        this.outbounds = outbounds != null ? outbounds : new ArrayList<>();
    }

    public RoutingProperties getRouting() {
        return routing;
    }

    public void setRouting(RoutingProperties routing) {
        this.routing = routing;
    }

    public AuthProperties getAuth() {
        return auth;
    }

    public void setAuth(AuthProperties auth) {
        this.auth = auth;
    }

    public PoolProperties getPool() {
        return pool;
    }

    public void setPool(PoolProperties pool) {
        this.pool = pool;
    }

    public MetricsProperties getMetrics() {
        return metrics;
    }

    public void setMetrics(MetricsProperties metrics) {
        this.metrics = metrics;
    }

    public BlacklistProperties getBlacklist() {
        return blacklist;
    }

    public void setBlacklist(BlacklistProperties blacklist) {
        this.blacklist = blacklist;
    }

    public PerformanceProperties getPerformance() {
        return performance;
    }

    public void setPerformance(PerformanceProperties performance) {
        this.performance = performance;
    }

    public SslProperties getSsl() {
        return ssl;
    }

    public void setSsl(SslProperties ssl) {
        this.ssl = ssl;
    }

    public GeoLocationFilterProperties getGeoLocationFilter() {
        return geoLocationFilter;
    }

    public void setGeoLocationFilter(GeoLocationFilterProperties geoLocationFilter) {
        this.geoLocationFilter = geoLocationFilter;
    }

    // 内部配置类
    public static class GlobalProperties {
        private String name = "ProxyServerV2";
        private String version = "2.0.0";
        private String description = "组件化代理服务器";

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }

    public static class InboundProperties {
        private String id;
        private String type;
        private String name;
        private boolean enabled = true;
        private Map<String, Object> config = new HashMap<>();

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public Map<String, Object> getConfig() {
            return config;
        }

        public void setConfig(Map<String, Object> config) {
            this.config = config != null ? config : new HashMap<>();
        }

        @SuppressWarnings("unchecked")
        public <T> T getConfigValue(String key) {
            return (T) config.get(key);
        }

        public <T> T getConfigValue(String key, T defaultValue) {
            T value = getConfigValue(key);
            return value != null ? value : defaultValue;
        }
    }

    public static class OutboundProperties {
        private String id;
        private String type;
        private String name;
        private boolean enabled = true;
        private Map<String, Object> config = new HashMap<>();

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public Map<String, Object> getConfig() {
            return config;
        }

        public void setConfig(Map<String, Object> config) {
            this.config = config != null ? config : new HashMap<>();
        }

        @SuppressWarnings("unchecked")
        public <T> T getConfigValue(String key) {
            return (T) config.get(key);
        }

        public <T> T getConfigValue(String key, T defaultValue) {
            T value = getConfigValue(key);
            return value != null ? value : defaultValue;
        }
    }

    public static class RoutingProperties {
        private String defaultOutbound = "direct";
        private List<RouteRuleProperties> rules = new ArrayList<>();

        public String getDefaultOutbound() {
            return defaultOutbound;
        }

        public void setDefaultOutbound(String defaultOutbound) {
            this.defaultOutbound = defaultOutbound;
        }

        public List<RouteRuleProperties> getRules() {
            return rules;
        }

        public void setRules(List<RouteRuleProperties> rules) {
            this.rules = rules != null ? rules : new ArrayList<>();
        }
    }

    public static class RouteRuleProperties {
        private String id;
        private String name;
        private int priority = 100;
        private String outbound;
        private boolean enabled = true;
        private List<RouteMatcherConfig> matchers = new ArrayList<>();

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getPriority() {
            return priority;
        }

        public void setPriority(int priority) {
            this.priority = priority;
        }

        public String getOutbound() {
            return outbound;
        }

        public void setOutbound(String outbound) {
            this.outbound = outbound;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public List<RouteMatcherConfig> getMatchers() {
            return matchers;
        }

        public void setMatchers(List<RouteMatcherConfig> matchers) {
            this.matchers = matchers != null ? matchers : new ArrayList<>();
        }
    }

    public static class RouteMatcherConfig {
        private String type;
        private String operator;
        private String value;
        private boolean caseSensitive = true;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getOperator() {
            return operator;
        }

        public void setOperator(String operator) {
            this.operator = operator;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public boolean isCaseSensitive() {
            return caseSensitive;
        }

        public void setCaseSensitive(boolean caseSensitive) {
            this.caseSensitive = caseSensitive;
        }
    }

    public static class AuthProperties {
        private boolean enable = false;
        private String username = "admin";
        private String password = "password";
        private TimeoutProperties timeout = new TimeoutProperties();

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public TimeoutProperties getTimeout() {
            return timeout;
        }

        public void setTimeout(TimeoutProperties timeout) {
            this.timeout = timeout;
        }
    }

    public static class PoolProperties {
        private boolean enable = true;
        private MaxConnectionsConfig maxConnections = new MaxConnectionsConfig();
        private TimeoutProperties idleTimeout = new TimeoutProperties();
        private TimeoutProperties cleanupInterval = new TimeoutProperties();

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public MaxConnectionsConfig getMaxConnections() {
            return maxConnections;
        }

        public void setMaxConnections(MaxConnectionsConfig maxConnections) {
            this.maxConnections = maxConnections;
        }

        public TimeoutProperties getIdleTimeout() {
            return idleTimeout;
        }

        public void setIdleTimeout(TimeoutProperties idleTimeout) {
            this.idleTimeout = idleTimeout;
        }

        public TimeoutProperties getCleanupInterval() {
            return cleanupInterval;
        }

        public void setCleanupInterval(TimeoutProperties cleanupInterval) {
            this.cleanupInterval = cleanupInterval;
        }
    }

    public static class MaxConnectionsConfig {
        private int perHost = 20;

        public int getPerHost() {
            return perHost;
        }

        public void setPerHost(int perHost) {
            this.perHost = perHost;
        }
    }

    public static class BlacklistProperties {
        private boolean enable = true;
        private FailureConfig failure = new FailureConfig();
        private CacheConfig cache = new CacheConfig();

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public FailureConfig getFailure() {
            return failure;
        }

        public void setFailure(FailureConfig failure) {
            this.failure = failure;
        }

        public CacheConfig getCache() {
            return cache;
        }

        public void setCache(CacheConfig cache) {
            this.cache = cache;
        }
    }

    public static class FailureConfig {
        private int threshold = 3;

        public int getThreshold() {
            return threshold;
        }

        public void setThreshold(int threshold) {
            this.threshold = threshold;
        }
    }

    public static class CacheConfig {
        private TimeoutProperties timeout = new TimeoutProperties();

        public TimeoutProperties getTimeout() {
            return timeout;
        }

        public void setTimeout(TimeoutProperties timeout) {
            this.timeout = timeout;
        }
    }

    public static class PerformanceProperties {
        private int bossThreads = 0;
        private int workerThreads = 0;
        private int ioRatio = 60;
        private boolean enableThreadOptimization = true;
        private int maxWorkerThreads = 32;
        private int minWorkerThreads = 2;

        public int getBossThreads() {
            return bossThreads;
        }

        public void setBossThreads(int bossThreads) {
            this.bossThreads = bossThreads;
        }

        public int getWorkerThreads() {
            return workerThreads;
        }

        public void setWorkerThreads(int workerThreads) {
            this.workerThreads = workerThreads;
        }

        public int getIoRatio() {
            return ioRatio;
        }

        public void setIoRatio(int ioRatio) {
            this.ioRatio = ioRatio;
        }

        public boolean isEnableThreadOptimization() {
            return enableThreadOptimization;
        }

        public void setEnableThreadOptimization(boolean enableThreadOptimization) {
            this.enableThreadOptimization = enableThreadOptimization;
        }

        public int getMaxWorkerThreads() {
            return maxWorkerThreads;
        }

        public void setMaxWorkerThreads(int maxWorkerThreads) {
            this.maxWorkerThreads = maxWorkerThreads;
        }

        public int getMinWorkerThreads() {
            return minWorkerThreads;
        }

        public void setMinWorkerThreads(int minWorkerThreads) {
            this.minWorkerThreads = minWorkerThreads;
        }
    }

    public static class MetricsProperties {
        private boolean enable = true;
        private ReportConfig report = new ReportConfig();

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public ReportConfig getReport() {
            return report;
        }

        public void setReport(ReportConfig report) {
            this.report = report;
        }
    }

    public static class ReportConfig {
        private TimeoutProperties interval = new TimeoutProperties();

        public TimeoutProperties getInterval() {
            return interval;
        }

        public void setInterval(TimeoutProperties interval) {
            this.interval = interval;
        }
    }

    public static class SslProperties {
        private boolean enable = false;
        private String keyStorePath = "server.p12";
        private String keyStorePassword = "password";
        private String keyStoreType = "PKCS12";
        private String trustStorePath = "truststore.p12";
        private String trustStorePassword = "password";
        private String trustStoreType = "PKCS12";
        private List<String> protocols = new ArrayList<>();
        private List<String> cipherSuites = new ArrayList<>();
        private boolean clientAuth = false;
        private boolean needClientAuth = false;
        private boolean wantClientAuth = false;
        private TimeoutProperties handshakeTimeout = new TimeoutProperties();

        public SslProperties() {
            protocols.add("TLSv1.2");
            protocols.add("TLSv1.3");
            handshakeTimeout.setSeconds(30);
        }

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public String getKeyStorePath() {
            return keyStorePath;
        }

        public void setKeyStorePath(String keyStorePath) {
            this.keyStorePath = keyStorePath;
        }

        public String getKeyStorePassword() {
            return keyStorePassword;
        }

        public void setKeyStorePassword(String keyStorePassword) {
            this.keyStorePassword = keyStorePassword;
        }

        public String getKeyStoreType() {
            return keyStoreType;
        }

        public void setKeyStoreType(String keyStoreType) {
            this.keyStoreType = keyStoreType;
        }

        public String getTrustStorePath() {
            return trustStorePath;
        }

        public void setTrustStorePath(String trustStorePath) {
            this.trustStorePath = trustStorePath;
        }

        public String getTrustStorePassword() {
            return trustStorePassword;
        }

        public void setTrustStorePassword(String trustStorePassword) {
            this.trustStorePassword = trustStorePassword;
        }

        public String getTrustStoreType() {
            return trustStoreType;
        }

        public void setTrustStoreType(String trustStoreType) {
            this.trustStoreType = trustStoreType;
        }

        public List<String> getProtocols() {
            return protocols;
        }

        public void setProtocols(List<String> protocols) {
            this.protocols = protocols != null ? protocols : new ArrayList<>();
        }

        public List<String> getCipherSuites() {
            return cipherSuites;
        }

        public void setCipherSuites(List<String> cipherSuites) {
            this.cipherSuites = cipherSuites != null ? cipherSuites : new ArrayList<>();
        }

        public boolean isClientAuth() {
            return clientAuth;
        }

        public void setClientAuth(boolean clientAuth) {
            this.clientAuth = clientAuth;
        }

        public boolean isNeedClientAuth() {
            return needClientAuth;
        }

        public void setNeedClientAuth(boolean needClientAuth) {
            this.needClientAuth = needClientAuth;
        }

        public boolean isWantClientAuth() {
            return wantClientAuth;
        }

        public void setWantClientAuth(boolean wantClientAuth) {
            this.wantClientAuth = wantClientAuth;
        }

        public TimeoutProperties getHandshakeTimeout() {
            return handshakeTimeout;
        }

        public void setHandshakeTimeout(TimeoutProperties handshakeTimeout) {
            this.handshakeTimeout = handshakeTimeout;
        }
    }

    public static class GeoLocationFilterProperties {
        private boolean enable = false;
        private boolean blockOverseasSuspicious = false;
        private boolean enableDomainFilter = true;
        private boolean enableKeywordFilter = true;
        private boolean enableWhitelist = true;
        private TimeoutProperties dnsCacheTimeout = new TimeoutProperties();
        private TimeoutProperties ipCacheTimeout = new TimeoutProperties();
        private int maxCacheSize = 10000;
        private boolean autoUpdateIpRanges = true;
        private TimeoutProperties updateInterval = new TimeoutProperties();
        private OnlineDataSourcesProperties onlineDataSources = new OnlineDataSourcesProperties();

        public GeoLocationFilterProperties() {
            dnsCacheTimeout.setMinutes(5);
            ipCacheTimeout.setMinutes(60);
            updateInterval.setHours(24);
        }

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public boolean isBlockOverseasSuspicious() {
            return blockOverseasSuspicious;
        }

        public void setBlockOverseasSuspicious(boolean blockOverseasSuspicious) {
            this.blockOverseasSuspicious = blockOverseasSuspicious;
        }

        public boolean isEnableDomainFilter() {
            return enableDomainFilter;
        }

        public void setEnableDomainFilter(boolean enableDomainFilter) {
            this.enableDomainFilter = enableDomainFilter;
        }

        public boolean isEnableKeywordFilter() {
            return enableKeywordFilter;
        }

        public void setEnableKeywordFilter(boolean enableKeywordFilter) {
            this.enableKeywordFilter = enableKeywordFilter;
        }

        public boolean isEnableWhitelist() {
            return enableWhitelist;
        }

        public void setEnableWhitelist(boolean enableWhitelist) {
            this.enableWhitelist = enableWhitelist;
        }

        public TimeoutProperties getDnsCacheTimeout() {
            return dnsCacheTimeout;
        }

        public void setDnsCacheTimeout(TimeoutProperties dnsCacheTimeout) {
            this.dnsCacheTimeout = dnsCacheTimeout;
        }

        public TimeoutProperties getIpCacheTimeout() {
            return ipCacheTimeout;
        }

        public void setIpCacheTimeout(TimeoutProperties ipCacheTimeout) {
            this.ipCacheTimeout = ipCacheTimeout;
        }

        public int getMaxCacheSize() {
            return maxCacheSize;
        }

        public void setMaxCacheSize(int maxCacheSize) {
            this.maxCacheSize = maxCacheSize;
        }

        public boolean isAutoUpdateIpRanges() {
            return autoUpdateIpRanges;
        }

        public void setAutoUpdateIpRanges(boolean autoUpdateIpRanges) {
            this.autoUpdateIpRanges = autoUpdateIpRanges;
        }

        public TimeoutProperties getUpdateInterval() {
            return updateInterval;
        }

        public void setUpdateInterval(TimeoutProperties updateInterval) {
            this.updateInterval = updateInterval;
        }

        public OnlineDataSourcesProperties getOnlineDataSources() {
            return onlineDataSources;
        }

        public void setOnlineDataSources(OnlineDataSourcesProperties onlineDataSources) {
            this.onlineDataSources = onlineDataSources;
        }
    }

    public static class OnlineDataSourcesProperties {
        private List<String> maliciousDomains = new ArrayList<>();
        private List<String> maliciousKeywords = new ArrayList<>();
        private List<String> chinaIpRanges = new ArrayList<>();

        public List<String> getMaliciousDomains() {
            return maliciousDomains;
        }

        public void setMaliciousDomains(List<String> maliciousDomains) {
            this.maliciousDomains = maliciousDomains != null ? maliciousDomains : new ArrayList<>();
        }

        public List<String> getMaliciousKeywords() {
            return maliciousKeywords;
        }

        public void setMaliciousKeywords(List<String> maliciousKeywords) {
            this.maliciousKeywords = maliciousKeywords != null ? maliciousKeywords : new ArrayList<>();
        }

        public List<String> getChinaIpRanges() {
            return chinaIpRanges;
        }

        public void setChinaIpRanges(List<String> chinaIpRanges) {
            this.chinaIpRanges = chinaIpRanges != null ? chinaIpRanges : new ArrayList<>();
        }
    }

    public static class TimeoutProperties {
        private int seconds = 30;
        private int minutes = 0;
        private int hours = 0;

        public int getSeconds() {
            return seconds;
        }

        public void setSeconds(int seconds) {
            this.seconds = seconds;
        }

        public int getMinutes() {
            return minutes;
        }

        public void setMinutes(int minutes) {
            this.minutes = minutes;
        }

        public int getHours() {
            return hours;
        }

        public void setHours(int hours) {
            this.hours = hours;
        }

        public int getTotalSeconds() {
            return seconds + (minutes * 60) + (hours * 3600);
        }
    }
}