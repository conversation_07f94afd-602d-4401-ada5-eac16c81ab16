# Inbound服务器架构使用示例

## 架构概览

新的Inbound架构分为两个层次：

1. **InboundServer** - Netty服务端，负责网络监听和连接管理
2. **InboundHandler** - 协议处理器，负责具体的协议解析和请求处理

```
┌─────────────────────────────────────────────────────────────┐
│                    InboundServer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Netty Server   │  │  SSL Handler    │  │  Connection  │ │
│  │  (Port Binding) │  │  (Optional)     │  │  Management  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│                              │                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              InboundHandler                             │ │
│  │  ┌─────────────────┐  ┌─────────────────┐              │ │
│  │  │ Protocol Parser │  │ Request Builder │              │ │
│  │  └─────────────────┘  └─────────────────┘              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                      ┌──────────────┐
                      │ ProxyRequest │
                      └──────────────┘
                              │
                              ▼
                      ┌──────────────┐
                      │ ProxyProcessor│
                      └──────────────┘
```

## 1. 基本使用示例

### 1.1 创建和启动多路复用服务器

```java
// 创建代理处理器
Router router = new DefaultRouter();
ProxyProcessor proxyProcessor = new ProxyProcessor(router);

// 创建Inbound服务器管理器
InboundServerManager serverManager = new InboundServerManager(proxyProcessor);

// 创建多路复用服务器
MultiplexInboundServer multiplexServer = serverManager.createMultiplexServer("multiplex-8080", 8080);

// 启动服务器
multiplexServer.start().whenComplete((result, throwable) -> {
    if (throwable != null) {
        logger.error("服务器启动失败", throwable);
    } else {
        logger.info("多路复用服务器启动成功，监听端口: 8080");
    }
});
```

### 1.2 创建高性能服务器

```java
// 创建高性能配置的服务器
MultiplexInboundServer highPerfServer = serverManager.createHighPerformanceMultiplexServer("multiplex-high-8081", 8081);

// 启动服务器
highPerfServer.start();
```

### 1.3 创建SSL服务器

```java
// 创建SSL服务器
MultiplexInboundServer sslServer = serverManager.createSslMultiplexServer(
    "multiplex-ssl-8443", 8443, 
    "/path/to/keystore.jks", "keystorePassword"
);

// 启动服务器
sslServer.start();
```

## 2. 自定义配置示例

### 2.1 详细配置服务器

```java
// 创建自定义配置
InboundServerConfig config = new InboundServerConfig(8080);
config.setBindAddress("0.0.0.0");
config.setBossThreads(2);
config.setWorkerThreads(8);
config.setBacklog(2048);
config.setMaxConnections(20000);
config.setReceiveBufferSize(128 * 1024);
config.setSendBufferSize(128 * 1024);
config.setKeepAlive(true);
config.setTcpNoDelay(true);

// 设置自定义属性
config.setProperty("max.frame.length", 2 * 1024 * 1024); // 2MB最大帧长度
config.setProperty("enable.protocol.detection", true);   // 启用协议检测

// 创建服务器
MultiplexInboundServer customServer = new MultiplexInboundServer("custom-server", config, proxyProcessor);
serverManager.registerServer(customServer);

// 启动服务器
customServer.start();
```

### 2.2 SSL配置示例

```java
InboundServerConfig sslConfig = new InboundServerConfig(8443);
sslConfig.setEnableSsl(true);
sslConfig.setSslKeyStore("/path/to/server.jks");
sslConfig.setSslKeyStorePassword("serverPassword");
sslConfig.setSslTrustStore("/path/to/truststore.jks");
sslConfig.setSslTrustStorePassword("truststorePassword");

MultiplexInboundServer sslServer = new MultiplexInboundServer("ssl-server", sslConfig, proxyProcessor);
```

## 3. 服务器管理示例

### 3.1 批量管理服务器

```java
// 创建多个服务器
serverManager.createMultiplexServer("server-8080", 8080);
serverManager.createMultiplexServer("server-8081", 8081);
serverManager.createMultiplexServer("server-8082", 8082);

// 启动所有服务器
serverManager.startAll().whenComplete((result, throwable) -> {
    if (throwable != null) {
        logger.error("部分服务器启动失败", throwable);
    } else {
        logger.info("所有服务器启动成功");
    }
});

// 停止所有服务器
serverManager.stopAll().whenComplete((result, throwable) -> {
    logger.info("所有服务器已停止");
});
```

### 3.2 动态管理服务器

```java
// 动态启动服务器
serverManager.startServer("server-8080");

// 动态停止服务器
serverManager.stopServer("server-8081");

// 动态注销服务器
serverManager.unregisterServer("server-8082");
```

## 4. 监控和统计示例

### 4.1 获取服务器统计信息

```java
// 获取单个服务器统计
InboundServer server = serverManager.getServer("multiplex-8080");
InboundServerStatistics stats = server.getStatistics();
logger.info("服务器统计: {}", stats);

// 获取多路复用特定统计
if (server instanceof MultiplexInboundServer) {
    MultiplexInboundServer multiplexServer = (MultiplexInboundServer) server;
    MultiplexInboundServer.MultiplexStatistics multiplexStats = multiplexServer.getMultiplexStatistics();
    logger.info("多路复用统计: {}", multiplexStats);
}
```

### 4.2 获取管理器统计信息

```java
// 获取管理器统计
InboundServerManager.ManagerStatistics managerStats = serverManager.getStatistics();
logger.info("管理器统计: {}", managerStats);

// 获取所有服务器统计
Map<String, InboundServerStatistics> allStats = serverManager.getAllStatistics();
allStats.forEach((serverId, stats) -> {
    logger.info("服务器 {} 统计: {}", serverId, stats);
});
```

### 4.3 健康状态监控

```java
// 获取健康状态报告
InboundServerManager.HealthReport healthReport = serverManager.getHealthReport();
logger.info("健康状态报告: {}", healthReport);

// 检查特定服务器健康状态
InboundServer.ServerHealthStatus health = server.getHealthStatus();
switch (health) {
    case HEALTHY:
        logger.info("服务器健康");
        break;
    case DEGRADED:
        logger.warn("服务器性能降级");
        break;
    case UNHEALTHY:
        logger.error("服务器不健康");
        break;
    case STOPPED:
        logger.info("服务器已停止");
        break;
}
```

## 5. 扩展示例

### 5.1 创建自定义Inbound服务器

```java
public class HttpInboundServer extends AbstractInboundServer {
    
    public HttpInboundServer(String serverId, InboundServerConfig config, ProxyProcessor proxyProcessor) {
        super(serverId, config, proxyProcessor);
    }

    @Override
    public String getServerType() {
        return "http";
    }

    @Override
    public ChannelInitializer<SocketChannel> createChannelInitializer() {
        return new ChannelInitializer<SocketChannel>() {
            @Override
            protected void initChannel(SocketChannel ch) throws Exception {
                ChannelPipeline pipeline = ch.pipeline();
                
                // SSL处理器
                if (config.isEnableSsl() && sslContext != null) {
                    pipeline.addLast("ssl", sslContext.newHandler(ch.alloc()));
                }
                
                // HTTP编解码器
                pipeline.addLast("http-codec", new HttpServerCodec());
                pipeline.addLast("http-aggregator", new HttpObjectAggregator(1024 * 1024));
                
                // 连接管理
                pipeline.addLast("connection", createConnectionHandler());
                
                // HTTP处理器
                pipeline.addLast("http-handler", new HttpInboundChannelHandler(proxyProcessor));
            }
        };
    }
}
```

### 5.2 创建自定义协议处理器

```java
public class HttpInboundHandler implements InboundHandler {
    private final ProxyProcessor proxyProcessor;
    private final InboundHandlerStatistics statistics = new InboundHandlerStatistics();

    public HttpInboundHandler(ProxyProcessor proxyProcessor) {
        this.proxyProcessor = proxyProcessor;
    }

    @Override
    public void handleInbound(Channel channel, Object message) {
        if (message instanceof FullHttpRequest) {
            FullHttpRequest httpRequest = (FullHttpRequest) message;
            statistics.incrementTotalRequests();
            
            try {
                // 解析HTTP请求
                String host = httpRequest.headers().get("Host");
                int port = 80; // 默认HTTP端口
                
                // 创建代理请求
                ProxyRequest proxyRequest = ProxyRequest.builder()
                        .protocol(ProxyRequest.Protocol.HTTP)
                        .target(host, port)
                        .clientChannel(channel)
                        .data(httpRequest.content())
                        .build();
                
                // 处理请求
                proxyProcessor.processRequest(proxyRequest)
                        .whenComplete((response, throwable) -> {
                            if (throwable != null) {
                                statistics.incrementFailedRequests();
                                sendHttpError(channel, HttpResponseStatus.INTERNAL_SERVER_ERROR);
                            } else {
                                statistics.incrementSuccessfulRequests();
                                // 处理HTTP响应
                            }
                        });
                        
            } catch (Exception e) {
                statistics.incrementFailedRequests();
                sendHttpError(channel, HttpResponseStatus.BAD_REQUEST);
            }
        }
    }

    @Override
    public boolean supports(String protocol) {
        return ProxyRequest.Protocol.HTTP.equals(protocol);
    }

    @Override
    public String getProtocolName() {
        return ProxyRequest.Protocol.HTTP;
    }

    @Override
    public InboundHandlerStatistics getStatistics() {
        return statistics;
    }

    private void sendHttpError(Channel channel, HttpResponseStatus status) {
        FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, status);
        channel.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
    }
}
```

## 6. 完整的应用示例

```java
public class ProxyServerApplication {
    private static final Logger logger = LoggerFactory.getLogger(ProxyServerApplication.class);

    public static void main(String[] args) {
        try {
            // 1. 创建核心组件
            Router router = new DefaultRouter();
            ProxyProcessor proxyProcessor = new ProxyProcessor(router);
            InboundServerManager serverManager = new InboundServerManager(proxyProcessor);

            // 2. 配置路由规则
            setupRoutes(router);

            // 3. 注册Outbound处理器
            setupOutbounds(proxyProcessor);

            // 4. 创建Inbound服务器
            setupInboundServers(serverManager);

            // 5. 启动所有服务器
            serverManager.startAll().get();
            logger.info("代理服务器启动完成");

            // 6. 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("正在关闭代理服务器...");
                try {
                    serverManager.stopAll().get();
                    proxyProcessor.shutdown();
                    logger.info("代理服务器关闭完成");
                } catch (Exception e) {
                    logger.error("关闭代理服务器时发生异常", e);
                }
            }));

            // 7. 启动监控任务
            startMonitoring(serverManager);

        } catch (Exception e) {
            logger.error("启动代理服务器失败", e);
            System.exit(1);
        }
    }

    private static void setupRoutes(Router router) {
        // 配置路由规则
        RouteRule directRule = new RouteRule("direct", "直连路由", 100, "direct");
        router.addRoute(directRule);
    }

    private static void setupOutbounds(ProxyProcessor proxyProcessor) {
        // 注册Outbound处理器
        DirectOutboundHandler directOutbound = new DirectOutboundHandler("direct", OutboundConfig.defaultConfig());
        proxyProcessor.registerOutboundHandler(directOutbound);
    }

    private static void setupInboundServers(InboundServerManager serverManager) {
        // 创建多路复用服务器
        serverManager.createMultiplexServer("multiplex-8080", 8080);
        serverManager.createHighPerformanceMultiplexServer("multiplex-high-8081", 8081);
        
        // 如果需要SSL
        // serverManager.createSslMultiplexServer("multiplex-ssl-8443", 8443, "keystore.jks", "password");
    }

    private static void startMonitoring(InboundServerManager serverManager) {
        // 启动监控任务
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        scheduler.scheduleWithFixedDelay(() -> {
            try {
                InboundServerManager.ManagerStatistics stats = serverManager.getStatistics();
                logger.info("服务器状态: {}", stats);
                
                InboundServerManager.HealthReport health = serverManager.getHealthReport();
                if (!health.isAllHealthy()) {
                    logger.warn("部分服务器不健康: {}", health);
                }
            } catch (Exception e) {
                logger.warn("监控任务执行异常", e);
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
}
```

## 总结

新的Inbound架构提供了：

1. **清晰的分层** - InboundServer负责网络层，InboundHandler负责协议层
2. **灵活的配置** - 支持各种网络和SSL配置
3. **统一的管理** - InboundServerManager统一管理多个服务器
4. **完善的监控** - 详细的统计信息和健康状态监控
5. **易于扩展** - 可以轻松添加新的协议和服务器类型

这个架构既保持了高性能，又提供了良好的可维护性和扩展性。