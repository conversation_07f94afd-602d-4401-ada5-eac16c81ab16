package com.proxy.client;

import com.proxy.client.config.ProxyClientConfigManager;
import com.proxy.client.connection.ConnectionManager;
import com.proxy.client.queue.QueuedConnectionManager;
import com.proxy.client.queue.ConnectionManagerFactory;
import com.proxy.client.queue.QueueMonitor;
import com.proxy.client.queue.PacketQueue;
import com.proxy.client.filter.AddressFilter;
import com.proxy.client.filter.DefaultAddressFilter;
import com.proxy.client.inbound.*;
import com.proxy.client.inbound.impl.HttpInbound;
import com.proxy.client.inbound.impl.Socks5Inbound;
import io.netty.channel.ChannelFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 代理客户端管理器
 * 管理多个代理接入组件的生命周期
 */
public class ProxyClientManager {
    private static final Logger logger = LoggerFactory.getLogger(ProxyClientManager.class);

    private final ProxyClientConfigManager configManager;
    private final QueuedConnectionManager queuedConnectionManager;
    private final AddressFilter addressFilter;
    private final List<ProxyInbound> inbounds;
    private final QueueMonitor queueMonitor;

    private volatile boolean running = false;

    public ProxyClientManager() {
        this.configManager = ProxyClientConfigManager.getInstance();
        this.addressFilter = new DefaultAddressFilter(configManager.getFilterMode());
        this.inbounds = new ArrayList<>();

        // 初始化队列化连接管理器
        this.queuedConnectionManager = ConnectionManagerFactory.getQueuedInstance(
                configManager.getProxyServerHost(),
                configManager.getProxyServerPort());

        // 初始化队列监控器
        this.queueMonitor = QueueMonitor.getInstance();
        this.queueMonitor.setQueuedConnectionManager(queuedConnectionManager);

        logger.info("ProxyClientManager初始化完成，使用队列化连接管理器");
    }

    /**
     * 获取队列化连接管理器（用于监控和统计）
     */
    public QueuedConnectionManager getQueuedConnectionManager() {
        return queuedConnectionManager;
    }

    /**
     * 添加代理接入组件
     */
    public ProxyClientManager addInbound(ProxyInbound inbound) {
        if (running) {
            throw new IllegalStateException("Cannot add inbound while manager is running");
        }
        inbounds.add(inbound);
        logger.info("添加代理接入组件: {} ({}:{})",
                inbound.getName(), inbound.getProtocol().getName(), inbound.getPort());
        return this;
    }

    /**
     * 添加SOCKS5接入组件
     */
    public ProxyClientManager addSocks5Inbound(int port) {
        return addInbound(new Socks5Inbound(port, queuedConnectionManager, addressFilter, configManager));
    }

    /**
     * 添加HTTP接入组件
     */
    public ProxyClientManager addHttpInbound(int port) {
        return addInbound(new HttpInbound(port, queuedConnectionManager, addressFilter, configManager));
    }

    /**
     * 启动所有组件
     */
    public CompletableFuture<Void> start() {
        if (running) {
            logger.warn("代理客户端管理器已经在运行中");
            return CompletableFuture.completedFuture(null);
        }

        if (inbounds.isEmpty()) {
            logger.warn("没有配置任何代理接入组件");
            return CompletableFuture.completedFuture(null);
        }

        logger.info("启动代理客户端管理器，共 {} 个组件", inbounds.size());

        return CompletableFuture.runAsync(() -> {
            try {
                // 启动连接管理器
                logger.info("启动连接管理器...");
                queuedConnectionManager.start();

                // 启动所有接入组件
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                for (ProxyInbound inbound : inbounds) {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            logger.info("启动 {} 组件...", inbound.getName());
                            ChannelFuture channelFuture = inbound.start();
                            if (channelFuture != null) {
                                channelFuture.sync();
                            }
                        } catch (Exception e) {
                            logger.error("启动 {} 组件失败: {}", inbound.getName(), e.getMessage(), e);
                            throw new RuntimeException(e);
                        }
                    });
                    futures.add(future);
                }

                // 等待所有组件启动完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                running = true;
                logger.info("代理客户端管理器启动成功");

                // 启动队列监控
                startQueueMonitoring();

                // 输出状态信息
                printStatus();

                // 添加关闭钩子
                Runtime.getRuntime().addShutdownHook(new Thread(this::stop));

            } catch (Exception e) {
                logger.error("启动代理客户端管理器失败: {}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 停止所有组件
     */
    public void stop() {
        if (!running) {
            logger.warn("代理客户端管理器未在运行");
            return;
        }

        logger.info("停止代理客户端管理器...");
        running = false;

        // 停止队列监控
        stopQueueMonitoring();

        // 停止所有接入组件
        for (ProxyInbound inbound : inbounds) {
            try {
                logger.info("停止 {} 组件...", inbound.getName());
                inbound.stop();
            } catch (Exception e) {
                logger.error("停止 {} 组件失败: {}", inbound.getName(), e.getMessage(), e);
            }
        }

        // 停止连接管理器
        try {
            logger.info("停止连接管理器...");
            queuedConnectionManager.stop();
        } catch (Exception e) {
            logger.error("停止连接管理器失败: {}", e.getMessage(), e);
        }

        logger.info("代理客户端管理器已停止");
    }

    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return running;
    }

    /**
     * 获取所有接入组件
     */
    public List<ProxyInbound> getInbounds() {
        return new ArrayList<>(inbounds);
    }

    /**
     * 根据协议类型获取接入组件
     */
    public List<ProxyInbound> getInboundsByProtocol(ProxyInbound.ProxyProtocol protocol) {
        return inbounds.stream()
                .filter(inbound -> inbound.getProtocol() == protocol)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 打印状态信息
     */
    public void printStatus() {
        logger.info("=== 代理客户端状态 ===");
        logger.info("运行状态: {}", running ? "运行中" : "已停止");
        logger.info("过滤模式: {}", configManager.getFilterMode());
        logger.info("代理服务器: {}:{}", ConnectionManager.getInstance().getProxyServerHost(), ConnectionManager.getInstance().getProxyServerPort());
        logger.info("认证状态: {}", configManager.isAuthEnabled() ? "启用" : "禁用");

        logger.info("接入组件 ({}):", inbounds.size());
        for (ProxyInbound inbound : inbounds) {
            ProxyInbound.InboundStatus status = inbound.getStatus();
            logger.info("  - {} ({}): 端口={}, 状态={}, 活跃连接={}, 总连接={}",
                    inbound.getName(),
                    inbound.getProtocol().getName(),
                    status.getPort(),
                    status.isRunning() ? "运行中" : "已停止",
                    status.getActiveConnections(),
                    status.getTotalConnections());
        }
        logger.info("=====================");

        // 打印队列状态
        printQueueStatus();
    }

    /**
     * 等待所有组件停止
     */
    public void awaitTermination() {
        while (running) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 等待指定时间或直到停止
     */
    public boolean awaitTermination(long timeout, TimeUnit unit) {
        long deadline = System.currentTimeMillis() + unit.toMillis(timeout);

        while (running && System.currentTimeMillis() < deadline) {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }

        return !running;
    }

    /**
     * 启动队列监控
     */
    private void startQueueMonitoring() {
        try {
            queueMonitor.start();
            logger.info("队列监控器启动成功");
        } catch (Exception e) {
            logger.error("启动队列监控器失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 停止队列监控
     */
    private void stopQueueMonitoring() {
        try {
            queueMonitor.stop();
            logger.info("队列监控器已停止");
        } catch (Exception e) {
            logger.error("停止队列监控器失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取队列统计信息
     */
    public PacketQueue.QueueStats getQueueStats() {
        return queuedConnectionManager.getQueueStats();
    }

    /**
     * 打印队列状态信息
     */
    public void printQueueStatus() {
        try {
            PacketQueue.QueueStats stats = getQueueStats();
            logger.info("=== 队列状态信息 ===");
            logger.info("队列运行状态: {}", stats.isRunning() ? "运行中" : "已停止");
            logger.info("当前队列大小: {}", stats.getQueueSize());
            logger.info("入队总数: {}", stats.getEnqueuedCount());
            logger.info("处理总数: {}", stats.getProcessedCount());
            logger.info("丢弃总数: {}", stats.getDroppedCount());

            // 计算成功率
            long totalProcessed = stats.getProcessedCount() + stats.getDroppedCount();
            double successRate = totalProcessed > 0 ? (double) stats.getProcessedCount() / totalProcessed * 100 : 100;
            logger.info("处理成功率: {:.2f}%", successRate);
            logger.info("==================");
        } catch (Exception e) {
            logger.error("获取队列状态信息失败: {}", e.getMessage(), e);
        }
    }
}
