# 商业化计划设计文档

## 概述

基于 netty_proxy_Multiplex 项目的商业化设计方案，将现有的高性能多路复用代理系统转化为完整的企业级商业产品。该设计充分利用现有项目的技术优势（性能提升50-80%、支持1000+并发客户端、连接复用率80%+），构建包含Web管理平台、RESTful API、用户管理、监控告警等完整功能的商业化产品。

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────────┐
│                    商业化代理平台架构                              │
├─────────────────────────────────────────────────────────────────┤
│  前端层 (Frontend Layer)                                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   管理控制台     │  │   用户门户      │  │   移动端应用     │ │
│  │   (Vue.js)      │  │   (React)      │  │   (Flutter)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  网关层 (Gateway Layer)                                         │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │           Nginx + API Gateway                               │ │
│  │  负载均衡 | SSL终止 | 限流 | 认证 | 监控                      │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Web管理服务   │  │   API服务       │  │   认证服务      │ │
│  │  (Spring Boot)  │  │  (Spring Boot)  │  │  (Spring Boot)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   监控服务      │  │   告警服务      │  │   计费服务      │ │
│  │  (Spring Boot)  │  │  (Spring Boot)  │  │  (Spring Boot)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  代理层 (Proxy Layer) - 基于现有 netty_proxy_Multiplex          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Proxy Server   │  │  Proxy Client   │  │  Load Balancer  │ │
│  │    (Netty)      │  │    (Netty)      │  │    (Netty)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   PostgreSQL    │  │     Redis       │  │   InfluxDB      │ │
│  │   (业务数据)     │  │   (缓存/会话)    │  │   (时序数据)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 微服务架构设计

```
┌─────────────────────────────────────────────────────────────────┐
│                      微服务架构图                                │
├─────────────────────────────────────────────────────────────────┤
│                    服务注册与发现 (Consul)                       │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   用户服务      │  │   代理服务      │  │   监控服务      │ │
│  │  User Service   │  │  Proxy Service  │  │Monitor Service  │ │
│  │                 │  │                 │  │                 │ │
│  │ • 用户管理      │  │ • 代理配置      │  │ • 指标收集      │ │
│  │ • 权限控制      │  │ • 连接管理      │  │ • 性能监控      │ │
│  │ • 认证授权      │  │ • 会话管理      │  │ • 告警处理      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   配置服务      │  │   日志服务      │  │   计费服务      │ │
│  │ Config Service  │  │  Log Service    │  │Billing Service  │ │
│  │                 │  │                 │  │                 │ │
│  │ • 配置管理      │  │ • 日志收集      │  │ • 使用统计      │ │
│  │ • 热更新        │  │ • 日志分析      │  │ • 计费规则      │ │
│  │ • 版本控制      │  │ • 日志搜索      │  │ • 账单生成      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   通知服务      │  │   文件服务      │  │   网关服务      │ │
│  │Notification Svc │  │  File Service   │  │ Gateway Service │ │
│  │                 │  │                 │  │                 │ │
│  │ • 邮件通知      │  │ • 文件上传      │  │ • 路由转发      │ │
│  │ • 短信通知      │  │ • 文件存储      │  │ • 限流控制      │ │
│  │ • 推送通知      │  │ • 文件下载      │  │ • 安全认证      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 组件设计

### 1. Web管理平台

#### 前端架构
```typescript
// 前端技术栈
interface FrontendStack {
  framework: 'Vue.js 3.x' | 'React 18.x';
  ui: 'Element Plus' | 'Ant Design';
  charts: 'ECharts' | 'Chart.js';
  state: 'Vuex' | 'Redux Toolkit';
  router: 'Vue Router' | 'React Router';
  http: 'Axios';
  websocket: 'Socket.io-client';
  build: 'Vite' | 'Webpack 5';
}

// 页面结构设计
interface PageStructure {
  dashboard: {
    realTimeMetrics: SystemMetricsComponent;
    connectionOverview: ConnectionOverviewComponent;
    performanceCharts: PerformanceChartsComponent;
    alertSummary: AlertSummaryComponent;
  };
  
  connectionManagement: {
    activeConnections: ConnectionListComponent;
    connectionPool: ConnectionPoolComponent;
    sessionDetails: SessionDetailsComponent;
  };
  
  monitoring: {
    realTimeCharts: RealTimeChartsComponent;
    historicalData: HistoricalDataComponent;
    alertManagement: AlertManagementComponent;
  };
  
  configuration: {
    serverConfig: ServerConfigComponent;
    clientConfig: ClientConfigComponent;
    securityConfig: SecurityConfigComponent;
  };
  
  userManagement: {
    userList: UserListComponent;
    roleManagement: RoleManagementComponent;
    permissionMatrix: PermissionMatrixComponent;
  };
}
```

#### 实时数据更新
```typescript
// WebSocket连接管理
class WebSocketManager {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  
  connect(url: string): void {
    this.ws = new WebSocket(url);
    
    this.ws.onopen = () => {
      console.log('WebSocket连接已建立');
      this.reconnectAttempts = 0;
    };
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
    
    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭');
      this.attemptReconnect();
    };
    
    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
  }
  
  private handleMessage(data: any): void {
    switch (data.type) {
      case 'METRICS_UPDATE':
        store.commit('updateMetrics', data.payload);
        break;
      case 'CONNECTION_EVENT':
        store.commit('updateConnections', data.payload);
        break;
      case 'ALERT_NOTIFICATION':
        store.commit('addAlert', data.payload);
        break;
    }
  }
  
  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++;
        this.connect(this.getWebSocketUrl());
      }, Math.pow(2, this.reconnectAttempts) * 1000);
    }
  }
}
```

### 2. RESTful API服务

#### API设计规范
```java
// 统一响应格式
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    private int code;
    private String message;
    private T data;
    private long timestamp;
    private String requestId;
    private PageInfo pagination;
    
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
            .code(200)
            .message("success")
            .data(data)
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
    }
    
    public static <T> ApiResponse<T> error(ErrorCode errorCode, String message) {
        return ApiResponse.<T>builder()
            .code(errorCode.getCode())
            .message(message)
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
    }
}

// 分页信息
@Data
@Builder
public class PageInfo {
    private int page;
    private int size;
    private long total;
    private int totalPages;
    private boolean hasNext;
    private boolean hasPrevious;
}
```

#### 核心API接口
```java
// 系统管理API
@RestController
@RequestMapping("/api/v1/system")
@Api(tags = "系统管理")
@Validated
public class SystemController {
    
    @GetMapping("/info")
    @ApiOperation("获取系统信息")
    public ApiResponse<SystemInfo> getSystemInfo() {
        SystemInfo info = systemService.getSystemInfo();
        return ApiResponse.success(info);
    }
    
    @GetMapping("/health")
    @ApiOperation("健康检查")
    public ApiResponse<HealthStatus> healthCheck() {
        HealthStatus status = systemService.checkHealth();
        return ApiResponse.success(status);
    }
    
    @PostMapping("/shutdown")
    @ApiOperation("优雅关闭")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> shutdown(
            @RequestParam(defaultValue = "30") @Min(1) @Max(300) int timeoutSeconds) {
        systemService.gracefulShutdown(timeoutSeconds);
        return ApiResponse.success(null);
    }
}

// 代理管理API
@RestController
@RequestMapping("/api/v1/proxy")
@Api(tags = "代理管理")
public class ProxyController {
    
    @GetMapping("/connections")
    @ApiOperation("获取连接列表")
    public ApiResponse<PageResponse<ConnectionInfo>> getConnections(
            @Valid ConnectionQueryRequest request) {
        PageResponse<ConnectionInfo> connections = proxyService.getConnections(request);
        return ApiResponse.success(connections);
    }
    
    @GetMapping("/connections/{connectionId}")
    @ApiOperation("获取连接详情")
    public ApiResponse<ConnectionDetail> getConnectionDetail(
            @PathVariable @NotBlank String connectionId) {
        ConnectionDetail detail = proxyService.getConnectionDetail(connectionId);
        return ApiResponse.success(detail);
    }
    
    @DeleteMapping("/connections/{connectionId}")
    @ApiOperation("断开连接")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> disconnectConnection(
            @PathVariable @NotBlank String connectionId) {
        proxyService.disconnectConnection(connectionId);
        return ApiResponse.success(null);
    }
    
    @GetMapping("/pool/status")
    @ApiOperation("获取连接池状态")
    public ApiResponse<ConnectionPoolStatus> getPoolStatus() {
        ConnectionPoolStatus status = proxyService.getConnectionPoolStatus();
        return ApiResponse.success(status);
    }
}
```

### 3. 用户认证与权限管理

#### 认证架构
```java
// JWT认证配置
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/api/v1/system/health").permitAll()
                .requestMatchers("/api/v1/system/info").hasRole("USER")
                .requestMatchers(HttpMethod.GET, "/api/v1/proxy/**").hasRole("USER")
                .requestMatchers(HttpMethod.POST, "/api/v1/proxy/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/api/v1/proxy/**").hasRole("ADMIN")
                .requestMatchers("/api/v1/config/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .exceptionHandling()
                .authenticationEntryPoint(jwtAuthenticationEntryPoint())
                .accessDeniedHandler(jwtAccessDeniedHandler())
            .and()
            .addFilterBefore(jwtRequestFilter(), UsernamePasswordAuthenticationFilter.class);
            
        return http.build();
    }
    
    @Bean
    public JwtTokenProvider jwtTokenProvider() {
        return new JwtTokenProvider(
            jwtProperties.getSecret(),
            jwtProperties.getExpiration(),
            jwtProperties.getRefreshExpiration()
        );
    }
}

// 用户服务
@Service
@Transactional
public class UserService {
    
    public LoginResponse login(LoginRequest request) {
        // 验证用户凭据
        User user = authenticateUser(request.getUsername(), request.getPassword());
        
        // 生成JWT令牌
        String accessToken = jwtTokenProvider.generateAccessToken(user);
        String refreshToken = jwtTokenProvider.generateRefreshToken(user);
        
        // 记录登录日志
        auditService.recordLogin(user, request.getClientIp());
        
        return LoginResponse.builder()
            .accessToken(accessToken)
            .refreshToken(refreshToken)
            .expiresIn(jwtProperties.getExpiration())
            .user(UserDto.from(user))
            .build();
    }
    
    public TokenResponse refreshToken(String refreshToken) {
        // 验证刷新令牌
        Claims claims = jwtTokenProvider.validateRefreshToken(refreshToken);
        String username = claims.getSubject();
        
        // 获取用户信息
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new UserNotFoundException("用户不存在"));
        
        // 生成新的访问令牌
        String newAccessToken = jwtTokenProvider.generateAccessToken(user);
        
        return TokenResponse.builder()
            .accessToken(newAccessToken)
            .expiresIn(jwtProperties.getExpiration())
            .build();
    }
}
```

#### 权限模型
```java
// 权限模型设计
@Entity
@Table(name = "users")
public class User {
    @Id
    private String id;
    private String username;
    private String password;
    private String email;
    private boolean enabled;
    private LocalDateTime createdAt;
    private LocalDateTime lastLoginAt;
    
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id"))
    private Set<Role> roles = new HashSet<>();
}

@Entity
@Table(name = "roles")
public class Role {
    @Id
    private String id;
    private String name;
    private String description;
    
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id"))
    private Set<Permission> permissions = new HashSet<>();
}

@Entity
@Table(name = "permissions")
public class Permission {
    @Id
    private String id;
    private String name;
    private String resource;
    private String action;
    private String description;
}

// 权限检查服务
@Service
public class PermissionService {
    
    public boolean hasPermission(String username, String resource, String action) {
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new UserNotFoundException("用户不存在"));
        
        return user.getRoles().stream()
            .flatMap(role -> role.getPermissions().stream())
            .anyMatch(permission -> 
                permission.getResource().equals(resource) && 
                permission.getAction().equals(action));
    }
    
    @PreAuthorize("@permissionService.hasPermission(authentication.name, 'proxy', 'manage')")
    public void manageProxy() {
        // 代理管理操作
    }
}
```

### 4. 监控告警系统

#### 监控数据收集
```java
// 指标收集器
@Component
public class MetricsCollector {
    
    private final MeterRegistry meterRegistry;
    private final Timer connectionTimer;
    private final Counter requestCounter;
    private final Gauge activeConnections;
    
    public MetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.connectionTimer = Timer.builder("proxy.connection.duration")
            .description("连接持续时间")
            .register(meterRegistry);
        this.requestCounter = Counter.builder("proxy.requests.total")
            .description("总请求数")
            .register(meterRegistry);
        this.activeConnections = Gauge.builder("proxy.connections.active")
            .description("活跃连接数")
            .register(meterRegistry, this, MetricsCollector::getActiveConnectionCount);
    }
    
    @EventListener
    public void handleConnectionEvent(ConnectionEvent event) {
        switch (event.getType()) {
            case CONNECTED:
                recordConnectionEstablished(event);
                break;
            case DISCONNECTED:
                recordConnectionClosed(event);
                break;
            case REQUEST:
                recordRequest(event);
                break;
        }
    }
    
    private void recordConnectionEstablished(ConnectionEvent event) {
        requestCounter.increment(
            Tags.of(
                "client_ip", event.getClientIp(),
                "protocol", event.getProtocol()
            )
        );
    }
    
    private void recordRequest(ConnectionEvent event) {
        Timer.Sample sample = Timer.start(meterRegistry);
        // 在请求完成时停止计时
        sample.stop(connectionTimer.tag("target_host", event.getTargetHost()));
    }
}

// 自定义指标
@Component
public class CustomMetrics {
    
    @Scheduled(fixedRate = 30000) // 每30秒收集一次
    public void collectCustomMetrics() {
        // 收集代理特定的指标
        ProxyMetrics metrics = proxyService.getMetrics();
        
        // 发送到时序数据库
        influxDBService.writeMetrics(
            Measurement.builder("proxy_metrics")
                .time(Instant.now(), WritePrecision.S)
                .addField("active_connections", metrics.getActiveConnections())
                .addField("total_sessions", metrics.getTotalSessions())
                .addField("multiplex_ratio", metrics.getMultiplexRatio())
                .addField("connection_pool_usage", metrics.getConnectionPoolUsage())
                .addField("filter_block_rate", metrics.getFilterBlockRate())
                .build()
        );
    }
}
```

#### 告警规则引擎
```java
// 告警规则定义
@Entity
@Table(name = "alert_rules")
public class AlertRule {
    @Id
    private String id;
    private String name;
    private String description;
    private String condition; // 告警条件表达式
    private AlertSeverity severity;
    private Duration duration; // 持续时间
    private boolean enabled;
    private String notificationChannels; // 通知渠道
    private Map<String, Object> parameters; // 规则参数
}

// 告警引擎
@Component
public class AlertEngine {
    
    private final ExpressionParser expressionParser = new SpelExpressionParser();
    private final StandardEvaluationContext evaluationContext = new StandardEvaluationContext();
    
    @EventListener
    public void handleMetricsUpdate(MetricsUpdateEvent event) {
        List<AlertRule> activeRules = alertRuleRepository.findByEnabledTrue();
        
        for (AlertRule rule : activeRules) {
            evaluateRule(rule, event.getMetrics());
        }
    }
    
    private void evaluateRule(AlertRule rule, Map<String, Object> metrics) {
        try {
            // 设置评估上下文
            evaluationContext.setVariables(metrics);
            
            // 解析并评估表达式
            Expression expression = expressionParser.parseExpression(rule.getCondition());
            Boolean result = expression.getValue(evaluationContext, Boolean.class);
            
            if (Boolean.TRUE.equals(result)) {
                handleAlertTriggered(rule, metrics);
            } else {
                handleAlertCleared(rule);
            }
        } catch (Exception e) {
            logger.error("评估告警规则失败: {}", rule.getName(), e);
        }
    }
    
    private void handleAlertTriggered(AlertRule rule, Map<String, Object> metrics) {
        String alertKey = rule.getId();
        AlertState state = alertStateManager.getState(alertKey);
        
        if (state == null) {
            // 首次触发
            state = AlertState.builder()
                .ruleId(rule.getId())
                .status(AlertStatus.PENDING)
                .firstTriggered(Instant.now())
                .build();
            alertStateManager.setState(alertKey, state);
        } else if (state.getStatus() == AlertStatus.PENDING) {
            // 检查持续时间
            Duration elapsed = Duration.between(state.getFirstTriggered(), Instant.now());
            if (elapsed.compareTo(rule.getDuration()) >= 0) {
                triggerAlert(rule, metrics, state);
            }
        }
    }
    
    private void triggerAlert(AlertRule rule, Map<String, Object> metrics, AlertState state) {
        Alert alert = Alert.builder()
            .id(UUID.randomUUID().toString())
            .ruleId(rule.getId())
            .ruleName(rule.getName())
            .severity(rule.getSeverity())
            .message(generateAlertMessage(rule, metrics))
            .timestamp(Instant.now())
            .metrics(metrics)
            .build();
        
        // 发送通知
        notificationService.sendAlert(alert);
        
        // 更新状态
        state.setStatus(AlertStatus.FIRING);
        state.setLastFired(Instant.now());
        alertStateManager.setState(rule.getId(), state);
        
        // 保存告警记录
        alertRepository.save(alert);
    }
}
```

### 5. 商业化功能模块

#### 用户管理与计费
```java
// 用户套餐模型
@Entity
@Table(name = "user_plans")
public class UserPlan {
    @Id
    private String id;
    private String userId;
    private PlanType planType; // FREE, BASIC, PREMIUM, ENTERPRISE
    private long monthlyTrafficLimit; // 月流量限制(字节)
    private int maxConcurrentConnections; // 最大并发连接数
    private boolean sslSupport; // SSL支持
    private boolean advancedFiltering; // 高级过滤
    private boolean prioritySupport; // 优先支持
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private boolean active;
}

// 使用统计服务
@Service
public class UsageStatisticsService {
    
    @EventListener
    public void recordTrafficUsage(TrafficEvent event) {
        String userId = event.getUserId();
        long bytesTransferred = event.getBytesTransferred();
        
        // 更新用户流量使用统计
        UsageRecord record = UsageRecord.builder()
            .userId(userId)
            .date(LocalDate.now())
            .bytesTransferred(bytesTransferred)
            .requestCount(1)
            .build();
        
        usageRepository.save(record);
        
        // 检查是否超出限制
        checkUsageLimits(userId);
    }
    
    private void checkUsageLimits(String userId) {
        UserPlan plan = userPlanRepository.findByUserIdAndActiveTrue(userId)
            .orElseThrow(() -> new PlanNotFoundException("用户套餐不存在"));
        
        // 获取当月使用量
        long monthlyUsage = getMonthlyUsage(userId);
        
        if (monthlyUsage > plan.getMonthlyTrafficLimit()) {
            // 超出限制，暂停服务或降级
            handleUsageLimitExceeded(userId, plan);
        } else if (monthlyUsage > plan.getMonthlyTrafficLimit() * 0.8) {
            // 接近限制，发送警告
            sendUsageWarning(userId, plan, monthlyUsage);
        }
    }
    
    public UsageReport generateUsageReport(String userId, LocalDate startDate, LocalDate endDate) {
        List<UsageRecord> records = usageRepository.findByUserIdAndDateBetween(
            userId, startDate, endDate);
        
        return UsageReport.builder()
            .userId(userId)
            .startDate(startDate)
            .endDate(endDate)
            .totalBytes(records.stream().mapToLong(UsageRecord::getBytesTransferred).sum())
            .totalRequests(records.stream().mapToLong(UsageRecord::getRequestCount).sum())
            .dailyUsage(groupByDate(records))
            .topHosts(getTopHosts(records))
            .build();
    }
}

// 计费服务
@Service
public class BillingService {
    
    @Scheduled(cron = "0 0 1 * * ?") // 每月1号执行
    public void generateMonthlyBills() {
        List<User> activeUsers = userRepository.findByEnabledTrue();
        
        for (User user : activeUsers) {
            generateBill(user, LocalDate.now().minusMonths(1));
        }
    }
    
    private void generateBill(User user, LocalDate billingMonth) {
        UserPlan plan = userPlanRepository.findByUserIdAndActiveTrue(user.getId())
            .orElse(getDefaultPlan());
        
        UsageReport usage = usageStatisticsService.generateUsageReport(
            user.getId(), 
            billingMonth.withDayOfMonth(1),
            billingMonth.withDayOfMonth(billingMonth.lengthOfMonth())
        );
        
        BigDecimal amount = calculateBillAmount(plan, usage);
        
        Bill bill = Bill.builder()
            .id(UUID.randomUUID().toString())
            .userId(user.getId())
            .billingMonth(billingMonth)
            .planType(plan.getPlanType())
            .baseAmount(plan.getMonthlyPrice())
            .usageAmount(calculateUsageAmount(usage))
            .totalAmount(amount)
            .status(BillStatus.PENDING)
            .createdAt(LocalDateTime.now())
            .dueDate(LocalDate.now().plusDays(30))
            .build();
        
        billRepository.save(bill);
        
        // 发送账单通知
        notificationService.sendBillNotification(user, bill);
    }
}
```

## 数据模型

### 核心实体设计

```java
// 用户实体
@Entity
@Table(name = "users")
public class User {
    @Id
    private String id;
    private String username;
    private String email;
    private String password;
    private String firstName;
    private String lastName;
    private String company;
    private String phone;
    private UserStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastLoginAt;
    
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL)
    private List<UserPlan> plans = new ArrayList<>();
    
    @ManyToMany(fetch = FetchType.EAGER)
    private Set<Role> roles = new HashSet<>();
}

// 连接信息实体
@Entity
@Table(name = "connections")
public class Connection {
    @Id
    private String id;
    private String userId;
    private String clientIp;
    private String serverIp;
    private int serverPort;
    private String protocol;
    private ConnectionStatus status;
    private LocalDateTime connectedAt;
    private LocalDateTime disconnectedAt;
    private long bytesReceived;
    private long bytesSent;
    private int sessionCount;
    
    @OneToMany(mappedBy = "connection", cascade = CascadeType.ALL)
    private List<Session> sessions = new ArrayList<>();
}

// 会话信息实体
@Entity
@Table(name = "sessions")
public class Session {
    @Id
    private String id;
    private String connectionId;
    private String targetHost;
    private int targetPort;
    private String protocol;
    private SessionStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime closedAt;
    private long bytesReceived;
    private long bytesSent;
    private Duration duration;
    
    @ManyToOne
    @JoinColumn(name = "connection_id")
    private Connection connection;
}

// 使用记录实体
@Entity
@Table(name = "usage_records")
public class UsageRecord {
    @Id
    private String id;
    private String userId;
    private LocalDate date;
    private long bytesTransferred;
    private long requestCount;
    private long sessionCount;
    private Duration totalDuration;
    private Map<String, Long> protocolUsage;
    private Map<String, Long> hostUsage;
}
```

## 错误处理

### 统一异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ApiResponse<Void>> handleValidationException(ValidationException e) {
        return ResponseEntity.badRequest()
            .body(ApiResponse.error(ErrorCode.VALIDATION_ERROR, e.getMessage()));
    }
    
    @ExceptionHandler(UserNotFoundException.class)
    public ResponseEntity<ApiResponse<Void>> handleUserNotFoundException(UserNotFoundException e) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(ApiResponse.error(ErrorCode.USER_NOT_FOUND, e.getMessage()));
    }
    
    @ExceptionHandler(InsufficientPermissionException.class)
    public ResponseEntity<ApiResponse<Void>> handleInsufficientPermissionException(
            InsufficientPermissionException e) {
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
            .body(ApiResponse.error(ErrorCode.INSUFFICIENT_PERMISSION, e.getMessage()));
    }
    
    @ExceptionHandler(ProxyServiceException.class)
    public ResponseEntity<ApiResponse<Void>> handleProxyServiceException(ProxyServiceException e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponse.error(ErrorCode.PROXY_SERVICE_ERROR, e.getMessage()));
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Void>> handleGenericException(Exception e) {
        logger.error("未处理的异常", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponse.error(ErrorCode.INTERNAL_ERROR, "系统内部错误"));
    }
}

// 错误码定义
public enum ErrorCode {
    SUCCESS(200, "成功"),
    VALIDATION_ERROR(400, "参数验证失败"),
    UNAUTHORIZED(401, "未授权"),
    INSUFFICIENT_PERMISSION(403, "权限不足"),
    USER_NOT_FOUND(404, "用户不存在"),
    PROXY_SERVICE_ERROR(500, "代理服务错误"),
    INTERNAL_ERROR(500, "系统内部错误");
    
    private final int code;
    private final String message;
}
```

## 测试策略

### 测试架构
```java
// 单元测试
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private JwtTokenProvider jwtTokenProvider;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    void shouldLoginSuccessfully() {
        // Given
        LoginRequest request = new LoginRequest("testuser", "password");
        User user = createTestUser();
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(user));
        when(jwtTokenProvider.generateAccessToken(user)).thenReturn("access-token");
        
        // When
        LoginResponse response = userService.login(request);
        
        // Then
        assertThat(response.getAccessToken()).isEqualTo("access-token");
        assertThat(response.getUser().getUsername()).isEqualTo("testuser");
    }
}

// 集成测试
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class ProxyControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldGetConnectionsWithValidToken() {
        // Given
        String token = createTestUserAndGetToken();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        
        // When
        ResponseEntity<ApiResponse> response = restTemplate.exchange(
            "/api/v1/proxy/connections",
            HttpMethod.GET,
            new HttpEntity<>(headers),
            ApiResponse.class
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getCode()).isEqualTo(200);
    }
}

// 性能测试
@Component
public class PerformanceTest {
    
    @Test
    void shouldHandleHighConcurrency() throws InterruptedException {
        int threadCount = 100;
        int requestsPerThread = 100;
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < requestsPerThread; j++) {
                        try {
                            // 执行API调用
                            ResponseEntity<ApiResponse> response = restTemplate.getForEntity(
                                "/api/v1/system/health", ApiResponse.class);
                            if (response.getStatusCode().is2xxSuccessful()) {
                                successCount.incrementAndGet();
                            } else {
                                errorCount.incrementAndGet();
                            }
                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await(60, TimeUnit.SECONDS);
        
        int totalRequests = threadCount * requestsPerThread;
        double successRate = (double) successCount.get() / totalRequests * 100;
        
        assertThat(successRate).isGreaterThan(95.0);
        logger.info("性能测试结果: 总请求数={}, 成功数={}, 失败数={}, 成功率={}%",
            totalRequests, successCount.get(), errorCount.get(), successRate);
    }
}
```

这个设计文档详细描述了基于 netty_proxy_Multiplex 项目的商业化平台架构，包含了Web管理平台、RESTful API、用户管理、监控告警、商业化功能等完整的企业级功能模块。设计充分利用了现有项目的技术优势，构建了一个可扩展、高性能、易维护的商业化产品架构。