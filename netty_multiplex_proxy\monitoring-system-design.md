# 监控和告警系统设计

## 1. 监控指标体系

### 系统级指标
```java
@Component
public class SystemMetricsCollector {
    
    // JVM指标
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    private final OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
    private final RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
    
    @Scheduled(fixedRate = 5000) // 每5秒收集一次
    public void collectSystemMetrics() {
        SystemMetrics metrics = SystemMetrics.builder()
            .timestamp(System.currentTimeMillis())
            .cpuUsage(getCpuUsage())
            .memoryUsage(getMemoryUsage())
            .heapMemory(getHeapMemoryUsage())
            .nonHeapMemory(getNonHeapMemoryUsage())
            .threadCount(getThreadCount())
            .gcCount(getGcCount())
            .gcTime(getGcTime())
            .uptime(runtimeBean.getUptime())
            .build();
            
        metricsRepository.save(metrics);
        eventPublisher.publishEvent(new MetricsUpdatedEvent(metrics));
    }
    
    private double getCpuUsage() {
        if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
            return ((com.sun.management.OperatingSystemMXBean) osBean).getProcessCpuLoad() * 100;
        }
        return -1;
    }
    
    private MemoryUsage getMemoryUsage() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        return MemoryUsage.builder()
            .used(heapUsage.getUsed())
            .max(heapUsage.getMax())
            .committed(heapUsage.getCommitted())
            .usagePercent((double) heapUsage.getUsed() / heapUsage.getMax() * 100)
            .build();
    }
}
```

### 网络级指标
```java
@Component
public class NetworkMetricsCollector {
    
    private final AtomicLong totalBytesReceived = new AtomicLong(0);
    private final AtomicLong totalBytesSent = new AtomicLong(0);
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong failedConnections = new AtomicLong(0);
    
    // 连接延迟统计
    private final Histogram connectionLatency = Histogram.create()
        .name("connection_latency_seconds")
        .help("Connection establishment latency")
        .buckets(0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0)
        .register();
    
    // 请求处理时间统计
    private final Histogram requestDuration = Histogram.create()
        .name("request_duration_seconds")
        .help("Request processing duration")
        .buckets(0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0)
        .register();
    
    public void recordConnectionEstablished(long latencyMs) {
        totalConnections.incrementAndGet();
        activeConnections.incrementAndGet();
        connectionLatency.observe(latencyMs / 1000.0);
    }
    
    public void recordConnectionClosed() {
        activeConnections.decrementAndGet();
    }
    
    public void recordConnectionFailed() {
        failedConnections.incrementAndGet();
    }
    
    public void recordBytesTransferred(long received, long sent) {
        totalBytesReceived.addAndGet(received);
        totalBytesSent.addAndGet(sent);
    }
    
    @Scheduled(fixedRate = 10000) // 每10秒收集一次
    public void collectNetworkMetrics() {
        NetworkMetrics metrics = NetworkMetrics.builder()
            .timestamp(System.currentTimeMillis())
            .totalConnections(totalConnections.get())
            .activeConnections(activeConnections.get())
            .failedConnections(failedConnections.get())
            .totalBytesReceived(totalBytesReceived.get())
            .totalBytesSent(totalBytesSent.get())
            .connectionSuccessRate(calculateSuccessRate())
            .averageLatency(connectionLatency.get().mean)
            .p95Latency(connectionLatency.get().quantile(0.95))
            .p99Latency(connectionLatency.get().quantile(0.99))
            .build();
            
        metricsRepository.save(metrics);
        checkAlertConditions(metrics);
    }
}
```

### 业务级指标
```java
@Component
public class BusinessMetricsCollector {
    
    private final Map<String, AtomicLong> hostRequestCounts = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> protocolCounts = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> geoLocationCounts = new ConcurrentHashMap<>();
    
    // 会话统计
    private final AtomicLong totalSessions = new AtomicLong(0);
    private final AtomicLong activeSessions = new AtomicLong(0);
    private final AtomicLong multiplexedSessions = new AtomicLong(0);
    
    // 过滤统计
    private final AtomicLong blockedRequests = new AtomicLong(0);
    private final AtomicLong whitelistHits = new AtomicLong(0);
    private final AtomicLong blacklistHits = new AtomicLong(0);
    
    public void recordSessionCreated(String protocol, String targetHost) {
        totalSessions.incrementAndGet();
        activeSessions.incrementAndGet();
        
        protocolCounts.computeIfAbsent(protocol, k -> new AtomicLong(0)).incrementAndGet();
        hostRequestCounts.computeIfAbsent(targetHost, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    public void recordSessionClosed() {
        activeSessions.decrementAndGet();
    }
    
    public void recordMultiplexedSession() {
        multiplexedSessions.incrementAndGet();
    }
    
    public void recordFilterAction(FilterAction action, String reason) {
        switch (action) {
            case BLOCKED:
                blockedRequests.incrementAndGet();
                break;
            case WHITELIST_HIT:
                whitelistHits.incrementAndGet();
                break;
            case BLACKLIST_HIT:
                blacklistHits.incrementAndGet();
                break;
        }
    }
    
    @Scheduled(fixedRate = 30000) // 每30秒收集一次
    public void collectBusinessMetrics() {
        BusinessMetrics metrics = BusinessMetrics.builder()
            .timestamp(System.currentTimeMillis())
            .totalSessions(totalSessions.get())
            .activeSessions(activeSessions.get())
            .multiplexedSessions(multiplexedSessions.get())
            .multiplexRatio(calculateMultiplexRatio())
            .topHosts(getTopHosts(10))
            .protocolDistribution(getProtocolDistribution())
            .blockedRequests(blockedRequests.get())
            .whitelistHits(whitelistHits.get())
            .blacklistHits(blacklistHits.get())
            .filterEffectiveness(calculateFilterEffectiveness())
            .build();
            
        metricsRepository.save(metrics);
    }
}
```

## 2. 告警系统设计

### 告警规则引擎
```java
@Component
public class AlertRuleEngine {
    
    private final List<AlertRule> alertRules = new ArrayList<>();
    private final AlertNotificationService notificationService;
    
    @PostConstruct
    public void initializeDefaultRules() {
        // CPU使用率告警
        alertRules.add(AlertRule.builder()
            .name("高CPU使用率")
            .condition("cpu_usage > 80")
            .severity(AlertSeverity.WARNING)
            .duration(Duration.ofMinutes(5))
            .build());
            
        // 内存使用率告警
        alertRules.add(AlertRule.builder()
            .name("高内存使用率")
            .condition("memory_usage > 85")
            .severity(AlertSeverity.WARNING)
            .duration(Duration.ofMinutes(3))
            .build());
            
        // 连接失败率告警
        alertRules.add(AlertRule.builder()
            .name("连接失败率过高")
            .condition("connection_failure_rate > 10")
            .severity(AlertSeverity.CRITICAL)
            .duration(Duration.ofMinutes(2))
            .build());
            
        // 响应延迟告警
        alertRules.add(AlertRule.builder()
            .name("响应延迟过高")
            .condition("p95_latency > 5000")
            .severity(AlertSeverity.WARNING)
            .duration(Duration.ofMinutes(5))
            .build());
    }
    
    @EventListener
    public void handleMetricsUpdate(MetricsUpdatedEvent event) {
        for (AlertRule rule : alertRules) {
            evaluateRule(rule, event.getMetrics());
        }
    }
    
    private void evaluateRule(AlertRule rule, Object metrics) {
        boolean conditionMet = evaluateCondition(rule.getCondition(), metrics);
        
        if (conditionMet) {
            handleAlertConditionMet(rule, metrics);
        } else {
            handleAlertConditionCleared(rule);
        }
    }
    
    private void handleAlertConditionMet(AlertRule rule, Object metrics) {
        String alertKey = rule.getName();
        AlertState currentState = alertStateManager.getState(alertKey);
        
        if (currentState == null) {
            // 首次触发，开始计时
            alertStateManager.setState(alertKey, AlertState.builder()
                .rule(rule)
                .firstTriggered(Instant.now())
                .status(AlertStatus.PENDING)
                .build());
        } else if (currentState.getStatus() == AlertStatus.PENDING) {
            // 检查是否达到持续时间要求
            Duration elapsed = Duration.between(currentState.getFirstTriggered(), Instant.now());
            if (elapsed.compareTo(rule.getDuration()) >= 0) {
                // 触发告警
                triggerAlert(rule, metrics, currentState);
            }
        }
    }
    
    private void triggerAlert(AlertRule rule, Object metrics, AlertState state) {
        Alert alert = Alert.builder()
            .id(UUID.randomUUID().toString())
            .ruleName(rule.getName())
            .severity(rule.getSeverity())
            .message(generateAlertMessage(rule, metrics))
            .timestamp(Instant.now())
            .metrics(metrics)
            .build();
            
        // 发送通知
        notificationService.sendAlert(alert);
        
        // 更新状态
        state.setStatus(AlertStatus.FIRING);
        state.setLastFired(Instant.now());
        alertStateManager.setState(rule.getName(), state);
        
        // 记录告警历史
        alertHistoryRepository.save(alert);
    }
}
```

### 通知服务
```java
@Service
public class AlertNotificationService {
    
    private final List<NotificationChannel> channels = new ArrayList<>();
    
    @PostConstruct
    public void initializeChannels() {
        // 邮件通知
        if (configManager.isEmailNotificationEnabled()) {
            channels.add(new EmailNotificationChannel(emailService));
        }
        
        // 钉钉通知
        if (configManager.isDingTalkNotificationEnabled()) {
            channels.add(new DingTalkNotificationChannel(dingTalkService));
        }
        
        // 企业微信通知
        if (configManager.isWeChatWorkNotificationEnabled()) {
            channels.add(new WeChatWorkNotificationChannel(weChatWorkService));
        }
        
        // Webhook通知
        if (configManager.isWebhookNotificationEnabled()) {
            channels.add(new WebhookNotificationChannel(httpClient));
        }
    }
    
    public void sendAlert(Alert alert) {
        for (NotificationChannel channel : channels) {
            try {
                if (channel.shouldSendAlert(alert)) {
                    channel.sendAlert(alert);
                }
            } catch (Exception e) {
                logger.error("发送告警通知失败: {}", channel.getClass().getSimpleName(), e);
            }
        }
    }
}

// 邮件通知实现
@Component
public class EmailNotificationChannel implements NotificationChannel {
    
    @Override
    public void sendAlert(Alert alert) {
        String subject = String.format("[%s] %s", alert.getSeverity(), alert.getRuleName());
        String body = generateEmailBody(alert);
        
        emailService.sendEmail(
            configManager.getAlertEmailRecipients(),
            subject,
            body
        );
    }
    
    private String generateEmailBody(Alert alert) {
        return String.format("""
            告警规则: %s
            告警级别: %s
            触发时间: %s
            告警信息: %s
            
            详细指标:
            %s
            
            请及时处理！
            """,
            alert.getRuleName(),
            alert.getSeverity(),
            alert.getTimestamp(),
            alert.getMessage(),
            formatMetrics(alert.getMetrics())
        );
    }
}
```

## 3. 性能基线和异常检测

### 性能基线建立
```java
@Component
public class PerformanceBaselineService {
    
    private final Map<String, BaselineMetrics> baselines = new ConcurrentHashMap<>();
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点更新基线
    public void updateBaselines() {
        // 获取过去7天的历史数据
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(7);
        
        List<MetricsData> historicalData = metricsRepository.findByTimestampBetween(
            startTime, endTime);
        
        // 计算各项指标的基线值
        BaselineMetrics baseline = calculateBaseline(historicalData);
        baselines.put("default", baseline);
        
        logger.info("性能基线已更新: {}", baseline);
    }
    
    private BaselineMetrics calculateBaseline(List<MetricsData> data) {
        // 使用统计学方法计算基线
        DoubleSummaryStatistics cpuStats = data.stream()
            .mapToDouble(MetricsData::getCpuUsage)
            .summaryStatistics();
            
        DoubleSummaryStatistics memoryStats = data.stream()
            .mapToDouble(MetricsData::getMemoryUsage)
            .summaryStatistics();
            
        DoubleSummaryStatistics latencyStats = data.stream()
            .mapToDouble(MetricsData::getAverageLatency)
            .summaryStatistics();
        
        return BaselineMetrics.builder()
            .cpuUsageBaseline(cpuStats.getAverage())
            .cpuUsageStdDev(calculateStandardDeviation(data, MetricsData::getCpuUsage))
            .memoryUsageBaseline(memoryStats.getAverage())
            .memoryUsageStdDev(calculateStandardDeviation(data, MetricsData::getMemoryUsage))
            .latencyBaseline(latencyStats.getAverage())
            .latencyStdDev(calculateStandardDeviation(data, MetricsData::getAverageLatency))
            .updatedAt(LocalDateTime.now())
            .build();
    }
    
    public boolean isAnomalous(String metric, double value) {
        BaselineMetrics baseline = baselines.get("default");
        if (baseline == null) return false;
        
        double baselineValue = getBaselineValue(baseline, metric);
        double stdDev = getStandardDeviation(baseline, metric);
        
        // 使用3-sigma规则检测异常
        double threshold = 3.0;
        return Math.abs(value - baselineValue) > threshold * stdDev;
    }
}
```

### 异常检测算法
```java
@Component
public class AnomalyDetectionService {
    
    // 移动平均异常检测
    public boolean detectMovingAverageAnomaly(List<Double> values, int windowSize, double threshold) {
        if (values.size() < windowSize + 1) return false;
        
        double currentValue = values.get(values.size() - 1);
        double movingAverage = values.subList(values.size() - windowSize - 1, values.size() - 1)
            .stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        
        return Math.abs(currentValue - movingAverage) > threshold * movingAverage;
    }
    
    // 指数加权移动平均异常检测
    public boolean detectEWMAAnomaly(List<Double> values, double alpha, double threshold) {
        if (values.size() < 2) return false;
        
        double ewma = values.get(0);
        for (int i = 1; i < values.size() - 1; i++) {
            ewma = alpha * values.get(i) + (1 - alpha) * ewma;
        }
        
        double currentValue = values.get(values.size() - 1);
        return Math.abs(currentValue - ewma) > threshold * ewma;
    }
    
    // 季节性异常检测
    public boolean detectSeasonalAnomaly(List<Double> values, int seasonLength, double threshold) {
        if (values.size() < seasonLength * 2) return false;
        
        double currentValue = values.get(values.size() - 1);
        
        // 获取同一季节位置的历史值
        List<Double> seasonalValues = new ArrayList<>();
        for (int i = values.size() - 1 - seasonLength; i >= 0; i -= seasonLength) {
            seasonalValues.add(values.get(i));
        }
        
        double seasonalAverage = seasonalValues.stream()
            .mapToDouble(Double::doubleValue).average().orElse(0.0);
        
        return Math.abs(currentValue - seasonalAverage) > threshold * seasonalAverage;
    }
}
```