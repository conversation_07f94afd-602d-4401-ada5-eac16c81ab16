package com.xiang.traffic.server.db.vo;

import com.xiang.traffic.server.core.token.IUser;

/**
 * <AUTHOR>
 * @date 2024/2/6 21:37
 */
public class SimpleUserVo implements IUser {
    private String username;
    private int readLimit;
    private int writeLimit;

    public SimpleUserVo() {
        this.username = "anonymous";
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public int getWriteLimit() {
        return this.writeLimit;
    }

    @Override
    public int getReadLimit() {
        return this.readLimit;
    }

    public void setReadLimit(int readLimit) {
        this.readLimit = readLimit;
    }

    public void setWriteLimit(int writeLimit) {
        this.writeLimit = writeLimit;
    }
}
