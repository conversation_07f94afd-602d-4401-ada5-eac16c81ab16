package com.proxy.client.config;

import com.proxy.client.config.binder.ConfigurationBinder;
import com.proxy.client.config.properties.ProxyClientProperties;
import com.proxy.client.filter.AddressFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.yaml.snakeyaml.Yaml;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代理客户端配置管理器
 * 使用类似 Spring Boot 的配置绑定方式
 * 支持外部配置文件加载
 */
public class ProxyClientConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(ProxyClientConfigManager.class);

    // 默认配置文件路径
    private static final String DEFAULT_CONFIG_FILE = "configs/development/proxy-client.yml";

    // 配置文件路径的环境变量名
    private static final String CONFIG_FILE_ENV = "PROXY_CLIENT_CONFIG";

    // 配置文件路径的系统属性名
    private static final String CONFIG_FILE_PROPERTY = "proxy.client.config";

    private static final String CONFIG_NAME = "app.properties";

    private static ProxyClientConfigManager instance;
    private ProxyClientProperties properties;
    private static String configFilePath;
    
    private ProxyClientConfigManager() {
        loadConfiguration();
    }

    public static synchronized ProxyClientConfigManager getInstance() {
        if (instance == null) {
            instance = new ProxyClientConfigManager();
        }
        return instance;
    }

    /**
     * 设置配置文件路径（用于命令行参数指定）
     */
    public static void setConfigFilePath(String path) {
        configFilePath = path;
    }
    
    /**
     * 加载配置
     */
    private void loadConfiguration() {
        // 确定配置文件路径
        String configPath = determineConfigFilePath();

        // 尝试加载 YAML 配置文件
        if (loadYamlConfiguration(configPath)) {
            return;
        }

        // 如果 YAML 配置文件不存在，使用默认配置
        logger.info("未找到 YAML 配置文件，使用默认配置");
        properties = new ProxyClientProperties();
        initializeDefaultConfiguration();
    }

    /**
     * 确定配置文件路径
     * 优先级：命令行参数 > 系统属性 > 环境变量 > properties文件 > 默认路径
     */
    private String determineConfigFilePath() {
        // 1. 命令行参数指定的路径（通过 setConfigFilePath 设置）
        if (configFilePath != null && !configFilePath.trim().isEmpty()) {
            logger.info("使用命令行参数指定的配置文件: {}", configFilePath);
            return configFilePath;
        }

        // 2. 系统属性指定的路径
        String systemProperty = System.getProperty(CONFIG_FILE_PROPERTY);
        if (systemProperty != null && !systemProperty.trim().isEmpty()) {
            logger.info("使用系统属性指定的配置文件: {}", systemProperty);
            return systemProperty;
        }

        // 3. 环境变量指定的路径
        String envVariable = System.getenv(CONFIG_FILE_ENV);
        if (envVariable != null && !envVariable.trim().isEmpty()) {
            logger.info("使用环境变量指定的配置文件: {}", envVariable);
            return envVariable;
        }

        // 4. 从properties文件读取配置路径
        String propertiesPath = loadConfigPathFromProperties();
        if (propertiesPath != null && !propertiesPath.trim().isEmpty()) {
            logger.info("使用properties文件指定的配置文件: {}", propertiesPath);
            return propertiesPath;
        }

        // 5. 默认路径
        logger.info("使用默认配置文件路径: {}", DEFAULT_CONFIG_FILE);
        return DEFAULT_CONFIG_FILE;
    }

    /**
     * 从properties文件中加载配置文件路径
     */
    private String loadConfigPathFromProperties() {
        Properties props = new Properties();

        // 尝试加载app.properties
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(CONFIG_NAME)) {
            if (is != null) {
                props.load(is);

                // 优先检查配置目录
                String configDir = props.getProperty("config.ext.dir");
                if (configDir != null && !configDir.trim().isEmpty()) {
                    String configPath = findConfigFileInDirectory(configDir.trim());
                    if (configPath != null) {
                        return configPath;
                    }
                }

                // 获取配置文件路径
                String configPath = props.getProperty("config.file.path");
                if (configPath != null && !configPath.trim().isEmpty()) {
                    return configPath.trim();
                }
            }
        } catch (Exception e) {
            logger.warn("读取application.properties文件失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 在指定目录中查找配置文件
     * 按优先级查找：proxy-client.yml > proxy-client-dev-*.yml > 其他.yml文件
     */
    private String findConfigFileInDirectory(String configDir) {
        File dir = new File(configDir);
        if (!dir.exists() || !dir.isDirectory()) {
            logger.warn("配置目录不存在或不是目录: {}", configDir);
            return null;
        }

        File[] files = dir.listFiles((file, name) -> name.endsWith(".yml") || name.endsWith(".yaml"));
        if (files == null || files.length == 0) {
            logger.warn("配置目录中未找到YAML配置文件: {}", configDir);
            return null;
        }

        // 按优先级排序查找配置文件
        String[] priorities = {
            "proxy-client.yml",
            "proxy-client.yaml"
        };

        // 1. 首先查找主配置文件
        for (String priority : priorities) {
            File configFile = new File(dir, priority);
            if (configFile.exists() && configFile.isFile()) {
                logger.info("在配置目录中找到主配置文件: {}", configFile.getAbsolutePath());
                return configFile.getAbsolutePath();
            }
        }

        // 2. 查找开发环境配置文件
        for (File file : files) {
            String fileName = file.getName();
            if (fileName.startsWith("proxy-client-dev-") &&
                (fileName.endsWith(".yml") || fileName.endsWith(".yaml"))) {
                logger.info("在配置目录中找到开发环境配置文件: {}", file.getAbsolutePath());
                return file.getAbsolutePath();
            }
        }

        // 3. 查找任何包含proxy-client的配置文件
        for (File file : files) {
            String fileName = file.getName();
            if (fileName.contains("proxy-client") &&
                (fileName.endsWith(".yml") || fileName.endsWith(".yaml"))) {
                logger.info("在配置目录中找到配置文件: {}", file.getAbsolutePath());
                return file.getAbsolutePath();
            }
        }

        // 4. 如果都没找到，使用第一个YAML文件
        File firstYamlFile = files[0];
        logger.info("在配置目录中使用第一个YAML文件: {}", firstYamlFile.getAbsolutePath());
        return firstYamlFile.getAbsolutePath();
    }
    
    /**
     * 加载 YAML 配置文件
     */
    private boolean loadYamlConfiguration(String configPath) {
        // 首先尝试作为外部文件加载
        if (loadExternalYamlFile(configPath)) {
            return true;
        }

        // 如果外部文件不存在，尝试从classpath加载
        return loadClasspathYamlFile(configPath);
    }

    /**
     * 从外部文件系统加载YAML配置文件
     */
    private boolean loadExternalYamlFile(String configPath) {
        File configFile = new File(configPath);
        if (!configFile.exists() || !configFile.isFile()) {
            return false;
        }

        try (InputStream is = new FileInputStream(configFile)) {
            Yaml yaml = new Yaml();
            Map<String, Object> config = yaml.load(is);

            // 使用配置绑定器绑定配置
            properties = ConfigurationBinder.bind(config, ProxyClientProperties.class);

            logger.info("成功加载外部 YAML 配置文件: {}", configFile.getAbsolutePath());
            logConfiguration();
            return true;
        } catch (Exception e) {
            logger.warn("加载外部 YAML 配置文件失败: {}", configFile.getAbsolutePath(), e);
            return false;
        }
    }

    /**
     * 从classpath加载YAML配置文件
     */
    private boolean loadClasspathYamlFile(String configPath) {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(configPath)) {
            if (is != null) {
                Yaml yaml = new Yaml();
                Map<String, Object> config = yaml.load(is);

                // 使用配置绑定器绑定配置
                properties = ConfigurationBinder.bind(config, ProxyClientProperties.class);

                logger.info("成功加载 classpath YAML 配置文件: {}", configPath);
                logConfiguration();
                return true;
            }
        } catch (Exception e) {
            logger.warn("加载 classpath YAML 配置文件失败: {}", configPath, e);
        }
        return false;
    }
    

    

    
    /**
     * 初始化默认配置
     */
    private void initializeDefaultConfiguration() {
        // 添加默认的 SOCKS5 接入器
        ProxyClientProperties.InboundItemProperties defaultSocks5 = new ProxyClientProperties.InboundItemProperties();
        defaultSocks5.setName("socks5-default");
        defaultSocks5.setPort(properties.getLocal().getPort());
        defaultSocks5.setEnabled(true);
        defaultSocks5.setDescription("默认SOCKS5接入器");
        
        properties.getInbound().getSocks5().add(defaultSocks5);
        
        logger.info("使用默认配置: SOCKS5 端口 {}", properties.getLocal().getPort());
    }
    
    /**
     * 记录配置信息
     */
    private void logConfiguration() {
        logger.info("配置加载完成 - 过滤模式: {}, 代理服务器: {}:{}, 本地端口: {}, 认证: {}",
                   properties.getFilter().getMode(),
                   properties.getProxy().getServer().getHost(),
                   properties.getProxy().getServer().getPort(),
                   properties.getLocal().getPort(),
                   properties.getAuth().isEnable() ? "启用" : "禁用");

        if (properties.getAuth().isEnable()) {
            logger.info("认证配置 - 用户名: {}, 超时时间: {}秒", 
                       properties.getAuth().getUsername(), 
                       properties.getAuth().getTimeout().getSeconds());
        }
        
        int socks5Count = (int) properties.getInbound().getSocks5().stream().filter(ProxyClientProperties.InboundItemProperties::isEnabled).count();
        int httpCount = (int) properties.getInbound().getHttp().stream().filter(ProxyClientProperties.InboundItemProperties::isEnabled).count();
        
        logger.info("接入器配置 - 启用的 SOCKS5: {} 个, 启用的 HTTP: {} 个", socks5Count, httpCount);
        
        // 详细记录每个接入器
        for (ProxyClientProperties.InboundItemProperties inbound : properties.getInbound().getSocks5()) {
            if (inbound.isEnabled()) {
                logger.info("SOCKS5 接入器: {} (端口 {}) - {}", 
                           inbound.getName(), inbound.getPort(), inbound.getDescription());
            }
        }
        
        for (ProxyClientProperties.InboundItemProperties inbound : properties.getInbound().getHttp()) {
            if (inbound.isEnabled()) {
                logger.info("HTTP 接入器: {} (端口 {}) - {}", 
                           inbound.getName(), inbound.getPort(), inbound.getDescription());
            }
        }
    }
    
    // Getter 方法
    public ProxyClientProperties getProperties() {
        return properties;
    }
    
    public AddressFilter.FilterMode getFilterMode() {
        return properties.getFilter().getMode();
    }
    
    public String getProxyServerHost() {
        return properties.getProxy().getServer().getHost();
    }
    
    public int getProxyServerPort() {
        return properties.getProxy().getServer().getPort();
    }
    
    public int getLocalPort() {
        return properties.getLocal().getPort();
    }
    
    public boolean isAuthEnabled() {
        return properties.getAuth().isEnable();
    }
    
    public String getAuthUsername() {
        return properties.getAuth().getUsername();
    }
    
    public String getAuthPassword() {
        return properties.getAuth().getPassword();
    }
    
    public int getAuthTimeoutSeconds() {
        return properties.getAuth().getTimeout().getSeconds();
    }
    
    public List<ProxyClientProperties.InboundItemProperties> getEnabledSocks5Inbounds() {
        return properties.getInbound().getSocks5().stream()
                .filter(ProxyClientProperties.InboundItemProperties::isEnabled)
                .collect(Collectors.toList());
    }

    public List<ProxyClientProperties.InboundItemProperties> getEnabledHttpInbounds() {
        return properties.getInbound().getHttp().stream()
                .filter(ProxyClientProperties.InboundItemProperties::isEnabled)
                .collect(Collectors.toList());
    }

    public List<ProxyClientProperties.InboundItemProperties> getAllSocks5Inbounds() {
        return new ArrayList<>(properties.getInbound().getSocks5());
    }

    public List<ProxyClientProperties.InboundItemProperties> getAllHttpInbounds() {
        return new ArrayList<>(properties.getInbound().getHttp());
    }
    
    // 向后兼容的方法
    public String[] getEnabledInbounds() {
        List<String> enabled = new ArrayList<>();
        
        if (!getEnabledSocks5Inbounds().isEmpty()) {
            enabled.add("socks5");
        }
        
        if (!getEnabledHttpInbounds().isEmpty()) {
            enabled.add("http");
        }
        
        return enabled.toArray(new String[0]);
    }
    
    public boolean isSocks5Enabled() {
        return !getEnabledSocks5Inbounds().isEmpty();
    }
    
    public boolean isHttpEnabled() {
        return !getEnabledHttpInbounds().isEmpty();
    }
    
    public int getSocks5Port() {
        List<ProxyClientProperties.InboundItemProperties> socks5List = getEnabledSocks5Inbounds();
        return socks5List.isEmpty() ? getLocalPort() : socks5List.get(0).getPort();
    }

    public int getHttpPort() {
        List<ProxyClientProperties.InboundItemProperties> httpList = getEnabledHttpInbounds();
        return httpList.isEmpty() ? 8080 : httpList.get(0).getPort();
    }

    // SSL配置相关方法
    public boolean isSslEnabled() {
        return properties.getSsl().isEnable();
    }

    public boolean isTrustAll() {
        return properties.getSsl().isTrustAll();
    }

    public String getTrustStorePath() {
        return properties.getSsl().getTrustStorePath();
    }

    public String getTrustStorePassword() {
        return properties.getSsl().getTrustStorePassword();
    }

    public String getTrustStoreType() {
        return properties.getSsl().getTrustStoreType();
    }

    public String getKeyStorePath() {
        return properties.getSsl().getKeyStorePath();
    }

    public String getKeyStorePassword() {
        return properties.getSsl().getKeyStorePassword();
    }

    public String getKeyStoreType() {
        return properties.getSsl().getKeyStoreType();
    }

    public String[] getSslProtocols() {
        return properties.getSsl().getProtocols();
    }

    public String[] getSslCipherSuites() {
        return properties.getSsl().getCipherSuites();
    }

    public boolean isVerifyHostname() {
        return properties.getSsl().isVerifyHostname();
    }

    public int getSslHandshakeTimeoutSeconds() {
        return properties.getSsl().getHandshakeTimeoutSeconds();
    }

    /**
     * 获取在线数据源配置
     */
    public ProxyClientProperties.OnlineDataSourcesProperties getOnlineDataSources() {
        return properties.getOnlineDataSources();
    }

    /**
     * 获取中国IP段数据源URL列表
     */
    public java.util.List<String> getChinaIpRangesUrls() {
        return properties.getOnlineDataSources().getChinaIpRanges();
    }

    // ========== 队列配置相关方法 ==========

    /**
     * 获取队列配置
     */
    public ProxyClientProperties.QueueProperties getQueueProperties() {
        return properties.getQueue();
    }

    /**
     * 获取队列容量
     */
    public int getQueueCapacity() {
        return properties.getQueue().getCapacity();
    }

    /**
     * 获取批处理大小
     */
    public int getQueueBatchSize() {
        return properties.getQueue().getBatchSize();
    }

    /**
     * 获取刷新间隔（毫秒）
     */
    public long getQueueFlushIntervalMs() {
        return properties.getQueue().getFlushIntervalMs();
    }

    /**
     * 获取重试最大次数
     */
    public int getQueueRetryMaxAttempts() {
        return properties.getQueue().getRetry().getMaxAttempts();
    }

    /**
     * 获取重试延迟（毫秒）
     */
    public long getQueueRetryDelayMs() {
        return properties.getQueue().getRetry().getDelayMs();
    }

    /**
     * 是否启用队列监控
     */
    public boolean isQueueMonitoringEnabled() {
        return properties.getQueue().getMonitoring().isEnabled();
    }

    /**
     * 获取队列监控报告间隔（秒）
     */
    public int getQueueMonitoringReportIntervalSeconds() {
        return properties.getQueue().getMonitoring().getReportIntervalSeconds();
    }

    /**
     * 获取队列使用率警告阈值（百分比）
     */
    public int getQueueMonitoringWarningThreshold() {
        return properties.getQueue().getMonitoring().getWarningThreshold();
    }

    /**
     * 获取队列使用率错误阈值（百分比）
     */
    public int getQueueMonitoringErrorThreshold() {
        return properties.getQueue().getMonitoring().getErrorThreshold();
    }

    // ========== Nacos 配置相关方法 ==========

    /**
     * 获取 Nacos 配置
     */
    public ProxyClientProperties.NacosProperties getNacosProperties() {
        return properties.getNacos();
    }

    /**
     * 是否启用 Nacos 服务发现
     */
    public boolean isNacosEnabled() {
        return properties.getNacos().isEnabled();
    }

    /**
     * 获取 Nacos 服务器地址
     */
    public String getNacosServerAddr() {
        return properties.getNacos().getServerAddr();
    }

    /**
     * 获取 Nacos 命名空间
     */
    public String getNacosNamespace() {
        return properties.getNacos().getNamespace();
    }

    /**
     * 获取 Nacos 服务名称
     */
    public String getNacosServiceName() {
        return properties.getNacos().getServiceName();
    }

    /**
     * 获取 Nacos 分组名称
     */
    public String getNacosGroupName() {
        return properties.getNacos().getGroupName();
    }

    /**
     * 获取 Nacos 用户名
     * @return
     */
    public String getUsername() {
        return properties.getNacos().getUsername();
    }

    /**
     * 获取 Nacos 密码
     * @return
     */
    public String getPassword() {
        return properties.getNacos().getPassword();
    }
}
