package com.xiang.proxy.server.inbound;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Inbound服务器统计信息
 */
public class InboundServerStatistics {
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong currentConnections = new AtomicLong(0);
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong totalBytesReceived = new AtomicLong(0);
    private final AtomicLong totalBytesSent = new AtomicLong(0);
    private final AtomicLong connectionErrors = new AtomicLong(0);
    private final AtomicLong requestErrors = new AtomicLong(0);
    private final long startTime;

    public InboundServerStatistics() {
        this.startTime = System.currentTimeMillis();
    }

    // 连接统计
    public void incrementTotalConnections() {
        totalConnections.incrementAndGet();
    }

    public void incrementCurrentConnections() {
        currentConnections.incrementAndGet();
    }

    public void decrementCurrentConnections() {
        currentConnections.decrementAndGet();
    }

    public void incrementConnectionErrors() {
        connectionErrors.incrementAndGet();
    }

    // 请求统计
    public void incrementTotalRequests() {
        totalRequests.incrementAndGet();
    }

    public void incrementRequestErrors() {
        requestErrors.incrementAndGet();
    }

    // 流量统计
    public void addBytesReceived(long bytes) {
        totalBytesReceived.addAndGet(bytes);
    }

    public void addBytesSent(long bytes) {
        totalBytesSent.addAndGet(bytes);
    }

    // Getters
    public long getTotalConnections() {
        return totalConnections.get();
    }

    public long getCurrentConnections() {
        return currentConnections.get();
    }

    public long getTotalRequests() {
        return totalRequests.get();
    }

    public long getTotalBytesReceived() {
        return totalBytesReceived.get();
    }

    public long getTotalBytesSent() {
        return totalBytesSent.get();
    }

    public long getConnectionErrors() {
        return connectionErrors.get();
    }

    public long getRequestErrors() {
        return requestErrors.get();
    }

    public long getStartTime() {
        return startTime;
    }

    public long getUptime() {
        return System.currentTimeMillis() - startTime;
    }

    // 计算统计指标
    public double getConnectionErrorRate() {
        long total = getTotalConnections();
        return total > 0 ? (double) getConnectionErrors() / total : 0.0;
    }

    public double getRequestErrorRate() {
        long total = getTotalRequests();
        return total > 0 ? (double) getRequestErrors() / total : 0.0;
    }

    public double getAverageRequestsPerConnection() {
        long connections = getTotalConnections();
        return connections > 0 ? (double) getTotalRequests() / connections : 0.0;
    }

    public double getBytesReceivedPerSecond() {
        long uptimeSeconds = getUptime() / 1000;
        return uptimeSeconds > 0 ? (double) getTotalBytesReceived() / uptimeSeconds : 0.0;
    }

    public double getBytesSentPerSecond() {
        long uptimeSeconds = getUptime() / 1000;
        return uptimeSeconds > 0 ? (double) getTotalBytesSent() / uptimeSeconds : 0.0;
    }

    // 重置统计
    public void reset() {
        totalConnections.set(0);
        currentConnections.set(0);
        totalRequests.set(0);
        totalBytesReceived.set(0);
        totalBytesSent.set(0);
        connectionErrors.set(0);
        requestErrors.set(0);
    }

    @Override
    public String toString() {
        return String.format("InboundServerStatistics{" +
                "totalConnections=%d, currentConnections=%d, totalRequests=%d, " +
                "bytesReceived=%d, bytesSent=%d, connectionErrors=%d, requestErrors=%d, " +
                "uptime=%dms, connectionErrorRate=%.2f%%, requestErrorRate=%.2f%%}",
                getTotalConnections(), getCurrentConnections(), getTotalRequests(),
                getTotalBytesReceived(), getTotalBytesSent(), getConnectionErrors(), getRequestErrors(),
                getUptime(), getConnectionErrorRate() * 100, getRequestErrorRate() * 100);
    }
}