# GraalVM Native Image build configuration with ULTIMATE Netty fixes
Args = --no-fallback \
       --enable-http \
       --enable-https \
       --enable-url-protocols=http,https \
       --initialize-at-build-time=org.slf4j \
       --initialize-at-build-time=ch.qos.logback \
       --initialize-at-run-time=io.netty \
       --initialize-at-run-time=io.netty.util.internal.shaded.org.jctools \
       --initialize-at-run-time=io.netty.buffer \
       --initialize-at-run-time=io.netty.buffer.ByteBufAllocator \
       --initialize-at-run-time=io.netty.buffer.UnpooledByteBufAllocator \
       --initialize-at-run-time=io.netty.buffer.PooledByteBufAllocator \
       --initialize-at-run-time=io.netty.buffer.AbstractByteBufAllocator \
       --initialize-at-run-time=io.netty.util.internal.PlatformDependent \
       --initialize-at-run-time=io.netty.util.internal.PlatformDependent0 \
       --initialize-at-run-time=io.netty.util.internal.shaded.org.jctools.util.UnsafeAccess \
       --initialize-at-run-time=io.netty.util.internal.shaded.org.jctools.queues \
       --initialize-at-run-time=io.netty.util.ResourceLeakDetector \
       --initialize-at-run-time=io.netty.util.ReferenceCountUtil \
       --initialize-at-run-time=io.netty.util.concurrent \
       --initialize-at-run-time=io.netty.channel \
       --allow-incomplete-classpath \
       -H:+UnlockExperimentalVMOptions \
       -H:+ReportExceptionStackTraces \
       -H:+AddAllCharsets \
       -H:+JNI \
       -H:IncludeResources=.*\.properties$ \
       -H:IncludeResources=.*\.txt$ \
       -H:IncludeResources=.*\.xml$ \
       -H:IncludeResources=.*\.yml$ \
       -H:IncludeResources=.*\.p12$ \
       --report-unsupported-elements-at-runtime
