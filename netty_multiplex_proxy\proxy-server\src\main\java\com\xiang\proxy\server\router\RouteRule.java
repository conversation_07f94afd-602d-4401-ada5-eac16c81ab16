package com.xiang.proxy.server.router;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 路由规则
 */
public class RouteRule {
    private String ruleId;
    private String name;
    private int priority;
    private List<RouteMatcher> matchers;
    private String outboundId;
    private boolean enabled;
    private Map<String, Object> config;
    private long createTime;
    private long updateTime;
    private String description;

    public RouteRule() {
        this.matchers = new ArrayList<>();
        this.config = new HashMap<>();
        this.enabled = true;
        this.priority = 100;
        this.createTime = System.currentTimeMillis();
        this.updateTime = this.createTime;
    }

    public RouteRule(String ruleId, String name, int priority, String outboundId) {
        this();
        this.ruleId = ruleId;
        this.name = name;
        this.priority = priority;
        this.outboundId = outboundId;
    }

    // Getters and Setters
    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
        this.updateTime = System.currentTimeMillis();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
        this.updateTime = System.currentTimeMillis();
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
        this.updateTime = System.currentTimeMillis();
    }

    public List<RouteMatcher> getMatchers() {
        return matchers;
    }

    public void setMatchers(List<RouteMatcher> matchers) {
        this.matchers = matchers != null ? matchers : new ArrayList<>();
        this.updateTime = System.currentTimeMillis();
    }

    public String getOutboundId() {
        return outboundId;
    }

    public void setOutboundId(String outboundId) {
        this.outboundId = outboundId;
        this.updateTime = System.currentTimeMillis();
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        this.updateTime = System.currentTimeMillis();
    }

    public Map<String, Object> getConfig() {
        return config;
    }

    public void setConfig(Map<String, Object> config) {
        this.config = config != null ? config : new HashMap<>();
        this.updateTime = System.currentTimeMillis();
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
        this.updateTime = System.currentTimeMillis();
    }

    // 便捷方法
    public void addMatcher(RouteMatcher matcher) {
        if (matcher != null) {
            this.matchers.add(matcher);
            this.updateTime = System.currentTimeMillis();
        }
    }

    public void removeMatcher(RouteMatcher matcher) {
        if (this.matchers.remove(matcher)) {
            this.updateTime = System.currentTimeMillis();
        }
    }

    public void clearMatchers() {
        if (!this.matchers.isEmpty()) {
            this.matchers.clear();
            this.updateTime = System.currentTimeMillis();
        }
    }

    public void setConfigValue(String key, Object value) {
        this.config.put(key, value);
        this.updateTime = System.currentTimeMillis();
    }

    @SuppressWarnings("unchecked")
    public <T> T getConfigValue(String key) {
        return (T) this.config.get(key);
    }

    public <T> T getConfigValue(String key, T defaultValue) {
        T value = getConfigValue(key);
        return value != null ? value : defaultValue;
    }

    public boolean hasMatchers() {
        return matchers != null && !matchers.isEmpty();
    }

    @Override
    public String toString() {
        return String.format("RouteRule{id=%s, name=%s, priority=%d, outbound=%s, enabled=%s, matchers=%d}",
                ruleId, name, priority, outboundId, enabled, matchers != null ? matchers.size() : 0);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        RouteRule routeRule = (RouteRule) obj;
        return ruleId != null ? ruleId.equals(routeRule.ruleId) : routeRule.ruleId == null;
    }

    @Override
    public int hashCode() {
        return ruleId != null ? ruleId.hashCode() : 0;
    }

    // 复制构造方法
    public RouteRule copy() {
        RouteRule copy = new RouteRule();
        copy.ruleId = this.ruleId;
        copy.name = this.name;
        copy.priority = this.priority;
        copy.matchers = new ArrayList<>(this.matchers);
        copy.outboundId = this.outboundId;
        copy.enabled = this.enabled;
        copy.config = new HashMap<>(this.config);
        copy.createTime = this.createTime;
        copy.updateTime = this.updateTime;
        copy.description = this.description;
        return copy;
    }
}