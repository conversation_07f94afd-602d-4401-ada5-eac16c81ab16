# Web管理界面实现方案

## 后端API实现

### 1. 添加Web模块依赖
```xml
<!-- proxy-server/pom.xml -->
<dependencies>
    <!-- Spring Boot Web -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>3.2.0</version>
    </dependency>
    
    <!-- WebSocket支持 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
        <version>3.2.0</version>
    </dependency>
    
    <!-- 嵌入式数据库 -->
    <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>2.2.224</version>
    </dependency>
    
    <!-- JSON处理 -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.15.2</version>
    </dependency>
</dependencies>
```

### 2. Web管理模块结构
```
proxy-server/src/main/java/com/proxy/server/
├── web/                           # Web管理模块
│   ├── WebManagementServer.java   # Web服务器启动类
│   ├── controller/                # REST控制器
│   │   ├── SystemController.java  # 系统信息API
│   │   ├── ConnectionController.java # 连接管理API
│   │   ├── MetricsController.java  # 性能监控API
│   │   ├── ConfigController.java   # 配置管理API
│   │   └── LogController.java      # 日志管理API
│   ├── service/                   # 业务服务层
│   │   ├── MetricsService.java    # 指标收集服务
│   │   ├── ConnectionService.java  # 连接管理服务
│   │   └── ConfigService.java     # 配置管理服务
│   ├── websocket/                 # WebSocket处理
│   │   ├── MetricsWebSocket.java  # 实时指标推送
│   │   └── LogWebSocket.java      # 实时日志推送
│   ├── model/                     # 数据模型
│   │   ├── SystemInfo.java       # 系统信息
│   │   ├── ConnectionInfo.java    # 连接信息
│   │   └── MetricsData.java       # 指标数据
│   └── config/                    # Web配置
│       ├── WebConfig.java         # Web配置类
│       └── SecurityConfig.java    # 安全配置
└── static/                        # 静态资源
    └── web-ui/                    # 前端构建文件
```

### 3. 核心API实现

#### 系统信息API
```java
@RestController
@RequestMapping("/api/system")
public class SystemController {
    
    @Autowired
    private PerformanceMetrics performanceMetrics;
    
    @GetMapping("/info")
    public ResponseEntity<SystemInfo> getSystemInfo() {
        SystemInfo info = SystemInfo.builder()
            .version(getVersion())
            .startTime(getStartTime())
            .uptime(getUptime())
            .javaVersion(System.getProperty("java.version"))
            .osName(System.getProperty("os.name"))
            .build();
        return ResponseEntity.ok(info);
    }
    
    @GetMapping("/status")
    public ResponseEntity<SystemStatus> getSystemStatus() {
        SystemStatus status = SystemStatus.builder()
            .running(true)
            .activeConnections(performanceMetrics.getActiveConnections())
            .totalSessions(performanceMetrics.getTotalSessions())
            .memoryUsage(getMemoryUsage())
            .cpuUsage(getCpuUsage())
            .build();
        return ResponseEntity.ok(status);
    }
}
```

#### 连接管理API
```java
@RestController
@RequestMapping("/api/connections")
public class ConnectionController {
    
    @Autowired
    private ConnectionService connectionService;
    
    @GetMapping
    public ResponseEntity<List<ConnectionInfo>> getActiveConnections() {
        List<ConnectionInfo> connections = connectionService.getActiveConnections();
        return ResponseEntity.ok(connections);
    }
    
    @GetMapping("/{connectionId}")
    public ResponseEntity<ConnectionDetail> getConnectionDetail(
            @PathVariable String connectionId) {
        ConnectionDetail detail = connectionService.getConnectionDetail(connectionId);
        return ResponseEntity.ok(detail);
    }
    
    @DeleteMapping("/{connectionId}")
    public ResponseEntity<Void> disconnectConnection(
            @PathVariable String connectionId) {
        connectionService.disconnectConnection(connectionId);
        return ResponseEntity.ok().build();
    }
    
    @GetMapping("/pool/status")
    public ResponseEntity<ConnectionPoolStatus> getConnectionPoolStatus() {
        ConnectionPoolStatus status = connectionService.getConnectionPoolStatus();
        return ResponseEntity.ok(status);
    }
}
```

#### 性能监控API
```java
@RestController
@RequestMapping("/api/metrics")
public class MetricsController {
    
    @Autowired
    private MetricsService metricsService;
    
    @GetMapping("/current")
    public ResponseEntity<CurrentMetrics> getCurrentMetrics() {
        CurrentMetrics metrics = metricsService.getCurrentMetrics();
        return ResponseEntity.ok(metrics);
    }
    
    @GetMapping("/history")
    public ResponseEntity<List<MetricsData>> getHistoryMetrics(
            @RequestParam String timeRange,
            @RequestParam(defaultValue = "1m") String interval) {
        List<MetricsData> history = metricsService.getHistoryMetrics(timeRange, interval);
        return ResponseEntity.ok(history);
    }
    
    @GetMapping("/performance")
    public ResponseEntity<PerformanceReport> getPerformanceReport(
            @RequestParam String startTime,
            @RequestParam String endTime) {
        PerformanceReport report = metricsService.generateReport(startTime, endTime);
        return ResponseEntity.ok(report);
    }
}
```

### 4. WebSocket实时推送
```java
@Component
@ServerEndpoint("/ws/metrics")
public class MetricsWebSocket {
    
    private static final Set<Session> sessions = ConcurrentHashMap.newKeySet();
    
    @OnOpen
    public void onOpen(Session session) {
        sessions.add(session);
        logger.info("WebSocket连接建立: {}", session.getId());
    }
    
    @OnClose
    public void onClose(Session session) {
        sessions.remove(session);
        logger.info("WebSocket连接关闭: {}", session.getId());
    }
    
    // 定时推送实时指标
    @Scheduled(fixedRate = 1000) // 每秒推送一次
    public void pushMetrics() {
        if (sessions.isEmpty()) return;
        
        CurrentMetrics metrics = metricsService.getCurrentMetrics();
        String message = JsonUtils.toJson(metrics);
        
        sessions.forEach(session -> {
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                logger.error("推送指标失败", e);
                sessions.remove(session);
            }
        });
    }
}
```

## 前端实现

### 1. 项目结构
```
web-ui/
├── src/
│   ├── components/          # 通用组件
│   │   ├── MetricsCard.vue  # 指标卡片
│   │   ├── RealTimeChart.vue # 实时图表
│   │   └── ConnectionTable.vue # 连接表格
│   ├── views/              # 页面组件
│   │   ├── Dashboard.vue    # 系统概览
│   │   ├── Connections.vue  # 连接管理
│   │   ├── Metrics.vue      # 性能监控
│   │   ├── Config.vue       # 配置管理
│   │   └── Logs.vue         # 日志管理
│   ├── api/                # API接口
│   │   ├── system.js       # 系统API
│   │   ├── connections.js   # 连接API
│   │   └── metrics.js      # 指标API
│   ├── utils/              # 工具函数
│   │   ├── websocket.js    # WebSocket工具
│   │   └── charts.js       # 图表工具
│   └── router/             # 路由配置
│       └── index.js
├── public/
└── package.json
```

### 2. 核心组件实现

#### 实时指标卡片
```vue
<template>
  <div class="metrics-card">
    <el-row :gutter="20">
      <el-col :span="6" v-for="metric in metrics" :key="metric.key">
        <el-card class="metric-item">
          <div class="metric-value">{{ metric.value }}</div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-trend" :class="metric.trend">
            <i :class="metric.trendIcon"></i>
            {{ metric.change }}
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useWebSocket } from '@/utils/websocket'

const metrics = ref([
  { key: 'connections', label: '活动连接', value: 0, trend: 'up', change: '+5%' },
  { key: 'sessions', label: '总会话数', value: 0, trend: 'up', change: '+12%' },
  { key: 'throughput', label: '吞吐量', value: '0 MB/s', trend: 'stable', change: '0%' },
  { key: 'uptime', label: '运行时间', value: '0d 0h', trend: 'stable', change: '' }
])

const { connect, disconnect, onMessage } = useWebSocket('/ws/metrics')

onMounted(() => {
  connect()
  onMessage((data) => {
    updateMetrics(data)
  })
})

onUnmounted(() => {
  disconnect()
})

function updateMetrics(data) {
  metrics.value[0].value = data.activeConnections
  metrics.value[1].value = data.totalSessions
  metrics.value[2].value = data.throughput
  metrics.value[3].value = data.uptime
}
</script>
```

#### 实时图表组件
```vue
<template>
  <div class="chart-container">
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: Array,
  type: String,
  title: String
})

const chartRef = ref()
let chart = null

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
  }
})

watch(() => props.data, (newData) => {
  updateChart(newData)
}, { deep: true })

function initChart() {
  chart = echarts.init(chartRef.value)
  
  const option = {
    title: { text: props.title },
    tooltip: { trigger: 'axis' },
    xAxis: { type: 'time' },
    yAxis: { type: 'value' },
    series: [{
      type: 'line',
      data: props.data,
      smooth: true,
      animation: true
    }]
  }
  
  chart.setOption(option)
}

function updateChart(data) {
  if (chart && data) {
    chart.setOption({
      series: [{ data }]
    })
  }
}
</script>
```

### 3. WebSocket工具类
```javascript
// utils/websocket.js
export function useWebSocket(url) {
  let ws = null
  let messageHandlers = []
  
  function connect() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${protocol}//${window.location.host}${url}`
    
    ws = new WebSocket(wsUrl)
    
    ws.onopen = () => {
      console.log('WebSocket连接已建立')
    }
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      messageHandlers.forEach(handler => handler(data))
    }
    
    ws.onclose = () => {
      console.log('WebSocket连接已关闭')
      // 自动重连
      setTimeout(connect, 3000)
    }
    
    ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
    }
  }
  
  function disconnect() {
    if (ws) {
      ws.close()
      ws = null
    }
  }
  
  function onMessage(handler) {
    messageHandlers.push(handler)
  }
  
  function send(data) {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(data))
    }
  }
  
  return { connect, disconnect, onMessage, send }
}
```