package com.xiang.proxy.server.handler;

import com.xiang.proxy.server.protocol.MultiplexProtocol;
import com.xiang.proxy.server.auth.AuthManager;
import com.xiang.proxy.server.auth.AuthConfig;
import com.xiang.proxy.server.pool.ConnectionPool;
import com.xiang.proxy.server.config.ConnectionPoolConfig;
import com.xiang.proxy.server.blacklist.HostBlacklist;
import com.xiang.proxy.server.metrics.AdvancedMetrics;
import com.xiang.proxy.server.metrics.PerformanceMetrics;
import com.xiang.proxy.server.filter.GeoLocationFilter;
import com.xiang.proxy.server.filter.FilterResult;
import com.xiang.proxy.server.exception.ExceptionHandler;
import com.xiang.proxy.server.exception.ResourceCleanupManager;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.socket.nio.NioSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 多路复用代理处理器
 * 处理来自proxy-client的多路复用连接
 * 每个客户端连接都会创建一个独立的处理器实例，支持多客户端并发访问
 */
public class MultiplexProxyHandler extends ChannelInboundHandlerAdapter {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexProxyHandler.class);

    // 全局客户端连接计数器
    private static final AtomicLong CLIENT_CONNECTION_COUNTER = new AtomicLong(0);

    // 每个客户端的最大会话数限制
    private static final int MAX_SESSIONS_PER_CLIENT = 200;

    // 当前客户端连接的会话映射（单线程访问，使用HashMap即可）
    private final HashMap<Integer, Channel> sessions = new HashMap<>();
    // 会话ID到主机键值的映射，用于连接归还（单线程访问，使用HashMap即可）
    private final HashMap<Integer, String> sessionHostKeys = new HashMap<>();
    // 当前客户端的sessionId生成器
    private final AtomicInteger sessionIdGenerator = new AtomicInteger(1);
    // 可重用的会话ID队列
    private final Queue<Integer> reusableSessionIds = new ConcurrentLinkedQueue<>();
    private ByteBuf buffer = null;
    private Channel clientChannel;
    private final long clientConnectionId;

    // 添加性能指标收集实例
    private final AdvancedMetrics advancedMetrics = AdvancedMetrics.getInstance();
    
    // 异常处理器
    private final ExceptionHandler exceptionHandler = new ExceptionHandler();
    
    // 资源清理管理器
    private final ResourceCleanupManager cleanupManager = ResourceCleanupManager.getInstance();

    /**
     * 构造函数
     */
    public MultiplexProxyHandler() {
        this.clientConnectionId = CLIENT_CONNECTION_COUNTER.incrementAndGet();
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        this.clientChannel = ctx.channel();

        // 性能指标统计
        PerformanceMetrics.getInstance().incrementActiveConnections();

        logger.info("多路复用客户端连接建立: {} (连接ID: {})",
                ctx.channel().remoteAddress(), clientConnectionId);

        // 启动认证过程
        if (AuthConfig.isAuthEnabled()) {
            AuthManager.getInstance().startAuthProcess(ctx.channel());
            logger.debug("连接 {} 需要认证", clientConnectionId);
        }

        super.channelActive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        // 记录请求处理
        advancedMetrics.recordRequest();
        long startTime = System.currentTimeMillis();

        ByteBuf data = (ByteBuf) msg;

        try {
            // 优化：延迟创建缓冲区，减少内存分配
            if (buffer == null) {
                // 如果数据足够大，直接处理而不创建缓冲区
                if (data.readableBytes() >= MultiplexProtocol.HEADER_LENGTH) {
                    if (tryProcessDirectly(ctx, data)) {
                        // 记录响应处理
                        advancedMetrics.recordResponse();
                        return; // 直接处理成功，无需缓冲
                    }
                }
                // 需要缓冲，创建适当大小的缓冲区
                buffer = ctx.alloc().buffer(Math.max(data.readableBytes() * 2, 1024));
            }

            buffer.writeBytes(data);

            // 尝试解析数据包
            while (buffer.readableBytes() >= MultiplexProtocol.HEADER_LENGTH) {
                int readerIndex = buffer.readerIndex();
                MultiplexProtocol.Packet packet = MultiplexProtocol.Packet.decode(buffer);

                if (packet == null) {
                    // 数据不完整，等待更多数据
                    buffer.readerIndex(readerIndex);
                    break;
                }

                // 处理数据包
                handlePacket(ctx, packet);
            }

            // 优化缓冲区管理
            optimizeBuffer();

            // 记录响应处理
            advancedMetrics.recordResponse();

        } finally {
            // 记录处理延迟
            long latency = System.currentTimeMillis() - startTime;
            advancedMetrics.recordLatency("request", latency);

            data.release();
        }
    }

    /**
     * 尝试直接处理数据而不使用缓冲区
     */
    private boolean tryProcessDirectly(ChannelHandlerContext ctx, ByteBuf data) {
        if (data.readableBytes() < MultiplexProtocol.HEADER_LENGTH) {
            return false;
        }

        int readerIndex = data.readerIndex();
        MultiplexProtocol.Packet packet = MultiplexProtocol.Packet.decode(data);

        if (packet == null) {
            // 数据不完整，恢复读取位置
            data.readerIndex(readerIndex);
            return false;
        }

        // 处理数据包
        handlePacket(ctx, packet);

        // 如果还有剩余数据，返回false以便使用缓冲区处理
        return data.readableBytes() == 0;
    }

    /**
     * 优化缓冲区管理
     */
    private void optimizeBuffer() {
        if (buffer == null)
            return;

        int readableBytes = buffer.readableBytes();
        if (readableBytes == 0) {
            // 缓冲区为空，清理
            buffer.clear();
        } else if (readableBytes < buffer.capacity() / 4 && buffer.capacity() > 1024) {
            // 缓冲区使用率低且较大，压缩以节省内存
            buffer.discardReadBytes();
            if (buffer.capacity() > readableBytes * 4) {
                // 创建更小的缓冲区
                ByteBuf newBuffer = buffer.alloc().buffer(Math.max(readableBytes * 2, 256));
                newBuffer.writeBytes(buffer);
                buffer.release();
                buffer = newBuffer;
            }
        } else {
            // 正常压缩
            buffer.discardReadBytes();
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.warn("多路复用客户端连接断开: {} (连接ID: {}), 活跃会话数: {}",
                ctx.channel().remoteAddress(), clientConnectionId, sessions.size());

        // 优先归还连接到连接池；若不适合或失败则关闭
        int closedSessions = 0;
        int returnedSessions = 0;
        for (Map.Entry<Integer, Channel> entry : sessions.entrySet()) {
            Integer sid = entry.getKey();
            Channel channel = entry.getValue();
            if (channel != null && channel.isActive()) {
                String hostKey = sessionHostKeys.get(sid);
                try {
                    if (ConnectionPoolConfig.ENABLE_CONNECTION_POOL && hostKey != null && !hostKey.trim().isEmpty()) {
                        ConnectionPool.getInstance().returnConnection(hostKey, channel);
                        returnedSessions++;
                        logger.debug("会话 {} 后端连接已归还到连接池: {} (连接ID: {})", sid, hostKey, clientConnectionId);
                    } else {
                        logger.debug("关闭后端连接: {} (连接ID: {}, 会话: {})",
                                channel.remoteAddress(), clientConnectionId, sid);
                        channel.close();
                        closedSessions++;
                    }

                    // 性能指标统计
                    PerformanceMetrics.getInstance().decrementActiveSessions();
                } catch (Exception e) {
                    logger.warn("处理会话 {} 后端连接返还/关闭时异常: {}", sid, e.getMessage());
                    try { channel.close(); } catch (Exception ignore) {}
                    closedSessions++;
                }
            }
        }
        sessions.clear();
        sessionHostKeys.clear(); // 清理主机键值映射
        reusableSessionIds.clear(); // 清理可重用ID队列

        if (closedSessions > 0) {
            logger.info("客户端连接 {} 断开，已清理 {} 个后端会话",
                    clientConnectionId, closedSessions);
        }

        // 安全释放缓冲区
        if (buffer != null) {
            try {
                buffer.release();
                logger.debug("已释放客户端连接 {} 的缓冲区", clientConnectionId);
            } catch (Exception e) {
                logger.warn("释放缓冲区时出现异常 (连接ID: {}): {}",
                        clientConnectionId, e.getMessage());
            } finally {
                buffer = null;
            }
        }

        // 清理认证状态
        AuthManager.getInstance().clearAuth(ctx.channel());

        super.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        String contextInfo = String.format("多路复用客户端连接 (连接ID: %d, 远程地址: %s, 活跃会话数: %d)", 
                clientConnectionId, ctx.channel().remoteAddress(), sessions.size());
        
        // 使用统一异常处理器处理异常
        ExceptionHandler.ExceptionResult result = exceptionHandler.handleChannelException(ctx, cause, contextInfo);
        
        // 根据处理结果决定是否关闭连接
        if (result.shouldClose()) {
            // 清理所有会话资源
            cleanupManager.cleanupConnectionResources(
                String.valueOf(clientConnectionId), 
                clientChannel, 
                sessions, 
                sessionHostKeys, 
                buffer
            );
            
            // 安全关闭连接
            exceptionHandler.safeCloseContext(ctx, "异常处理后关闭连接");
        }
    }

    /**
     * 处理接收到的数据包
     */
    private void handlePacket(ChannelHandlerContext ctx, MultiplexProtocol.Packet packet) {
        logger.debug("收到数据包 (连接ID: {}): {}", clientConnectionId, packet);

        try {
            switch (packet.getType()) {
                case MultiplexProtocol.TYPE_AUTH_REQUEST:
                    handleAuthRequest(ctx, packet);
                    break;
                case MultiplexProtocol.TYPE_CONNECT_REQUEST_V2:
                    handleConnectRequestV2(ctx, packet);
                    break;
                case MultiplexProtocol.TYPE_TCP_CONNECT_REQUEST:
                    handleTcpConnectRequest(ctx, packet);
                    break;
                case MultiplexProtocol.TYPE_UDP_CONNECT_REQUEST:
                    handleUdpConnectRequest(ctx, packet);
                    break;
                case MultiplexProtocol.TYPE_DATA:
                    handleDataPacket(packet);
                    break;
                case MultiplexProtocol.TYPE_CLOSE:
                    handleClosePacket(packet);
                    break;
                case MultiplexProtocol.TYPE_HEARTBEAT:
                    handleHeartbeat(ctx, packet);
                    break;
                default:
                    logger.warn("未知数据包类型 (连接ID: {}): {}", clientConnectionId, packet.getType());
            }
        } catch (Exception e) {
            logger.error("处理数据包时发生异常 (连接ID: {}, 数据包: {}): {}",
                    clientConnectionId, packet, e.getMessage(), e);
        }
    }

    /**
     * 处理认证请求
     */
    private void handleAuthRequest(ChannelHandlerContext ctx, MultiplexProtocol.Packet packet) {
        try {
            // 解析认证数据
            String[] credentials = MultiplexProtocol.parseAuthRequest(packet);
            String username = credentials[0];
            String password = credentials[1];

            logger.debug("连接 {} 收到认证请求，用户: {}", clientConnectionId, username);

            // 执行认证
            boolean success = AuthManager.getInstance().authenticate(ctx.channel(), username, password);

            // 发送认证响应
            byte status = success ? MultiplexProtocol.STATUS_SUCCESS : MultiplexProtocol.STATUS_AUTH_FAILED;
            MultiplexProtocol.Packet response = MultiplexProtocol.createAuthResponsePacket(status);
            ByteBuf responseBuffer = response.encode();
            ctx.writeAndFlush(responseBuffer);

            if (success) {
                logger.info("连接 {} 认证成功，用户: {}", clientConnectionId, username);
            } else {
                logger.warn("连接 {} 认证失败，用户: {}，即将关闭连接", clientConnectionId, username);
                // 认证失败后延迟关闭连接，给客户端时间接收响应
                ctx.channel().eventLoop().schedule(() -> {
                    ctx.close();
                }, 1, java.util.concurrent.TimeUnit.SECONDS);
            }

        } catch (Exception e) {
            logger.error("处理认证请求时发生异常 (连接ID: {}): {}", clientConnectionId, e.getMessage(), e);

            // 发送认证失败响应
            MultiplexProtocol.Packet response = MultiplexProtocol
                    .createAuthResponsePacket(MultiplexProtocol.STATUS_AUTH_FAILED);
            ByteBuf responseBuffer = response.encode();
            ctx.writeAndFlush(responseBuffer);

            // 关闭连接
            ctx.close();
        }
    }

    /**
     * 处理连接请求V2（服务器分配sessionId）- 保持向后兼容
     */
    private void handleConnectRequestV2(ChannelHandlerContext ctx, MultiplexProtocol.Packet packet) {
        // 默认为TCP连接以保持向后兼容
        handleConnectRequestV2(ctx, packet, "TCP");
    }

    /**
     * 处理TCP连接请求
     */
    private void handleTcpConnectRequest(ChannelHandlerContext ctx, MultiplexProtocol.Packet packet) {
        handleConnectRequestV2(ctx, packet, "TCP");
    }

    /**
     * 处理UDP连接请求
     */
    private void handleUdpConnectRequest(ChannelHandlerContext ctx, MultiplexProtocol.Packet packet) {
        handleConnectRequestV2(ctx, packet, "UDP");
    }

    /**
     * 处理连接请求V2（通用方法，支持TCP和UDP）
     */
    private void handleConnectRequestV2(ChannelHandlerContext ctx, MultiplexProtocol.Packet packet,
                                        String connectionType) {
        try {
            // 检查认证状态
            if (AuthManager.getInstance().requiresAuth(ctx.channel())) {
                logger.warn("连接 {} 未认证，拒绝{}连接请求", clientConnectionId, connectionType);
                sendConnectResponseV2(ctx, 0, MultiplexProtocol.STATUS_AUTH_REQUIRED);
                return;
            }

            // 检查认证超时
            if (AuthManager.getInstance().isAuthTimeout(ctx.channel())) {
                logger.warn("连接 {} 认证超时，关闭连接", clientConnectionId);
                ctx.close();
                return;
            }

            String[] hostPort = MultiplexProtocol.parseConnectRequest(packet);
            String host = hostPort[0];
            int port = Integer.parseInt(hostPort[1]);

            // 安全地分配新的sessionId，避免冲突
            int sessionId = allocateSessionId();
            if (sessionId == -1) {
                logger.error("无法分配会话ID，会话数量已达上限 (连接ID: {})", clientConnectionId);
                sendConnectResponseV2(ctx, 0, MultiplexProtocol.STATUS_FAILED);
                return;
            }

            // 格式化IPv6地址用于日志显示
            String displayAddress = formatAddressForDisplay(host, port);
            logger.info("客户端连接 {} 分配会话 {} 请求{}连接: {}",
                    clientConnectionId, sessionId, connectionType, displayAddress);

            // 检查主机是否在黑名单中
            if (HostBlacklist.getInstance().isBlacklisted(host)) {
                PerformanceMetrics.getInstance().incrementBlacklistHits();
                if (logger.isDebugEnabled()) {
                    logger.debug("主机 {} 在黑名单中，拒绝{}连接请求 (连接ID: {}, 会话: {})",
                            host, connectionType, clientConnectionId, sessionId);
                }
                sendConnectResponseV2(ctx, sessionId, MultiplexProtocol.STATUS_HOST_UNREACHABLE);
                return;
            }

            // 地理位置过滤检查
            FilterResult filterResult = GeoLocationFilter.getInstance().checkAccess(host, port);
            if (filterResult.isBlocked()) {
                PerformanceMetrics.getInstance().incrementGeoLocationBlocks();
                logger.warn("主机 {} 被地理位置过滤器阻止，原因: {} (连接ID: {}, 会话: {}, 类型: {})",
                        host, filterResult.getReason(), clientConnectionId, sessionId, connectionType);

                byte statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
                switch (filterResult.getBlockReason()) {
                    case MALICIOUS_DOMAIN:
                    case MALICIOUS_KEYWORDS:
                        statusCode = MultiplexProtocol.STATUS_FORBIDDEN;
                        break;
                    case OVERSEAS_SUSPICIOUS:
                    case GEO_LOCATION_RESTRICTED:
                        statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
                        break;
                    default:
                        statusCode = MultiplexProtocol.STATUS_FAILED;
                        break;
                }
                sendConnectResponseV2(ctx, sessionId, statusCode);
                return;
            }

            // 检查会话数量限制
            if (sessions.size() >= MAX_SESSIONS_PER_CLIENT) {
                logger.warn("客户端连接 {} 会话数量已达上限 {}，拒绝新{}连接请求",
                        clientConnectionId, MAX_SESSIONS_PER_CLIENT, connectionType);
                sendConnectResponseV2(ctx, sessionId, MultiplexProtocol.STATUS_FAILED);
                return;
            }

            // 根据连接类型创建连接
            boolean isUdp = "UDP".equals(connectionType);
            connectToBackendV2(ctx, sessionId, host, port, connectionType, isUdp);

        } catch (Exception e) {
            logger.error("处理{}连接请求失败 (连接ID: {}): {}", connectionType, clientConnectionId, e.getMessage(), e);
            sendConnectResponseV2(ctx, 0, MultiplexProtocol.STATUS_FAILED);
        }
    }

    /**
     * 连接到后端服务器V2（支持TCP和UDP）
     */
    private void connectToBackendV2(ChannelHandlerContext ctx, int sessionId, String host, int port,
                                    String connectionType, boolean isUdp) {
        String hostKey = host + ":" + port + ":" + connectionType;

        // 尝试从连接池获取连接
        Channel pooledChannel = null;
        if (ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
            pooledChannel = ConnectionPool.getInstance().getConnection(hostKey);
        }

        if (pooledChannel != null && pooledChannel.isActive()) {
            // 使用连接池中的连接
            PerformanceMetrics.getInstance().incrementPoolHits();
            try {
                sessions.put(sessionId, pooledChannel);
                sessionHostKeys.put(sessionId, hostKey);

                if (!isUdp) {
                    setupBackendHandlerTCP(pooledChannel, sessionId);
                }else  {
                    setupBackendHandlerUDP(pooledChannel, sessionId);
                }

                sendConnectResponseV2(ctx, sessionId, MultiplexProtocol.STATUS_SUCCESS);
                PerformanceMetrics.getInstance().incrementTotalSessions();
                PerformanceMetrics.getInstance().incrementActiveSessions();

                logger.debug("客户端连接 {} 会话 {} 使用连接池{}连接: {}:{}",
                        clientConnectionId, sessionId, connectionType, host, port);
                return;
            } catch (Exception e) {
                logger.error("设置连接池{}连接失败 (连接ID: {}, 会话: {}): {}",
                        connectionType, clientConnectionId, sessionId, e.getMessage(), e);
                sessions.remove(sessionId);
                sessionHostKeys.remove(sessionId);
                pooledChannel.close();
            }
        } else {
            PerformanceMetrics.getInstance().incrementPoolMisses();
        }

        // 创建新连接
        if (isUdp) {
            createUdpConnection(ctx, sessionId, host, port, hostKey);
        } else {
            createTcpConnection(ctx, sessionId, host, port, hostKey);
        }
    }

    private void setupBackendHandlerUDP(Channel udpChannel, int sessionId) {
        if (udpChannel == null || !udpChannel.isActive()) {
            throw new IllegalStateException("后端连接无效");
        }

        // 清理可能存在的旧处理器
        ChannelPipeline pipeline = udpChannel.pipeline();

        // 移除可能存在的旧的udp-response-handler
        if (pipeline.get("udp-response-handler") != null) {
            pipeline.remove("udp-response-handler");
            logger.debug("移除旧的后端处理器");
        }

        // 为UDP连接添加响应处理器
        udpChannel.pipeline().addLast("udp-response-handler", new ChannelInboundHandlerAdapter() {
            @Override
            public void channelRead(ChannelHandlerContext udpCtx, Object msg) throws Exception {
                if (msg instanceof io.netty.channel.socket.DatagramPacket) {
                    io.netty.channel.socket.DatagramPacket responsePacket = (io.netty.channel.socket.DatagramPacket) msg;
                    forwardUdpResponseToClient(sessionId, responsePacket);
                }
            }

            @Override
            public void exceptionCaught(ChannelHandlerContext udpCtx, Throwable cause) throws Exception {
                logger.error("UDP响应处理异常 (会话: {}): {}", sessionId, cause.getMessage());
                cleanupSessionAndNotifyClient(sessionId, "UDP响应处理异常");
            }
        });
    }

    /**
     * 创建TCP连接
     */
    private void createTcpConnection(ChannelHandlerContext ctx, int sessionId, String host, int port, String hostKey) {
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(ctx.channel().eventLoop())
                .channel(NioSocketChannel.class)
                // Netty性能优化选项
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, ConnectionPoolConfig.CONNECTION_TIMEOUT)
                .option(ChannelOption.SO_RCVBUF, 65536)
                .option(ChannelOption.SO_SNDBUF, 65536)
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                .option(ChannelOption.WRITE_BUFFER_WATER_MARK, new WriteBufferWaterMark(8 * 1024, 32 * 1024))
                .handler(new ChannelInitializer<Channel>() {
                    @Override
                    protected void initChannel(Channel ch) throws Exception {
                        // 初始化时不添加处理器，连接成功后再添加
                    }
                });

        long startTime = System.currentTimeMillis();

        bootstrap.connect(host, port).addListener((ChannelFutureListener) future -> {
            long elapsedTime = System.currentTimeMillis() - startTime;

            if (future.isSuccess()) {
                Channel backendChannel = future.channel();

                logger.debug("客户端连接 {} 会话 {} TCP连接后端成功: {}:{}, 耗时: {}ms",
                        clientConnectionId, sessionId, host, port, elapsedTime);

                // Record connection quality metrics - success
                advancedMetrics.recordConnectionQuality(host, true, elapsedTime);
                HostBlacklist.getInstance().recordSuccess(host);

                if (clientChannel == null || !clientChannel.isActive()) {
                    logger.warn("客户端连接 {} 已断开，关闭新建的TCP后端连接: {}:{}",
                            clientConnectionId, host, port);
                    backendChannel.close();
                    return;
                }

                try {
                    sessions.put(sessionId, backendChannel);
                    sessionHostKeys.put(sessionId, hostKey);
                    setupBackendHandlerTCP(backendChannel, sessionId);
                    sendConnectResponseV2(ctx, sessionId, MultiplexProtocol.STATUS_SUCCESS);

                    PerformanceMetrics.getInstance().incrementTotalSessions();
                    PerformanceMetrics.getInstance().incrementActiveSessions();
                } catch (Exception e) {
                    logger.error("设置TCP后端连接失败，清理资源 (连接ID: {}, 会话: {}): {}",
                            clientConnectionId, sessionId, e.getMessage(), e);
                    sessions.remove(sessionId);
                    sessionHostKeys.remove(sessionId);
                    backendChannel.close();
                    sendConnectResponseV2(ctx, sessionId, MultiplexProtocol.STATUS_FAILED);
                }
            } else {
                logger.warn("客户端连接 {} 会话 {} TCP连接后端失败: {}:{}, 耗时: {}ms, 原因: {}",
                        clientConnectionId, sessionId, host, port, elapsedTime, future.cause().getMessage());

                // Record connection quality metrics - failure
                advancedMetrics.recordConnectionQuality(host, false, elapsedTime);
                handleConnectionFailure(ctx, sessionId, host, port, "TCP", future.cause());
            }
        });
    }

    /**
     * 创建UDP连接（实际上是创建UDP通道用于数据转发）
     */
    private void createUdpConnection(ChannelHandlerContext ctx, int sessionId, String host, int port, String hostKey) {
        Bootstrap udpBootstrap = new Bootstrap();
        udpBootstrap.group(ctx.channel().eventLoop())
                .channel(io.netty.channel.socket.nio.NioDatagramChannel.class)
                // Netty性能优化选项
                .option(ChannelOption.SO_RCVBUF, 65536)
                .option(ChannelOption.SO_SNDBUF, 65536)
                .option(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                .option(ChannelOption.WRITE_BUFFER_WATER_MARK, new WriteBufferWaterMark(8 * 1024, 32 * 1024));

        long startTime = System.currentTimeMillis();

        udpBootstrap.bind(0).addListener((ChannelFutureListener) bindFuture -> {
            long elapsedTime = System.currentTimeMillis() - startTime;

            if (bindFuture.isSuccess()) {
                Channel udpChannel = bindFuture.channel();

                logger.debug("客户端连接 {} 会话 {} UDP连接创建成功: {}:{}, 耗时: {}ms",
                        clientConnectionId, sessionId, host, port, elapsedTime);

                // Record connection quality metrics - success
                advancedMetrics.recordConnectionQuality(host, true, elapsedTime);
                HostBlacklist.getInstance().recordSuccess(host);

                if (clientChannel == null || !clientChannel.isActive()) {
                    logger.warn("客户端连接 {} 已断开，关闭新建的UDP连接: {}:{}",
                            clientConnectionId, host, port);
                    udpChannel.close();
                    return;
                }

                try {
                    sessions.put(sessionId, udpChannel);
                    sessionHostKeys.put(sessionId, hostKey);
                    
                    // 为UDP连接添加响应处理器
                    setupBackendHandlerUDP(udpChannel, sessionId);

                    sendConnectResponseV2(ctx, sessionId, MultiplexProtocol.STATUS_SUCCESS);

                    PerformanceMetrics.getInstance().incrementTotalSessions();
                    PerformanceMetrics.getInstance().incrementActiveSessions();
                } catch (Exception e) {
                    logger.error("设置UDP连接失败，清理资源 (连接ID: {}, 会话: {}): {}",
                            clientConnectionId, sessionId, e.getMessage(), e);
                    sessions.remove(sessionId);
                    sessionHostKeys.remove(sessionId);
                    udpChannel.close();
                    sendConnectResponseV2(ctx, sessionId, MultiplexProtocol.STATUS_FAILED);
                }
            } else {
                logger.warn("客户端连接 {} 会话 {} UDP连接创建失败: {}:{}, 耗时: {}ms, 原因: {}",
                        clientConnectionId, sessionId, host, port, elapsedTime, bindFuture.cause().getMessage());

                // Record connection quality metrics - failure
                advancedMetrics.recordConnectionQuality(host, false, elapsedTime);
                handleConnectionFailure(ctx, sessionId, host, port, "UDP", bindFuture.cause());
            }
        });
    }

    /**
     * 处理连接失败
     */
    private void handleConnectionFailure(ChannelHandlerContext ctx, int sessionId, String host, int port,
                                         String connectionType, Throwable cause) {
        logger.error("客户端连接 {} 会话 {} {}连接后端失败: {}:{}, 原因: {}",
                clientConnectionId, sessionId, connectionType, host, port, cause.getMessage());

        // Record connection quality metrics - failure with error type
        advancedMetrics.recordError("backend_connection_failed");
        advancedMetrics.recordConnectionQuality(host, false, 0); // 0 because we don't have timing in this context

        HostBlacklist.getInstance().recordFailure(host);

        byte statusCode = MultiplexProtocol.STATUS_FAILED;
        if (cause instanceof java.net.ConnectException) {
            statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
            logger.warn("目标主机不可达: {}:{}", host, port);
        } else if (cause instanceof java.net.UnknownHostException) {
            statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
            logger.warn("无法解析主机名: {}", host);
        } else if (cause instanceof java.util.concurrent.TimeoutException ||
                (cause.getMessage() != null && cause.getMessage().contains("timeout"))) {
            statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
            logger.warn("连接超时: {}:{}", host, port);
        }

        sendConnectResponseV2(ctx, sessionId, statusCode);
    }

    /**
     * 处理数据包
     */
    private void handleDataPacket(MultiplexProtocol.Packet packet) {
        int sessionId = packet.getSessionId();

        if (packet.isTcp()) {
            // TCP数据包：使用TCP转发
            handleTcpDataPacket(packet, sessionId);
        } else if (packet.isUdp()) {
            // UDP数据包：使用UDP转发
            handleUdpDataPacket(packet, sessionId);
        } else {
            logger.warn("客户端连接 {} 会话 {} 收到未知数据类型的数据包: {}",
                    clientConnectionId, sessionId, packet.getDataType());
        }
    }

    /**
     * 处理TCP数据包
     */
    private void handleTcpDataPacket(MultiplexProtocol.Packet packet, int sessionId) {
        Channel backendChannel = sessions.get(sessionId);

        if (backendChannel != null && backendChannel.isActive()) {
            try {
                // 优化：直接使用packet的ByteBuf，避免数据拷贝
                ByteBuf packetData = packet.getDataBuf();
                if (packetData != null && packetData.isReadable()) {
                    // 保留引用计数，直接转发
                    packetData.retain();
                    backendChannel.writeAndFlush(packetData).addListener(future -> {
                        // 写入完成后释放引用
                        packetData.release();
                        if (!future.isSuccess()) {
                            logger.error("客户端连接 {} 会话 {} TCP数据转发失败: {}",
                                    clientConnectionId, sessionId, future.cause().getMessage());
                            cleanupSessionAndNotifyClient(sessionId, "TCP数据转发失败");
                        }
                    });
                } else {
                    // 回退到原有方式
                    byte[] data = packet.getData();
                    if (data != null && data.length > 0) {
                        ByteBuf buffer = backendChannel.alloc().buffer(data.length);
                        buffer.writeBytes(data);
                        backendChannel.writeAndFlush(buffer);
                    }
                }

                logger.debug("客户端连接 {} 会话 {} 转发TCP数据到后端: {} bytes",
                        clientConnectionId, sessionId, packet.getDataLength());
            } catch (Exception e) {
                logger.error("客户端连接 {} 会话 {} 转发TCP数据到后端时发生异常: {}",
                        clientConnectionId, sessionId, e.getMessage(), e);
                // 转发失败，清理会话
                cleanupSessionAndNotifyClient(sessionId, "TCP数据转发异常");
            }
        } else {
            // 降低日志级别，避免过多警告
            if (logger.isDebugEnabled()) {
                logger.debug("客户端连接 {} 会话 {} TCP连接不存在或已关闭，丢弃数据包 ({} bytes) [活跃会话数: {}]",
                        clientConnectionId, sessionId, packet.getDataLength(), sessions.size());
            }
        }
    }

    /**
     * 处理UDP数据包
     */
    private void handleUdpDataPacket(MultiplexProtocol.Packet packet, int sessionId) {
        try {
            byte[] udpData = packet.getData();
            if (udpData == null || udpData.length == 0) {
                logger.warn("客户端连接 {} 会话 {} 收到空的UDP数据包", clientConnectionId, sessionId);
                return;
            }

            // 解析UDP数据包格式：[HOST_LEN][HOST][PORT][DATA_LEN][DATA]
            ByteBuf buffer = Unpooled.wrappedBuffer(udpData);

            try {
                // 读取目标主机信息
                int hostLen = buffer.readUnsignedByte();
                if (buffer.readableBytes() < hostLen + 4) { // hostLen + port(2) + dataLen(2)
                    logger.warn("客户端连接 {} 会话 {} UDP数据包格式错误", clientConnectionId, sessionId);
                    return;
                }

                byte[] hostBytes = new byte[hostLen];
                buffer.readBytes(hostBytes);
                String targetHost = new String(hostBytes, "UTF-8");
                int targetPort = buffer.readUnsignedShort();

                // 读取数据长度和数据
                int dataLen = buffer.readUnsignedShort();
                if (buffer.readableBytes() < dataLen) {
                    logger.warn("客户端连接 {} 会话 {} UDP数据包数据长度不匹配", clientConnectionId, sessionId);
                    return;
                }

                byte[] actualData = new byte[dataLen];
                buffer.readBytes(actualData);

                logger.debug("客户端连接 {} 会话 {} 收到UDP数据包: {}:{}, 数据长度: {}",
                        clientConnectionId, sessionId, targetHost, targetPort, dataLen);

                // 转发UDP数据包到目标地址
                forwardUdpData(sessionId, targetHost, targetPort, actualData);

            } finally {
                buffer.release();
            }

        } catch (Exception e) {
            logger.error("客户端连接 {} 会话 {} 处理UDP数据包时发生异常: {}",
                    clientConnectionId, sessionId, e.getMessage(), e);
        }
    }

    /**
     * 转发UDP数据到目标地址
     */
    private void forwardUdpData(int sessionId, String targetHost, int targetPort, byte[] data) {
        try {
            // 获取已建立的UDP连接
            Channel udpChannel = sessions.get(sessionId);
            if (udpChannel == null || !udpChannel.isActive()) {
                logger.error("客户端连接 {} 会话 {} UDP连接不存在或已关闭", clientConnectionId, sessionId);
                return;
            }

            // 确保UDP连接有正确的handler来处理响应
            if (udpChannel.pipeline().get("udp-response-handler") == null) {
                udpChannel.pipeline().addLast("udp-response-handler", new ChannelInboundHandlerAdapter() {
                    @Override
                    public void channelRead(ChannelHandlerContext udpCtx, Object msg) throws Exception {
                        if (msg instanceof io.netty.channel.socket.DatagramPacket) {
                            io.netty.channel.socket.DatagramPacket responsePacket = (io.netty.channel.socket.DatagramPacket) msg;
                            forwardUdpResponseToClient(sessionId, responsePacket);
                        }
                    }

                    @Override
                    public void exceptionCaught(ChannelHandlerContext udpCtx, Throwable cause) throws Exception {
                        logger.error("UDP响应处理异常 (会话: {}): {}", sessionId, cause.getMessage());
                        cleanupSessionAndNotifyClient(sessionId, "UDP响应处理异常");
                    }
                });
                logger.debug("为UDP连接 {} 添加响应处理器", sessionId);
            }

            // 发送UDP数据包到目标地址
            java.net.InetSocketAddress targetAddress = new java.net.InetSocketAddress(targetHost, targetPort);
            ByteBuf udpBuffer = Unpooled.wrappedBuffer(data);
            io.netty.channel.socket.DatagramPacket packet = new io.netty.channel.socket.DatagramPacket(
                    udpBuffer, targetAddress);

            udpChannel.writeAndFlush(packet).addListener(sendFuture -> {
                if (sendFuture.isSuccess()) {
                    logger.debug("客户端连接 {} 会话 {} UDP数据转发成功: {}:{}, 数据长度: {}",
                            clientConnectionId, sessionId, targetHost, targetPort, data.length);
                } else {
                    logger.error("客户端连接 {} 会话 {} UDP数据转发失败: {}:{}, 原因: {}",
                            clientConnectionId, sessionId, targetHost, targetPort,
                            sendFuture.cause().getMessage());
                }
            });

        } catch (Exception e) {
            logger.error("客户端连接 {} 会话 {} 创建UDP转发失败: {}",
                    clientConnectionId, sessionId, e.getMessage(), e);
        }
    }

    /**
     * 将UDP响应转发回客户端
     */
    private void forwardUdpResponseToClient(int sessionId, io.netty.channel.socket.DatagramPacket responsePacket) {
        try {
            if (clientChannel == null || !clientChannel.isActive()) {
                logger.warn("客户端连接 {} 已断开，无法转发UDP响应", clientConnectionId);
                return;
            }

            java.net.InetSocketAddress sender = responsePacket.sender();
            ByteBuf responseData = responsePacket.content();

            // 构造UDP响应数据包格式：[HOST_LEN][HOST][PORT][DATA_LEN][DATA]
            ByteBuf udpResponsePacket = Unpooled.buffer();

            // 写入源主机信息
            String sourceHost = sender.getHostString();
            byte[] hostBytes = sourceHost.getBytes("UTF-8");
            udpResponsePacket.writeByte(hostBytes.length);
            udpResponsePacket.writeBytes(hostBytes);
            udpResponsePacket.writeShort(sender.getPort());

            // 写入数据长度和数据
            int dataLength = responseData.readableBytes();
            udpResponsePacket.writeShort(dataLength);
            udpResponsePacket.writeBytes(responseData);

            // 转换为字节数组
            byte[] packetData = new byte[udpResponsePacket.readableBytes()];
            udpResponsePacket.readBytes(packetData);
            udpResponsePacket.release();

            // 创建UDP数据包发送给客户端
            MultiplexProtocol.Packet udpPacket = MultiplexProtocol.createUdpDataPacket(sessionId, packetData);
            ByteBuf responseBuffer = udpPacket.encode();

            clientChannel.writeAndFlush(responseBuffer).addListener(future -> {
                if (future.isSuccess()) {
                    logger.debug("客户端连接 {} 会话 {} UDP响应转发成功: {}:{}, 数据长度: {}",
                            clientConnectionId, sessionId, sourceHost, sender.getPort(), dataLength);
                } else {
                    logger.error("客户端连接 {} 会话 {} UDP响应转发失败: {}",
                            clientConnectionId, sessionId, future.cause().getMessage());
                }
            });

        } catch (Exception e) {
            logger.error("客户端连接 {} 会话 {} 转发UDP响应到客户端时发生异常: {}",
                    clientConnectionId, sessionId, e.getMessage(), e);
        }
    }

    /**
     * 设置后端处理器
     */
    private void setupBackendHandlerTCP(Channel backendChannel, int sessionId) throws Exception {
        if (backendChannel == null || !backendChannel.isActive()) {
            throw new IllegalStateException("后端连接无效");
        }

        // 清理可能存在的旧处理器
        ChannelPipeline pipeline = backendChannel.pipeline();

        // 移除可能存在的旧的multiplexBackendHandler
        if (pipeline.get("multiplexBackendHandler") != null) {
            pipeline.remove("multiplexBackendHandler");
            logger.debug("移除旧的后端处理器");
        }

        // 使用专门的多路复用后端处理器
        MultiplexBackendHandler backendHandler = new MultiplexBackendHandler(
                clientChannel, sessionId, clientConnectionId,
                (sid, reason) -> cleanupSessionAndNotifyClient(sid, reason));

        backendChannel.pipeline().addLast("multiplexBackendHandler", backendHandler);

        logger.debug("客户端连接 {} 会话 {} 设置后端处理器完成: {}",
                clientConnectionId, sessionId, backendChannel.remoteAddress());
    }

    /**
     * 清理会话并通知客户端
     */
    private void cleanupSessionAndNotifyClient(int sessionId, String reason) {
        // 从会话映射中移除
        Channel removedChannel = sessions.remove(sessionId);
        String hostKey = sessionHostKeys.remove(sessionId);

        if (removedChannel != null) {
            logger.debug("客户端连接 {} 会话 {} {}: {} (主机: {})",
                    clientConnectionId, sessionId, reason,
                    removedChannel.remoteAddress() != null ? removedChannel.remoteAddress() : "unknown",
                    hostKey != null ? hostKey : "unknown");

            // 回收会话ID以供重用
            if (sessionId > 0 && sessionId <= 10000) {
                reusableSessionIds.offer(sessionId);
                logger.debug("回收会话ID {} 以供重用 (连接ID: {})", sessionId, clientConnectionId);
            }

            // 发送关闭包给客户端
            if (clientChannel != null && clientChannel.isActive()) {
                try {
                    MultiplexProtocol.Packet closePacket = MultiplexProtocol.createClosePacket(sessionId);
                    ByteBuf buffer = closePacket.encode();
                    clientChannel.writeAndFlush(buffer);
                    logger.debug("客户端连接 {} 会话 {} 已发送关闭包到客户端",
                            clientConnectionId, sessionId);
                } catch (Exception e) {
                    logger.warn("客户端连接 {} 会话 {} 发送关闭包时出现异常: {}",
                            clientConnectionId, sessionId, e.getMessage());
                }
            }

            // 优化：尝试归还连接到连接池而不是直接关闭
            if (hostKey != null && ConnectionPoolConfig.ENABLE_CONNECTION_POOL && removedChannel.isActive()) {
                try {
                    ConnectionPool.getInstance().returnConnection(hostKey, removedChannel);
                    logger.debug("会话 {} 连接已归还到连接池: {}", sessionId, hostKey);
                } catch (Exception e) {
                    logger.warn("归还连接到连接池失败，直接关闭: {}", e.getMessage());
                    removedChannel.close();
                }
            } else {
                // 直接关闭连接
                if (removedChannel.isActive()) {
                    removedChannel.close();
                }
            }
        }
    }

    /**
     * 处理关闭包
     */
    private void handleClosePacket(MultiplexProtocol.Packet packet) {
        int sessionId = packet.getSessionId();
        Channel backendChannel = sessions.remove(sessionId);
        String hostKey = sessionHostKeys.remove(sessionId); // 获取并移除主机键值

        // 回收会话ID以供重用
        if (sessionId > 0 && sessionId <= 10000) {
            reusableSessionIds.offer(sessionId);
            logger.debug("回收会话ID {} 以供重用 (连接ID: {})", sessionId, clientConnectionId);
        }

        if (backendChannel != null && backendChannel.isActive()) {
            logger.debug("客户端连接 {} 关闭会话 {}", clientConnectionId, sessionId);

            // 尝试归还连接到连接池
            if (shouldReturnConnectionToPool(backendChannel, hostKey)) {
                try {
                    // 归还连接到连接池
                    ConnectionPool.getInstance().returnConnection(hostKey, backendChannel);
                    logger.debug("客户端连接 {} 会话 {} 连接已归还到连接池: {}",
                            clientConnectionId, sessionId, hostKey);
                } catch (Exception e) {
                    logger.warn("归还连接到连接池失败 (连接ID: {}, 会话: {}): {}",
                            clientConnectionId, sessionId, e.getMessage());
                    // 归还失败，直接关闭连接
                    backendChannel.close();
                }
            } else {
                // 直接关闭连接
                logger.debug("连接不适合归还到连接池，直接关闭: 连接ID: {}, 会话: {}",
                        clientConnectionId, sessionId);
                backendChannel.close();
            }
        } else {
            logger.debug("客户端连接 {} 尝试关闭不存在的会话 {}", clientConnectionId, sessionId);
        }
    }

    /**
     * 判断连接是否应该归还到连接池
     */
    private boolean shouldReturnConnectionToPool(Channel backendChannel, String hostKey) {
        // 检查连接池是否启用
        if (!ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
            return false;
        }

        // 检查主机键值是否有效
        if (hostKey == null || hostKey.trim().isEmpty()) {
            return false;
        }

        // 检查连接基本状态（放宽为只要求active/open，writable可能短暂为false）
        if (!backendChannel.isActive() || !backendChannel.isOpen()) {
            return false;
        }

        // 检查连接是否有异常状态
        try {
            // 可以在这里添加更多的连接状态检查
            // 例如检查是否有未完成的请求、连接是否被标记为关闭等

            return true;
        } catch (Exception e) {
            logger.warn("检查连接状态时发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat(ChannelHandlerContext ctx, MultiplexProtocol.Packet packet) {
        try {
            // 回复心跳
            MultiplexProtocol.Packet heartbeatResponse = MultiplexProtocol.createHeartbeatPacket();
            ByteBuf response = heartbeatResponse.encode();
            ctx.writeAndFlush(response);
            logger.debug("客户端连接 {} 回复心跳包", clientConnectionId);
        } catch (Exception e) {
            logger.warn("客户端连接 {} 回复心跳包时出现异常: {}", clientConnectionId, e.getMessage());
        }
    }

    /**
     * 发送连接响应V2（包含分配的sessionId）
     */
    private void sendConnectResponseV2(ChannelHandlerContext ctx, int sessionId, byte status) {
        try {
            MultiplexProtocol.Packet response = MultiplexProtocol.createConnectResponseV2(sessionId, status);
            ByteBuf buffer = response.encode();
            ctx.writeAndFlush(buffer);

            String statusText = (status == MultiplexProtocol.STATUS_SUCCESS) ? "成功" : "失败";
            logger.debug("客户端连接 {} 会话 {} 发送连接响应V2: {}",
                    clientConnectionId, sessionId, statusText);
        } catch (Exception e) {
            logger.error("客户端连接 {} 会话 {} 发送连接响应V2时出现异常: {}",
                    clientConnectionId, sessionId, e.getMessage(), e);
        }
    }

    /**
     * 获取当前活跃会话数
     */
    public int getActiveSessionCount() {
        return sessions.size();
    }

    /**
     * 获取会话统计信息
     */
    public String getSessionStats() {
        return String.format("活跃会话: %d, 可重用ID: %d, 下一个ID: %d",
                sessions.size(), reusableSessionIds.size(), sessionIdGenerator.get());
    }

    /**
     * 获取客户端连接ID
     */
    public long getClientConnectionId() {
        return clientConnectionId;
    }

    /**
     * 格式化地址用于日志显示
     * IPv6地址会被格式化为 [host]:port 的形式
     */
    private String formatAddressForDisplay(String host, int port) {
        if (host.contains(":") && !host.startsWith("[")) {
            // IPv6地址，添加方括号
            return "[" + host + "]:" + port;
        } else {
            // IPv4地址或域名
            return host + ":" + port;
        }
    }

    /**
     * 安全地分配会话ID，支持ID重用
     */
    private int allocateSessionId() {
        // 首先尝试重用已释放的会话ID
        Integer reusedId = reusableSessionIds.poll();
        if (reusedId != null && !sessions.containsKey(reusedId)) {
            logger.debug("重用会话ID: {} (连接ID: {})", reusedId, clientConnectionId);
            return reusedId;
        }

        // 如果没有可重用的ID，生成新的ID
        int maxAttempts = 100;
        int attempts = 0;

        while (attempts < maxAttempts) {
            int sessionId = sessionIdGenerator.getAndIncrement();

            // 避免sessionId溢出，重置为1
            if (sessionId <= 0 || sessionId > 10000) {
                sessionIdGenerator.set(1);
                sessionId = sessionIdGenerator.getAndIncrement();
            }

            // 检查是否已存在
            if (!sessions.containsKey(sessionId)) {
                return sessionId;
            }

            attempts++;
        }

        // 如果无法分配，返回-1表示失败
        logger.error("无法分配会话ID，尝试了{}次 (连接ID: {})", maxAttempts, clientConnectionId);
        return -1;
    }
}
