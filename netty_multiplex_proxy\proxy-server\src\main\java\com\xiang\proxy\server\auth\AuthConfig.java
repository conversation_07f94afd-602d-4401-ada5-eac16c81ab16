package com.xiang.proxy.server.auth;

import com.xiang.proxy.server.config.ProxyServerConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 认证配置类
 * 管理代理服务器的认证相关配置
 * 现在使用新的 YAML 配置管理器
 */
public class AuthConfig {
    private static final Logger logger = LoggerFactory.getLogger(AuthConfig.class);

    private static ProxyServerConfigManager getConfigManager() {
        return ProxyServerConfigManager.getInstance();
    }

    /**
     * 验证用户名密码
     */
    public static boolean validateCredentials(String username, String password) {
        return getConfigManager().validateCredentials(username, password);
    }

    /**
     * 检查是否启用认证
     */
    public static boolean isAuthEnabled() {
        return getConfigManager().isAuthEnabled();
    }

    /**
     * 获取认证超时时间（秒）
     */
    public static int getAuthTimeoutSeconds() {
        return getConfigManager().getAuthTimeoutSeconds();
    }
}
