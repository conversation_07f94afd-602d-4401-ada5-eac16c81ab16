package com.xiang.controller;

import com.xiang.chat.core.R;
import com.xiang.dto.TokenInfo;
import com.xiang.dto.UserInfo;
import com.xiang.dto.ValidationResult;
import com.xiang.entity.UserEntity;
import com.xiang.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;

@Slf4j
@RestController
@RequestMapping("/api/jwt")
@RequiredArgsConstructor
public class JwtValidationController {

    private final JwtDecoder jwtDecoder;
    private final UserService userService;

    /**
     * 验证JWT token并返回用户信息
     */
    @PostMapping("/validate")
    public R<ValidationResult> validateToken(@RequestParam("token") String token) {
        if (token == null || token.trim().isEmpty()) {
            return R.error("Token不能为空");
        }

        try {
            // 移除Bearer前缀
            String actualToken = token;
            if (token.startsWith("Bearer ")) {
                actualToken = token.substring(7);
            }

            // 解码JWT token
            Jwt jwt = jwtDecoder.decode(actualToken);

            // 检查token是否过期
            Instant now = Instant.now();
            if (jwt.getExpiresAt() != null && jwt.getExpiresAt().isBefore(now)) {
                return R.error("Token已过期");
            }

            // 获取用户名
            String username = jwt.getClaimAsString("sub");
            if (username == null) {
                username = jwt.getClaimAsString("username");
            }

            if (username == null) {
                return R.error("Token中缺少用户信息");
            }

            // 查询用户信息
            UserEntity userEntity = userService.findByUsername(username);
            if (userEntity == null) {
                return R.error("用户不存在");
            }

            if (userEntity.getStatus() == 0) {
                return R.error("用户已被禁用");
            }

            // 构建用户信息DTO
            UserInfo userInfo = new UserInfo(
                    userEntity.getId(),
                    userEntity.getUsername(),
                    userEntity.getNickname(),
                    userEntity.getEmail(),
                    userEntity.getRole(),
                    userEntity.getAvatarUrl()
            );

            // 构建Token信息DTO
            TokenInfo tokenInfo = new TokenInfo(
                    jwt.getSubject(),
                    jwt.getIssuedAt(),
                    jwt.getExpiresAt(),
                    jwt.getIssuer().toString()
            );

            // 构建验证结果
            ValidationResult result = new ValidationResult(userInfo, tokenInfo);

            return R.success(result);

        } catch (Exception e) {
            log.error("JWT token validation failed", e);
            return R.error("Token验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前认证用户信息（用于已认证的请求）
     */
    @GetMapping("/userinfo")
    public R<UserInfo> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication instanceof JwtAuthenticationToken jwtAuth) {
            Jwt jwt = jwtAuth.getToken();
            String username = jwt.getClaimAsString("sub");
            if (username == null) {
                username = jwt.getClaimAsString("username");
            }

            if (username != null) {
                UserEntity userEntity = userService.findByUsername(username);
                if (userEntity != null) {
                    UserInfo userInfo = new UserInfo(
                            userEntity.getId(),
                            userEntity.getUsername(),
                            userEntity.getNickname(),
                            userEntity.getEmail(),
                            userEntity.getRole(),
                            userEntity.getAvatarUrl()
                    );
                    return R.success(userInfo);
                }
            }
        }

        return R.error("未认证或token无效");
    }
}