package com.xiang.proxy.server.core;

import com.xiang.proxy.server.outbound.OutboundConnection;

import java.util.HashMap;
import java.util.Map;

/**
 * 代理响应模型
 */
public class ProxyResponse {
    private final String requestId;
    private final boolean success;
    private final String message;
    private final OutboundConnection connection;
    private final Map<String, Object> attributes;
    private final long timestamp;

    private ProxyResponse(Builder builder) {
        this.requestId = builder.requestId;
        this.success = builder.success;
        this.message = builder.message;
        this.connection = builder.connection;
        this.attributes = new HashMap<>(builder.attributes);
        this.timestamp = builder.timestamp > 0 ? builder.timestamp : System.currentTimeMillis();
    }

    // Getters
    public String getRequestId() {
        return requestId;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMessage() {
        return message;
    }

    public OutboundConnection getConnection() {
        return connection;
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public long getTimestamp() {
        return timestamp;
    }

    // 便捷方法
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

    public <T> T getAttribute(String key, T defaultValue) {
        T value = getAttribute(key);
        return value != null ? value : defaultValue;
    }

    public boolean hasConnection() {
        return connection != null;
    }

    @Override
    public String toString() {
        return String.format("ProxyResponse{requestId=%s, success=%s, message=%s, hasConnection=%s}",
                requestId, success, message, hasConnection());
    }

    // Builder模式
    public static class Builder {
        private String requestId;
        private boolean success;
        private String message;
        private OutboundConnection connection;
        private Map<String, Object> attributes = new HashMap<>();
        private long timestamp;

        public Builder requestId(String requestId) {
            this.requestId = requestId;
            return this;
        }

        public Builder success(boolean success) {
            this.success = success;
            return this;
        }

        public Builder message(String message) {
            this.message = message;
            return this;
        }

        public Builder connection(OutboundConnection connection) {
            this.connection = connection;
            return this;
        }

        public Builder attribute(String key, Object value) {
            this.attributes.put(key, value);
            return this;
        }

        public Builder attributes(Map<String, Object> attributes) {
            this.attributes.putAll(attributes);
            return this;
        }

        public Builder timestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public ProxyResponse build() {
            if (requestId == null) {
                throw new IllegalArgumentException("Request ID is required");
            }
            return new ProxyResponse(this);
        }
    }

    // 静态工厂方法
    public static Builder builder() {
        return new Builder();
    }

    public static ProxyResponse success(String requestId, OutboundConnection connection) {
        return builder()
                .requestId(requestId)
                .success(true)
                .message("Success")
                .connection(connection)
                .build();
    }

    public static ProxyResponse success(String requestId, String message) {
        return builder()
                .requestId(requestId)
                .success(true)
                .message(message)
                .build();
    }

    public static ProxyResponse failure(String requestId, String message) {
        return builder()
                .requestId(requestId)
                .success(false)
                .message(message)
                .build();
    }

    public static ProxyResponse error(String requestId, String message) {
        return builder()
                .requestId(requestId)
                .success(false)
                .message("Error: " + message)
                .build();
    }
}