package com.xiang.proxy.server.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;

/**
 * 通用缓存管理器
 * 提供高性能的缓存功能，支持TTL、LRU、统计等特性
 */
public class CacheManager<K, V> {
    private static final Logger logger = LoggerFactory.getLogger(CacheManager.class);
    
    private final ConcurrentHashMap<K, CacheEntry<V>> cache = new ConcurrentHashMap<>();
    private final String cacheName;
    private final long defaultTtl;
    private final int maxSize;
    private final ScheduledExecutorService cleanupExecutor;
    
    // 统计信息
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);
    private final AtomicLong evictionCount = new AtomicLong(0);
    
    public CacheManager(String cacheName, long defaultTtlMs, int maxSize) {
        this.cacheName = cacheName;
        this.defaultTtl = defaultTtlMs;
        this.maxSize = maxSize;
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "Cache-" + cacheName + "-Cleanup");
            t.setDaemon(true);
            return t;
        });
        
        // 启动定期清理任务
        startCleanupTask();
        
        logger.info("缓存管理器已启动: {}, TTL={}ms, 最大大小={}", cacheName, defaultTtl, maxSize);
    }
    
    /**
     * 获取缓存值
     */
    public V get(K key) {
        CacheEntry<V> entry = cache.get(key);
        if (entry == null) {
            missCount.incrementAndGet();
            return null;
        }
        
        if (isExpired(entry)) {
            cache.remove(key, entry);
            missCount.incrementAndGet();
            return null;
        }
        
        entry.updateAccessTime();
        hitCount.incrementAndGet();
        return entry.getValue();
    }
    
    /**
     * 获取缓存值，如果不存在则计算并缓存
     */
    public V getOrCompute(K key, Function<K, V> valueSupplier) {
        V value = get(key);
        if (value != null) {
            return value;
        }
        
        // 计算新值
        V newValue = valueSupplier.apply(key);
        if (newValue != null) {
            put(key, newValue);
        }
        
        return newValue;
    }
    
    /**
     * 放入缓存
     */
    public void put(K key, V value) {
        put(key, value, defaultTtl);
    }
    
    /**
     * 放入缓存，指定TTL
     */
    public void put(K key, V value, long ttlMs) {
        if (key == null || value == null) {
            return;
        }
        
        // 检查缓存大小限制
        if (cache.size() >= maxSize) {
            evictLRU();
        }
        
        CacheEntry<V> entry = new CacheEntry<>(value, ttlMs);
        cache.put(key, entry);
    }
    
    /**
     * 移除缓存项
     */
    public V remove(K key) {
        CacheEntry<V> entry = cache.remove(key);
        return entry != null ? entry.getValue() : null;
    }
    
    /**
     * 清空缓存
     */
    public void clear() {
        cache.clear();
        logger.info("缓存已清空: {}", cacheName);
    }
    
    /**
     * 检查缓存项是否过期
     */
    private boolean isExpired(CacheEntry<V> entry) {
        return System.currentTimeMillis() > entry.getExpirationTime();
    }
    
    /**
     * LRU淘汰策略
     */
    private void evictLRU() {
        if (cache.isEmpty()) {
            return;
        }
        
        // 找到最久未访问的条目
        K lruKey = cache.entrySet().stream()
            .min((e1, e2) -> Long.compare(e1.getValue().getLastAccessTime(), e2.getValue().getLastAccessTime()))
            .map(entry -> entry.getKey())
            .orElse(null);
        
        if (lruKey != null) {
            cache.remove(lruKey);
            evictionCount.incrementAndGet();
            logger.debug("LRU淘汰缓存项: {} from {}", lruKey, cacheName);
        }
    }
    
    /**
     * 启动清理任务
     */
    private void startCleanupTask() {
        cleanupExecutor.scheduleWithFixedDelay(() -> {
            try {
                cleanupExpiredEntries();
            } catch (Exception e) {
                logger.warn("缓存清理任务异常: {}", cacheName, e);
            }
        }, 60, 60, TimeUnit.SECONDS); // 每分钟清理一次
    }
    
    /**
     * 清理过期条目
     */
    private void cleanupExpiredEntries() {
        long currentTime = System.currentTimeMillis();
        AtomicInteger removedCount = new AtomicInteger(0);

        cache.entrySet().removeIf(entry -> {
            if (currentTime > entry.getValue().getExpirationTime()) {
                removedCount.incrementAndGet();
                return true;
            }
            return false;
        });

        int removed = removedCount.get();
        if (removed > 0) {
            logger.debug("清理了 {} 个过期缓存项 from {}", removed, cacheName);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getStats() {
        return new CacheStats(
            cacheName,
            cache.size(),
            hitCount.get(),
            missCount.get(),
            evictionCount.get()
        );
    }
    
    /**
     * 记录缓存统计信息
     */
    public void logStats() {
        CacheStats stats = getStats();
        logger.info("=== 缓存统计: {} ===", cacheName);
        logger.info("大小: {}/{} 命中率: {}% 淘汰数: {}", 
                   stats.size, maxSize, String.format("%.2f", stats.getHitRate()), stats.evictionCount);
    }
    
    /**
     * 关闭缓存管理器
     */
    public void shutdown() {
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        clear();
        logger.info("缓存管理器已关闭: {}", cacheName);
    }
    
    /**
     * 缓存条目
     */
    private static class CacheEntry<V> {
        private final V value;
        private final long expirationTime;
        private volatile long lastAccessTime;
        
        public CacheEntry(V value, long ttlMs) {
            this.value = value;
            this.lastAccessTime = System.currentTimeMillis();
            this.expirationTime = this.lastAccessTime + ttlMs;
        }
        
        public V getValue() {
            return value;
        }
        
        public long getExpirationTime() {
            return expirationTime;
        }
        
        public long getLastAccessTime() {
            return lastAccessTime;
        }
        
        public void updateAccessTime() {
            this.lastAccessTime = System.currentTimeMillis();
        }
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        public final String name;
        public final int size;
        public final long hitCount;
        public final long missCount;
        public final long evictionCount;
        
        public CacheStats(String name, int size, long hitCount, long missCount, long evictionCount) {
            this.name = name;
            this.size = size;
            this.hitCount = hitCount;
            this.missCount = missCount;
            this.evictionCount = evictionCount;
        }
        
        public double getHitRate() {
            long total = hitCount + missCount;
            return total > 0 ? (double) hitCount / total * 100.0 : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("CacheStats{name='%s', size=%d, hitRate=%.2f%%, evictions=%d}",
                               name, size, getHitRate(), evictionCount);
        }
    }
}
