package com.xiang.proxy.server.handler;

import com.xiang.proxy.server.protocol.MultiplexProtocol;
import com.xiang.proxy.server.exception.ExceptionHandler;

import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 多路复用后端连接处理器
 * 专门处理多路复用代理的后端连接，提供更好的异常处理和会话管理
 */
public class MultiplexBackendHandler extends ChannelInboundHandlerAdapter {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexBackendHandler.class);

    private final Channel clientChannel;
    private final int sessionId;
    private final long clientConnectionId;
    private final SessionCleanupCallback cleanupCallback;
    
    // 异常处理器
    private final ExceptionHandler exceptionHandler = new ExceptionHandler();

    /**
     * 会话清理回调接口
     */
    public interface SessionCleanupCallback {
        void cleanupSession(int sessionId, String reason);
    }

    /**
     * 构造函数
     * @param clientChannel 客户端通道
     * @param sessionId 会话ID
     * @param clientConnectionId 客户端连接ID
     * @param cleanupCallback 会话清理回调
     */
    public MultiplexBackendHandler(Channel clientChannel, int sessionId, 
                                 long clientConnectionId, SessionCleanupCallback cleanupCallback) {
        this.clientChannel = clientChannel;
        this.sessionId = sessionId;
        this.clientConnectionId = clientConnectionId;
        this.cleanupCallback = cleanupCallback;
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        ByteBuf data = (ByteBuf) msg;
        try {
            if (clientChannel != null && clientChannel.isActive()) {
                // 将后端数据包装成多路复用数据包发送给客户端
                byte[] bytes = new byte[data.readableBytes()];
                data.readBytes(bytes);

                MultiplexProtocol.Packet dataPacket =
                        MultiplexProtocol.createDataPacket(sessionId, bytes);
                ByteBuf packetBuffer = dataPacket.encode();
                clientChannel.writeAndFlush(packetBuffer);

                logger.debug("客户端连接 {} 会话 {} 转发数据到客户端: {} bytes",
                    clientConnectionId, sessionId, bytes.length);
            } else {
                logger.warn("客户端连接 {} 不可用，丢弃会话 {} 的数据 ({} bytes)",
                    clientConnectionId, sessionId, data.readableBytes());
            }
        } catch (Exception e) {
            logger.error("客户端连接 {} 会话 {} 转发数据到客户端时发生异常: {}",
                clientConnectionId, sessionId, e.getMessage(), e);
        } finally {
            data.release();
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.debug("客户端连接 {} 会话 {} 后端连接断开: {}",
            clientConnectionId, sessionId, ctx.channel().remoteAddress());

        // 通过回调清理会话并通知客户端
        if (cleanupCallback != null) {
            cleanupCallback.cleanupSession(sessionId, "后端连接断开");
        }

        super.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        String contextInfo = String.format("后端连接 (客户端连接ID: %d, 会话ID: %d)", 
                clientConnectionId, sessionId);
        
        // 使用统一异常处理器处理异常
        ExceptionHandler.ExceptionResult result = exceptionHandler.handleChannelException(ctx, cause, contextInfo);
        
        // 通过回调清理会话并通知客户端
        if (cleanupCallback != null) {
            cleanupCallback.cleanupSession(sessionId, result.getMessage());
        }

        // 根据处理结果决定是否关闭连接
        if (result.shouldClose()) {
            exceptionHandler.safeCloseContext(ctx, "后端连接异常处理后关闭");
        }
    }

    /**
     * 获取会话ID
     */
    public int getSessionId() {
        return sessionId;
    }

    /**
     * 获取客户端连接ID
     */
    public long getClientConnectionId() {
        return clientConnectionId;
    }
}
