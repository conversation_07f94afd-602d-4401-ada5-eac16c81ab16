package com.xiang.proxy.server.util;

import com.xiang.proxy.server.config.ProxyServerConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 服务器端IP地理位置判断工具
 * 用于判断IP地址是否属于中国地区
 * 优化版本，适用于高并发服务器环境
 */
public class GeoIPUtil {
    private static final Logger logger = LoggerFactory.getLogger(GeoIPUtil.class);
    // 在线数据源URL - 从配置文件加载
    private static java.util.List<String> getOnlineDataSources() {
        try {
            ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
            return configManager.getChinaIpRangesUrls();
        } catch (Exception e) {
            // 如果配置加载失败，使用默认值
            return java.util.Arrays.asList(
                "https://raw.githubusercontent.com/mayaxcn/china-ip-list/master/chnroute.txt",
                "https://raw.githubusercontent.com/17mon/china_ip_list/master/china_ip_list.txt"
            );
        }
    }
    private static final GeoIPUtil INSTANCE = new GeoIPUtil();

    // 中国IP段配置文件名
    private static final String CHINA_IP_RANGES_FILE = "china-ip-ranges.txt";

    // 中国大陆主要IP段（从最新APNIC数据加载）
    private final Set<String> chinaIPRanges = ConcurrentHashMap.newKeySet();

    // IP判断结果缓存 - 提高性能
    private final Map<String, Boolean> ipCache = new ConcurrentHashMap<>();
    private final Map<String, Long> ipCacheTime = new ConcurrentHashMap<>();
    private static final long IP_CACHE_TTL = 3600000; // 1小时缓存
    private static final int MAX_CACHE_SIZE = 10000; // 最大缓存条目数


    // 定时任务调度器
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "GeoIPUtil-Scheduler");
        t.setDaemon(true);
        return t;
    });

    private GeoIPUtil() {
        // 初始化中国IP段
        initializeChinaIPRanges();

        // 启动定期清理缓存任务
        scheduler.scheduleAtFixedRate(this::cleanupCache, 1, 1, TimeUnit.HOURS);

        // 启动定期更新IP段任务（根据配置的间隔）
        ProxyServerConfigManager configManager = ProxyServerConfigManager.getInstance();
        if (configManager.isAutoUpdateIPRanges()) {
            int updateInterval = configManager.getUpdateIntervalHours();
            scheduler.scheduleAtFixedRate(this::updateIPRangesFromOnline, updateInterval, updateInterval, TimeUnit.HOURS);
            logger.info("已启用IP段数据自动更新，更新间隔: {} 小时", updateInterval);
        } else {
            logger.info("IP段数据自动更新已禁用");
        }
    }

    public static GeoIPUtil getInstance() {
        return INSTANCE;
    }

    /**
     * 初始化中国IP段
     * 优先级：china-ip-ranges.txt文件 -> 在线数据源（成功后写入文件） -> 内置基础IP段
     */
    private void initializeChinaIPRanges() {
        // 首先尝试从china-ip-ranges.txt文件加载
        if (loadChinaIPRangesFromSpecificFile(CHINA_IP_RANGES_FILE)) {
            logger.info("成功从{}加载 {} 个中国IP段", CHINA_IP_RANGES_FILE, chinaIPRanges.size());
            return;
        }

        // 如果文件不存在，尝试从在线数据源加载
        if (loadChinaIPRangesFromOnline()) {
            logger.info("成功从在线数据源加载 {} 个中国IP段", chinaIPRanges.size());
            // 网络加载成功后，保存到china-ip-ranges.txt文件
            saveChinaIPRangesToFile();
            return;
        }

        // 如果都失败，使用内置的基础IP段
        loadBuiltinChinaIPRanges();
        logger.info("使用内置IP段，已加载 {} 个中国IP段", chinaIPRanges.size());
    }

    /**
     * 从指定文件加载中国IP段
     */
    private boolean loadChinaIPRangesFromSpecificFile(String filename) {
        // 首先尝试从外部文件系统加载
        if (loadFromExternalFile(filename)) {
            return true;
        }

        // 然后尝试从classpath加载
        return loadFromClasspath(filename);
    }

    /**
     * 从外部文件系统加载IP段
     */
    private boolean loadFromExternalFile(String filename) {
        // 1. 首先尝试从配置目录加载
        if (loadFromConfigDirectory(filename)) {
            return true;
        }

        // 2. 然后尝试从当前目录加载
        File file = new File(filename);
        if (!file.exists() || !file.isFile()) {
            return false;
        }

        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            return loadIPRangesFromReader(reader, "外部文件: " + filename);
        } catch (IOException e) {
            logger.warn("从外部文件加载IP段失败: {}", filename, e);
            return false;
        }
    }

    /**
     * 从配置目录加载IP段文件
     */
    private boolean loadFromConfigDirectory(String filename) {
        try {
            // 获取配置目录路径
            String configDir = getConfigDirectory();
            if (configDir == null || configDir.trim().isEmpty()) {
                return false;
            }

            File configFile = new File(configDir, filename);
            if (!configFile.exists() || !configFile.isFile()) {
                logger.debug("配置目录中未找到文件: {}", configFile.getAbsolutePath());
                return false;
            }

            try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
                boolean success = loadIPRangesFromReader(reader, "配置目录文件: " + configFile.getAbsolutePath());
                if (success) {
                    logger.info("成功从配置目录加载IP段文件: {}", configFile.getAbsolutePath());
                }
                return success;
            }
        } catch (Exception e) {
            logger.warn("从配置目录加载IP段文件失败: {}", filename, e);
            return false;
        }
    }

    /**
     * 获取配置目录路径
     */
    private String getConfigDirectory() {
        try {
            // 从 app.properties 读取配置目录
            Properties props = new Properties();
            try (InputStream is = getClass().getClassLoader().getResourceAsStream("app.properties")) {
                if (is != null) {
                    props.load(is);
                    String configDir = props.getProperty("config.ext.dir");
                    if (configDir != null && !configDir.trim().isEmpty()) {
                        return configDir.trim();
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("获取配置目录失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 从classpath加载IP段
     */
    private boolean loadFromClasspath(String filename) {
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(filename)) {
            if (is != null) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {
                    return loadIPRangesFromReader(reader, "classpath: " + filename);
                }
            }
        } catch (IOException e) {
            logger.warn("从classpath加载IP段失败: {}", filename, e);
        }
        return false;
    }

    /**
     * 从Reader加载IP段数据
     */
    private boolean loadIPRangesFromReader(BufferedReader reader, String source) throws IOException {
        String line;
        int count = 0;

        while ((line = reader.readLine()) != null) {
            line = line.trim();
            if (!line.isEmpty() && !line.startsWith("#")) {
                if (isValidIPRange(line)) {
                    chinaIPRanges.add(line);
                    count++;
                }
            }
        }

        if (count > 0) {
            logger.info("从{}成功加载 {} 个中国IP段", source, count);
            return true;
        }

        return false;
    }

    /**
     * 从在线数据源加载中国IP段
     */
    private boolean loadChinaIPRangesFromOnline() {
        java.util.List<String> onlineDataSources = getOnlineDataSources();
        for (String dataSource : onlineDataSources) {
            try {
                logger.info("尝试从在线数据源加载: {}", dataSource);

                URL url = new URL(dataSource);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000); // 10秒连接超时
                connection.setReadTimeout(30000);    // 30秒读取超时
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Java GeoIP Server)");

                if (connection.getResponseCode() == 200) {
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(connection.getInputStream()))) {

                        if (loadIPRangesFromReader(reader, "在线数据源: " + dataSource)) {
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                logger.warn("从在线数据源加载失败: {}, 错误: {}", dataSource, e.getMessage());
            }
        }
        return false;
    }

    /**
     * 定期从在线数据源更新IP段
     */
    private void updateIPRangesFromOnline() {
        try {
            logger.info("开始定期更新中国IP段数据");
            Set<String> newRanges = new HashSet<>();

            // 临时保存当前数据
            Set<String> oldRanges = new HashSet<>(chinaIPRanges);

            // 清空当前数据
            chinaIPRanges.clear();

            // 尝试从在线源加载新数据
            if (loadChinaIPRangesFromOnline()) {
                // 更新成功，保存到文件
                saveChinaIPRangesToFile();
                // 清理IP缓存，强制重新判断
                ipCache.clear();
                ipCacheTime.clear();
                logger.info("中国IP段数据更新成功，新增 {} 个IP段", chinaIPRanges.size());
            } else {
                // 更新失败，恢复旧数据
                chinaIPRanges.addAll(oldRanges);
                logger.warn("中国IP段数据更新失败，继续使用旧数据");
            }
        } catch (Exception e) {
            logger.error("定期更新中国IP段数据时发生异常", e);
        }
    }

    /**
     * 保存中国IP段到文件
     */
    private void saveChinaIPRangesToFile() {
        // 优先保存到配置目录
        if (saveToConfigDirectory()) {
            return;
        }

        // 如果配置目录保存失败，保存到当前目录
        try (PrintWriter writer = new PrintWriter(new FileWriter(CHINA_IP_RANGES_FILE))) {
            writer.println("# 中国IP段数据");
            writer.println("# 更新时间: " + new Date());
            writer.println("# 数据来源: APNIC");
            writer.println();

            for (String range : chinaIPRanges) {
                writer.println(range);
            }

            logger.info("中国IP段数据已保存到当前目录文件: {}", CHINA_IP_RANGES_FILE);
        } catch (IOException e) {
            logger.warn("保存中国IP段数据到文件失败", e);
        }
    }

    /**
     * 保存IP段数据到配置目录
     */
    private boolean saveToConfigDirectory() {
        try {
            String configDir = getConfigDirectory();
            if (configDir == null || configDir.trim().isEmpty()) {
                return false;
            }

            File configDirFile = new File(configDir);
            if (!configDirFile.exists()) {
                if (!configDirFile.mkdirs()) {
                    logger.warn("无法创建配置目录: {}", configDir);
                    return false;
                }
            }

            File configFile = new File(configDirFile, CHINA_IP_RANGES_FILE);
            try (PrintWriter writer = new PrintWriter(new FileWriter(configFile))) {
                writer.println("# 中国IP段数据");
                writer.println("# 自动生成于: " + new Date());
                writer.println("# 总计: " + chinaIPRanges.size() + " 个IP段");
                writer.println();

                for (String range : chinaIPRanges) {
                    writer.println(range);
                }

                writer.flush();

                logger.info("中国IP段数据已保存到配置目录: {} ({} 个IP段)",
                        configFile.getAbsolutePath(), chinaIPRanges.size());
                return true;
            }
        } catch (Exception e) {
            logger.warn("保存中国IP段数据到配置目录失败", e);
            return false;
        }
    }

    /**
     * 加载内置的基础中国IP段
     */
    private void loadBuiltinChinaIPRanges() {
        // 添加一些主要的中国IP段作为备用
        String[] basicRanges = {
                "*******/24", "*******/23", "*******/21", "********/19",
                "*********/22", "*********/20", "*********/19", "*********/18",
                "********/11", "*********/11", "*********/10", "**********/9",
                "********/10", "*********/11", "*********/12", "**********/13",
                "********/11", "*********/11", "*********/10", "**********/10",
                "********/8", "********/11", "*********/11", "*********/10",
                "********/9", "**********/11", "**********/11", "**********/10",
                "********/10", "*********/11", "*********/11", "**********/9",
                "60.0.0.0/8", "********/10", "*********/11", "*********/11",
                "*********/10", "**********/11", "**********/11", "***********/9",
                "*********/10", "**********/11", "**********/11", "***********/10",
                "*********/8", "110.0.0.0/7", "*********/6", "*********/6",
                "120.0.0.0/6", "*********/6", "*********/8", "180.0.0.0/6",
                "*********/8", "*********/8", "*********/8", "210.0.0.0/7",
                "*********/7", "220.0.0.0/6", "*********/8", "*********/7"
        };

        chinaIPRanges.addAll(Arrays.asList(basicRanges));
    }

    /**
     * 判断IP地址是否属于中国
     *
     * @param ip IP地址字符串
     * @return true如果是中国IP，false否则
     */
    public boolean isChinaIP(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }

        // 检查缓存
        Boolean cachedResult = ipCache.get(ip);
        Long cacheTime = ipCacheTime.get(ip);
        if (cachedResult != null && cacheTime != null &&
                (System.currentTimeMillis() - cacheTime) < IP_CACHE_TTL) {
            return cachedResult;
        }

        boolean result = false;
        try {
            // 检查是否为私有地址
            InetAddress address = InetAddress.getByName(ip);
            if (address.isSiteLocalAddress() || address.isLoopbackAddress()) {
                logger.debug("IP {} 是私有地址，视为中国IP", ip);
                result = true;
            } else if (address.getAddress().length == 4) {
                // 检查IPv4地址
                result = isChinaIPv4(ip);
            } else {
                // IPv6地址处理
                result = isChinaIPv6(ip, address);
            }

        } catch (UnknownHostException e) {
            logger.warn("无效的IP地址: {}", ip);
            result = false;
        }

        // 更新缓存
        updateCache(ip, result);

        return result;
    }

    /**
     * 更新IP判断结果缓存
     */
    private void updateCache(String ip, boolean result) {
        // 检查缓存大小限制
        if (ipCache.size() >= MAX_CACHE_SIZE) {
            cleanupOldestCacheEntries();
        }

        ipCache.put(ip, result);
        ipCacheTime.put(ip, System.currentTimeMillis());
    }

    /**
     * 清理最旧的缓存条目
     */
    private void cleanupOldestCacheEntries() {
        // 清理25%的最旧条目
        int toRemove = MAX_CACHE_SIZE / 4;
        List<Map.Entry<String, Long>> entries = new ArrayList<>(ipCacheTime.entrySet());
        entries.sort(Map.Entry.comparingByValue());

        for (int i = 0; i < toRemove && i < entries.size(); i++) {
            String ip = entries.get(i).getKey();
            ipCache.remove(ip);
            ipCacheTime.remove(ip);
        }

        logger.debug("清理了 {} 个最旧的IP缓存条目", toRemove);
    }

    /**
     * 判断IPv6地址是否属于中国
     * 目前主要处理特殊地址，未来可扩展支持IPv6地理位置数据库
     */
    private boolean isChinaIPv6(String ip, InetAddress address) {
        // 检查是否为链路本地地址 (fe80::/10)
        if (address.isLinkLocalAddress()) {
            logger.debug("IPv6地址 {} 是链路本地地址，视为中国IP", ip);
            return true;
        }

        // 检查是否为唯一本地地址 (fc00::/7)
        byte[] addr = address.getAddress();
        if (addr.length == 16 && (addr[0] & 0xfe) == 0xfc) {
            logger.debug("IPv6地址 {} 是唯一本地地址，视为中国IP", ip);
            return true;
        }

        // 其他IPv6地址暂时返回false（可以后续扩展支持IPv6地理位置数据库）
        logger.debug("IPv6地址 {} 暂不支持地理位置判断，默认视为海外IP", ip);
        return false;
    }

    /**
     * 判断IPv4地址是否属于中国
     */
    private boolean isChinaIPv4(String ip) {
        try {
            long ipLong = ipToLong(ip);

            for (String range : chinaIPRanges) {
                if (isIPInRange(ipLong, range)) {
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            logger.debug("判断IPv4地址 {} 时发生异常: {}", ip, e.getMessage());
            return false;
        }
    }

    /**
     * 将IP地址转换为长整型
     */
    private long ipToLong(String ip) {
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            throw new IllegalArgumentException("Invalid IP address: " + ip);
        }

        long result = 0;
        for (int i = 0; i < 4; i++) {
            int part = Integer.parseInt(parts[i]);
            if (part < 0 || part > 255) {
                throw new IllegalArgumentException("Invalid IP address: " + ip);
            }
            result = (result << 8) + part;
        }
        return result;
    }

    /**
     * 检查IP是否在指定的CIDR范围内
     */
    private boolean isIPInRange(long ip, String cidr) {
        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return false;
            }

            long networkIP = ipToLong(parts[0]);
            int prefixLength = Integer.parseInt(parts[1]);

            if (prefixLength < 0 || prefixLength > 32) {
                return false;
            }

            long mask = (0xFFFFFFFFL << (32 - prefixLength)) & 0xFFFFFFFFL;

            return (ip & mask) == (networkIP & mask);
        } catch (Exception e) {
            logger.debug("检查IP范围时发生异常，CIDR: {}, 错误: {}", cidr, e.getMessage());
            return false;
        }
    }

    /**
     * 验证IP段格式是否有效
     */
    private boolean isValidIPRange(String range) {
        if (range == null || range.trim().isEmpty()) {
            return false;
        }

        try {
            String[] parts = range.split("/");
            if (parts.length != 2) {
                return false;
            }

            // 验证IP地址
            String[] ipParts = parts[0].split("\\.");
            if (ipParts.length != 4) {
                return false;
            }

            for (String part : ipParts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }

            // 验证前缀长度
            int prefixLength = Integer.parseInt(parts[1]);
            return prefixLength >= 0 && prefixLength <= 32;

        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 清理过期的缓存条目
     */
    private void cleanupCache() {
        long currentTime = System.currentTimeMillis();
        List<String> expiredKeys = new ArrayList<>();

        for (Map.Entry<String, Long> entry : ipCacheTime.entrySet()) {
            if (currentTime - entry.getValue() > IP_CACHE_TTL) {
                expiredKeys.add(entry.getKey());
            }
        }

        for (String key : expiredKeys) {
            ipCache.remove(key);
            ipCacheTime.remove(key);
        }

        if (!expiredKeys.isEmpty()) {
            logger.debug("清理了 {} 个过期的IP缓存条目", expiredKeys.size());
        }
    }

    /**
     * 获取统计信息
     */
    public String getStatsString() {
        return String.format("中国IP段数: %d, IP缓存条目数: %d, 缓存命中率: %.2f%%",
                chinaIPRanges.size(), ipCache.size(), getCacheHitRate() * 100);
    }

    /**
     * 计算缓存命中率（简单估算）
     */
    private double getCacheHitRate() {
        // 这里是一个简化的估算，实际应用中可以维护更详细的统计
        return ipCache.size() > 0 ? 0.85 : 0.0; // 假设85%的命中率
    }

    /**
     * 关闭资源
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
