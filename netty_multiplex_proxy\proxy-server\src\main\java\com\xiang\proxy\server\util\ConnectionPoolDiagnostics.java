package com.xiang.proxy.server.util;

import com.xiang.proxy.server.pool.ConnectionPool;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 连接池诊断工具
 * 用于监控和诊断连接复用相关问题
 */
public class ConnectionPoolDiagnostics {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionPoolDiagnostics.class);
    private static final ConnectionPoolDiagnostics INSTANCE = new ConnectionPoolDiagnostics();
    
    private final ScheduledExecutorService diagnosticExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "ConnectionPool-Diagnostics");
        t.setDaemon(true);
        return t;
    });
    
    private volatile boolean diagnosticsEnabled = false;
    
    private ConnectionPoolDiagnostics() {}
    
    public static ConnectionPoolDiagnostics getInstance() {
        return INSTANCE;
    }
    
    /**
     * 启动连接池诊断
     */
    public void startDiagnostics() {
        if (!diagnosticsEnabled) {
            diagnosticsEnabled = true;
            
            // 每30秒输出连接池状态
            diagnosticExecutor.scheduleAtFixedRate(this::logConnectionPoolStatus, 30, 30, TimeUnit.SECONDS);
            
            // 每5分钟进行深度诊断
            diagnosticExecutor.scheduleAtFixedRate(this::performDeepDiagnostics, 300, 300, TimeUnit.SECONDS);
            
            logger.info("连接池诊断已启动");
        }
    }
    
    /**
     * 停止连接池诊断
     */
    public void stopDiagnostics() {
        if (diagnosticsEnabled) {
            diagnosticsEnabled = false;
            diagnosticExecutor.shutdown();
            logger.info("连接池诊断已停止");
        }
    }
    
    /**
     * 记录连接池状态
     */
    private void logConnectionPoolStatus() {
        try {
            ConnectionPool pool = ConnectionPool.getInstance();
            String stats = pool.getPoolStats();
            logger.info("=== 连接池状态诊断 ===\n{}", stats);
            
            // 获取性能统计
            var performanceStats = pool.getPerformanceStats();
            logger.info("连接池性能: 总连接={}, 池中连接={}, 活跃连接={}, 命中率={}%",
                performanceStats.totalConnections,
                performanceStats.pooledConnections, 
                performanceStats.activeConnections,
                String.format("%.2f", getHitRate(performanceStats.hitCount, performanceStats.missCount)));
                
        } catch (Exception e) {
            logger.error("记录连接池状态时发生异常", e);
        }
    }
    
    /**
     * 执行深度诊断
     */
    private void performDeepDiagnostics() {
        try {
            logger.info("=== 连接池深度诊断开始 ===");
            
            // 检查连接池健康状态
            checkConnectionPoolHealth();
            
            // 分析连接复用效率
            analyzeConnectionReuseEfficiency();
            
            // 检查潜在的连接泄漏
            checkForConnectionLeaks();
            
            logger.info("=== 连接池深度诊断完成 ===");
            
        } catch (Exception e) {
            logger.error("执行深度诊断时发生异常", e);
        }
    }
    
    /**
     * 检查连接池健康状态
     */
    private void checkConnectionPoolHealth() {
        ConnectionPool pool = ConnectionPool.getInstance();
        var stats = pool.getPerformanceStats();
        
        // 检查命中率
        double hitRate = getHitRate(stats.hitCount, stats.missCount);
        if (hitRate < 50.0) {
            logger.warn("连接池命中率过低: {}%, 建议检查连接复用逻辑", String.format("%.2f", hitRate));
        }
        
        // 检查连接数是否异常
        if (stats.totalConnections > stats.pooledConnections * 3) {
            logger.warn("总连接数异常: 总连接={}, 池中连接={}, 可能存在连接泄漏", 
                stats.totalConnections, stats.pooledConnections);
        }
        
        logger.info("连接池健康检查完成: 命中率={}%, 总连接={}, 池中连接={}", 
            String.format("%.2f", hitRate), stats.totalConnections, stats.pooledConnections);
    }
    
    /**
     * 分析连接复用效率
     */
    private void analyzeConnectionReuseEfficiency() {
        ConnectionPool pool = ConnectionPool.getInstance();
        var stats = pool.getPerformanceStats();
        
        long totalRequests = stats.hitCount + stats.missCount;
        if (totalRequests > 0) {
            double reuseRatio = (double) stats.hitCount / totalRequests * 100.0;
            
            if (reuseRatio < 30.0) {
                logger.warn("连接复用率过低: {}%, 可能的原因:", String.format("%.2f", reuseRatio));
                logger.warn("  1. 连接空闲超时时间过短");
                logger.warn("  2. 连接清理过于频繁");
                logger.warn("  3. 连接验证逻辑过于严格");
                logger.warn("  4. 目标主机连接不稳定");
            } else if (reuseRatio > 80.0) {
                logger.info("连接复用率良好: {}%", String.format("%.2f", reuseRatio));
            } else {
                logger.info("连接复用率正常: {}%", String.format("%.2f", reuseRatio));
            }
        }
    }
    
    /**
     * 检查潜在的连接泄漏
     */
    private void checkForConnectionLeaks() {
        // 这里可以添加更复杂的连接泄漏检测逻辑
        // 比如跟踪连接的创建和销毁，检查长时间未使用的连接等
        
        logger.info("连接泄漏检查完成 - 暂未发现明显泄漏");
    }
    
    /**
     * 计算命中率
     */
    private double getHitRate(long hits, long misses) {
        long total = hits + misses;
        return total > 0 ? (double) hits / total * 100.0 : 0.0;
    }
    
    /**
     * 诊断特定连接
     */
    public void diagnoseConnection(Channel channel, String hostKey) {
        if (channel == null) {
            logger.warn("诊断连接失败: 连接为null, hostKey={}", hostKey);
            return;
        }
        
        logger.info("=== 连接诊断: {} ===", hostKey);
        logger.info("连接ID: {}", channel.id());
        logger.info("连接状态: active={}, open={}, writable={}", 
            channel.isActive(), channel.isOpen(), channel.isWritable());
        logger.info("远程地址: {}", channel.remoteAddress());
        logger.info("本地地址: {}", channel.localAddress());
        
        // 检查Pipeline
        var pipeline = channel.pipeline();
        logger.info("Pipeline处理器: {}", pipeline.names());
        
        // 检查EventLoop状态
        var eventLoop = channel.eventLoop();
        logger.info("EventLoop状态: shutdown={}, shuttingDown={}", 
            eventLoop.isShutdown(), eventLoop.isShuttingDown());
            
        logger.info("=== 连接诊断完成 ===");
    }
}