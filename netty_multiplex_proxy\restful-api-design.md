# RESTful API接口设计

## 1. API架构概述

### API设计原则
- **RESTful风格**: 遵循REST架构原则
- **版本控制**: 使用URL路径版本控制 `/api/v1/`
- **统一响应格式**: 标准化的响应结构
- **错误处理**: 统一的错误码和错误信息
- **认证授权**: JWT Token认证
- **限流控制**: API调用频率限制

### API基础结构
```java
// 统一响应格式
@Data
@Builder
public class ApiResponse<T> {
    private int code;           // 响应码
    private String message;     // 响应消息
    private T data;            // 响应数据
    private long timestamp;    // 时间戳
    private String requestId;  // 请求ID
    
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
            .code(200)
            .message("success")
            .data(data)
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
    }
    
    public static <T> ApiResponse<T> error(int code, String message) {
        return ApiResponse.<T>builder()
            .code(code)
            .message(message)
            .timestamp(System.currentTimeMillis())
            .requestId(RequestContextHolder.getRequestId())
            .build();
    }
}

// 分页响应格式
@Data
@Builder
public class PageResponse<T> {
    private List<T> items;      // 数据列表
    private long total;         // 总数量
    private int page;           // 当前页
    private int size;           // 页大小
    private int totalPages;     // 总页数
    private boolean hasNext;    // 是否有下一页
    private boolean hasPrev;    // 是否有上一页
}
```

## 2. 系统管理API

### 系统信息API
```java
@RestController
@RequestMapping("/api/v1/system")
@Api(tags = "系统管理")
public class SystemController {
    
    @GetMapping("/info")
    @ApiOperation("获取系统信息")
    public ApiResponse<SystemInfo> getSystemInfo() {
        SystemInfo info = SystemInfo.builder()
            .version(applicationProperties.getVersion())
            .buildTime(applicationProperties.getBuildTime())
            .startTime(applicationContext.getStartupDate())
            .uptime(System.currentTimeMillis() - applicationContext.getStartupDate().getTime())
            .javaVersion(System.getProperty("java.version"))
            .osName(System.getProperty("os.name"))
            .osVersion(System.getProperty("os.version"))
            .architecture(System.getProperty("os.arch"))
            .availableProcessors(Runtime.getRuntime().availableProcessors())
            .maxMemory(Runtime.getRuntime().maxMemory())
            .totalMemory(Runtime.getRuntime().totalMemory())
            .freeMemory(Runtime.getRuntime().freeMemory())
            .build();
        return ApiResponse.success(info);
    }
    
    @GetMapping("/health")
    @ApiOperation("健康检查")
    public ApiResponse<HealthStatus> getHealthStatus() {
        HealthStatus status = healthCheckService.checkHealth();
        return ApiResponse.success(status);
    }
    
    @PostMapping("/shutdown")
    @ApiOperation("优雅关闭系统")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> shutdown(@RequestParam(defaultValue = "30") int timeoutSeconds) {
        systemService.gracefulShutdown(timeoutSeconds);
        return ApiResponse.success(null);
    }
    
    @PostMapping("/restart")
    @ApiOperation("重启系统")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> restart() {
        systemService.restart();
        return ApiResponse.success(null);
    }
}
```

### 配置管理API
```java
@RestController
@RequestMapping("/api/v1/config")
@Api(tags = "配置管理")
public class ConfigController {
    
    @GetMapping
    @ApiOperation("获取所有配置")
    public ApiResponse<Map<String, Object>> getAllConfigs() {
        Map<String, Object> configs = configService.getAllConfigs();
        return ApiResponse.success(configs);
    }
    
    @GetMapping("/{category}")
    @ApiOperation("获取指定分类配置")
    public ApiResponse<Map<String, Object>> getConfigByCategory(
            @PathVariable String category) {
        Map<String, Object> configs = configService.getConfigByCategory(category);
        return ApiResponse.success(configs);
    }
    
    @PutMapping("/{category}/{key}")
    @ApiOperation("更新配置项")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> updateConfig(
            @PathVariable String category,
            @PathVariable String key,
            @RequestBody ConfigUpdateRequest request) {
        configService.updateConfig(category, key, request.getValue());
        return ApiResponse.success(null);
    }
    
    @PostMapping("/reload")
    @ApiOperation("重新加载配置")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> reloadConfig() {
        configService.reloadConfig();
        return ApiResponse.success(null);
    }
    
    @GetMapping("/validation")
    @ApiOperation("验证配置")
    public ApiResponse<ConfigValidationResult> validateConfig() {
        ConfigValidationResult result = configService.validateConfig();
        return ApiResponse.success(result);
    }
}
```

## 3. 连接管理API

### 连接信息API
```java
@RestController
@RequestMapping("/api/v1/connections")
@Api(tags = "连接管理")
public class ConnectionController {
    
    @GetMapping
    @ApiOperation("获取连接列表")
    public ApiResponse<PageResponse<ConnectionInfo>> getConnections(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String clientIp,
            @RequestParam(required = false) String protocol) {
        
        ConnectionQuery query = ConnectionQuery.builder()
            .page(page)
            .size(size)
            .status(status)
            .clientIp(clientIp)
            .protocol(protocol)
            .build();
            
        PageResponse<ConnectionInfo> connections = connectionService.getConnections(query);
        return ApiResponse.success(connections);
    }
    
    @GetMapping("/{connectionId}")
    @ApiOperation("获取连接详情")
    public ApiResponse<ConnectionDetail> getConnectionDetail(
            @PathVariable String connectionId) {
        ConnectionDetail detail = connectionService.getConnectionDetail(connectionId);
        return ApiResponse.success(detail);
    }
    
    @DeleteMapping("/{connectionId}")
    @ApiOperation("断开连接")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> disconnectConnection(
            @PathVariable String connectionId) {
        connectionService.disconnectConnection(connectionId);
        return ApiResponse.success(null);
    }
    
    @GetMapping("/{connectionId}/sessions")
    @ApiOperation("获取连接的会话列表")
    public ApiResponse<List<SessionInfo>> getConnectionSessions(
            @PathVariable String connectionId) {
        List<SessionInfo> sessions = connectionService.getConnectionSessions(connectionId);
        return ApiResponse.success(sessions);
    }
    
    @DeleteMapping("/{connectionId}/sessions/{sessionId}")
    @ApiOperation("关闭会话")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> closeSession(
            @PathVariable String connectionId,
            @PathVariable String sessionId) {
        connectionService.closeSession(connectionId, sessionId);
        return ApiResponse.success(null);
    }
}
```

### 连接池管理API
```java
@RestController
@RequestMapping("/api/v1/connection-pool")
@Api(tags = "连接池管理")
public class ConnectionPoolController {
    
    @GetMapping("/status")
    @ApiOperation("获取连接池状态")
    public ApiResponse<ConnectionPoolStatus> getConnectionPoolStatus() {
        ConnectionPoolStatus status = connectionPoolService.getStatus();
        return ApiResponse.success(status);
    }
    
    @GetMapping("/hosts")
    @ApiOperation("获取各主机连接池状态")
    public ApiResponse<List<HostPoolStatus>> getHostPoolStatus() {
        List<HostPoolStatus> hostStatus = connectionPoolService.getHostPoolStatus();
        return ApiResponse.success(hostStatus);
    }
    
    @PostMapping("/hosts/{host}/clear")
    @ApiOperation("清空指定主机连接池")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> clearHostPool(@PathVariable String host) {
        connectionPoolService.clearHostPool(host);
        return ApiResponse.success(null);
    }
    
    @PostMapping("/clear-all")
    @ApiOperation("清空所有连接池")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> clearAllPools() {
        connectionPoolService.clearAllPools();
        return ApiResponse.success(null);
    }
    
    @PutMapping("/config")
    @ApiOperation("更新连接池配置")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> updatePoolConfig(
            @RequestBody ConnectionPoolConfigRequest request) {
        connectionPoolService.updateConfig(request);
        return ApiResponse.success(null);
    }
}
```

## 4. 性能监控API

### 实时指标API
```java
@RestController
@RequestMapping("/api/v1/metrics")
@Api(tags = "性能监控")
public class MetricsController {
    
    @GetMapping("/current")
    @ApiOperation("获取当前指标")
    public ApiResponse<CurrentMetrics> getCurrentMetrics() {
        CurrentMetrics metrics = metricsService.getCurrentMetrics();
        return ApiResponse.success(metrics);
    }
    
    @GetMapping("/history")
    @ApiOperation("获取历史指标")
    public ApiResponse<List<MetricsData>> getHistoryMetrics(
            @RequestParam String startTime,
            @RequestParam String endTime,
            @RequestParam(defaultValue = "1m") String interval,
            @RequestParam(required = false) List<String> metrics) {
        
        MetricsQuery query = MetricsQuery.builder()
            .startTime(LocalDateTime.parse(startTime))
            .endTime(LocalDateTime.parse(endTime))
            .interval(interval)
            .metrics(metrics)
            .build();
            
        List<MetricsData> history = metricsService.getHistoryMetrics(query);
        return ApiResponse.success(history);
    }
    
    @GetMapping("/summary")
    @ApiOperation("获取指标摘要")
    public ApiResponse<MetricsSummary> getMetricsSummary(
            @RequestParam(defaultValue = "1h") String timeRange) {
        MetricsSummary summary = metricsService.getMetricsSummary(timeRange);
        return ApiResponse.success(summary);
    }
    
    @GetMapping("/top-hosts")
    @ApiOperation("获取热门主机统计")
    public ApiResponse<List<HostMetrics>> getTopHosts(
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(defaultValue = "1h") String timeRange) {
        List<HostMetrics> topHosts = metricsService.getTopHosts(limit, timeRange);
        return ApiResponse.success(topHosts);
    }
    
    @PostMapping("/export")
    @ApiOperation("导出指标数据")
    public ResponseEntity<Resource> exportMetrics(
            @RequestBody MetricsExportRequest request) {
        Resource resource = metricsService.exportMetrics(request);
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, 
                "attachment; filename=metrics-" + System.currentTimeMillis() + ".csv")
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .body(resource);
    }
}
```

### 告警管理API
```java
@RestController
@RequestMapping("/api/v1/alerts")
@Api(tags = "告警管理")
public class AlertController {
    
    @GetMapping
    @ApiOperation("获取告警列表")
    public ApiResponse<PageResponse<Alert>> getAlerts(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String severity,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        
        AlertQuery query = AlertQuery.builder()
            .page(page)
            .size(size)
            .severity(severity)
            .status(status)
            .startTime(startTime != null ? LocalDateTime.parse(startTime) : null)
            .endTime(endTime != null ? LocalDateTime.parse(endTime) : null)
            .build();
            
        PageResponse<Alert> alerts = alertService.getAlerts(query);
        return ApiResponse.success(alerts);
    }
    
    @GetMapping("/{alertId}")
    @ApiOperation("获取告警详情")
    public ApiResponse<AlertDetail> getAlertDetail(@PathVariable String alertId) {
        AlertDetail detail = alertService.getAlertDetail(alertId);
        return ApiResponse.success(detail);
    }
    
    @PostMapping("/{alertId}/acknowledge")
    @ApiOperation("确认告警")
    public ApiResponse<Void> acknowledgeAlert(
            @PathVariable String alertId,
            @RequestBody AlertAcknowledgeRequest request) {
        alertService.acknowledgeAlert(alertId, request.getComment());
        return ApiResponse.success(null);
    }
    
    @PostMapping("/{alertId}/resolve")
    @ApiOperation("解决告警")
    public ApiResponse<Void> resolveAlert(
            @PathVariable String alertId,
            @RequestBody AlertResolveRequest request) {
        alertService.resolveAlert(alertId, request.getComment());
        return ApiResponse.success(null);
    }
    
    @GetMapping("/rules")
    @ApiOperation("获取告警规则")
    public ApiResponse<List<AlertRule>> getAlertRules() {
        List<AlertRule> rules = alertService.getAlertRules();
        return ApiResponse.success(rules);
    }
    
    @PostMapping("/rules")
    @ApiOperation("创建告警规则")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<AlertRule> createAlertRule(@RequestBody AlertRuleRequest request) {
        AlertRule rule = alertService.createAlertRule(request);
        return ApiResponse.success(rule);
    }
    
    @PutMapping("/rules/{ruleId}")
    @ApiOperation("更新告警规则")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<AlertRule> updateAlertRule(
            @PathVariable String ruleId,
            @RequestBody AlertRuleRequest request) {
        AlertRule rule = alertService.updateAlertRule(ruleId, request);
        return ApiResponse.success(rule);
    }
    
    @DeleteMapping("/rules/{ruleId}")
    @ApiOperation("删除告警规则")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> deleteAlertRule(@PathVariable String ruleId) {
        alertService.deleteAlertRule(ruleId);
        return ApiResponse.success(null);
    }
}
```

## 5. 安全管理API

### 用户认证API
```java
@RestController
@RequestMapping("/api/v1/auth")
@Api(tags = "用户认证")
public class AuthController {
    
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public ApiResponse<LoginResponse> login(@RequestBody LoginRequest request) {
        LoginResponse response = authService.login(request.getUsername(), request.getPassword());
        return ApiResponse.success(response);
    }
    
    @PostMapping("/logout")
    @ApiOperation("用户登出")
    public ApiResponse<Void> logout(HttpServletRequest request) {
        String token = extractToken(request);
        authService.logout(token);
        return ApiResponse.success(null);
    }
    
    @PostMapping("/refresh")
    @ApiOperation("刷新Token")
    public ApiResponse<TokenResponse> refreshToken(@RequestBody RefreshTokenRequest request) {
        TokenResponse response = authService.refreshToken(request.getRefreshToken());
        return ApiResponse.success(response);
    }
    
    @GetMapping("/profile")
    @ApiOperation("获取用户信息")
    public ApiResponse<UserProfile> getUserProfile(Authentication authentication) {
        UserProfile profile = authService.getUserProfile(authentication.getName());
        return ApiResponse.success(profile);
    }
    
    @PutMapping("/password")
    @ApiOperation("修改密码")
    public ApiResponse<Void> changePassword(
            @RequestBody ChangePasswordRequest request,
            Authentication authentication) {
        authService.changePassword(authentication.getName(), 
            request.getOldPassword(), request.getNewPassword());
        return ApiResponse.success(null);
    }
}
```

### 访问控制API
```java
@RestController
@RequestMapping("/api/v1/access-control")
@Api(tags = "访问控制")
public class AccessControlController {
    
    @GetMapping("/whitelist")
    @ApiOperation("获取IP白名单")
    public ApiResponse<List<IpWhitelistEntry>> getIpWhitelist() {
        List<IpWhitelistEntry> whitelist = accessControlService.getIpWhitelist();
        return ApiResponse.success(whitelist);
    }
    
    @PostMapping("/whitelist")
    @ApiOperation("添加IP白名单")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<IpWhitelistEntry> addIpWhitelist(@RequestBody IpWhitelistRequest request) {
        IpWhitelistEntry entry = accessControlService.addIpWhitelist(request);
        return ApiResponse.success(entry);
    }
    
    @DeleteMapping("/whitelist/{id}")
    @ApiOperation("删除IP白名单")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> removeIpWhitelist(@PathVariable String id) {
        accessControlService.removeIpWhitelist(id);
        return ApiResponse.success(null);
    }
    
    @GetMapping("/blacklist")
    @ApiOperation("获取域名黑名单")
    public ApiResponse<List<DomainBlacklistEntry>> getDomainBlacklist() {
        List<DomainBlacklistEntry> blacklist = accessControlService.getDomainBlacklist();
        return ApiResponse.success(blacklist);
    }
    
    @PostMapping("/blacklist")
    @ApiOperation("添加域名黑名单")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<DomainBlacklistEntry> addDomainBlacklist(
            @RequestBody DomainBlacklistRequest request) {
        DomainBlacklistEntry entry = accessControlService.addDomainBlacklist(request);
        return ApiResponse.success(entry);
    }
    
    @GetMapping("/filter-stats")
    @ApiOperation("获取过滤统计")
    public ApiResponse<FilterStatistics> getFilterStatistics(
            @RequestParam(defaultValue = "1h") String timeRange) {
        FilterStatistics stats = accessControlService.getFilterStatistics(timeRange);
        return ApiResponse.success(stats);
    }
}
```

## 6. 日志管理API

### 日志查询API
```java
@RestController
@RequestMapping("/api/v1/logs")
@Api(tags = "日志管理")
public class LogController {
    
    @GetMapping
    @ApiOperation("查询日志")
    public ApiResponse<PageResponse<LogEntry>> getLogs(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "50") int size,
            @RequestParam(required = false) String level,
            @RequestParam(required = false) String logger,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        
        LogQuery query = LogQuery.builder()
            .page(page)
            .size(size)
            .level(level)
            .logger(logger)
            .keyword(keyword)
            .startTime(startTime != null ? LocalDateTime.parse(startTime) : null)
            .endTime(endTime != null ? LocalDateTime.parse(endTime) : null)
            .build();
            
        PageResponse<LogEntry> logs = logService.getLogs(query);
        return ApiResponse.success(logs);
    }
    
    @GetMapping("/levels")
    @ApiOperation("获取日志级别统计")
    public ApiResponse<Map<String, Long>> getLogLevelStats(
            @RequestParam(defaultValue = "1h") String timeRange) {
        Map<String, Long> stats = logService.getLogLevelStats(timeRange);
        return ApiResponse.success(stats);
    }
    
    @GetMapping("/loggers")
    @ApiOperation("获取Logger列表")
    public ApiResponse<List<LoggerInfo>> getLoggers() {
        List<LoggerInfo> loggers = logService.getLoggers();
        return ApiResponse.success(loggers);
    }
    
    @PutMapping("/loggers/{loggerName}/level")
    @ApiOperation("设置Logger级别")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> setLoggerLevel(
            @PathVariable String loggerName,
            @RequestBody LoggerLevelRequest request) {
        logService.setLoggerLevel(loggerName, request.getLevel());
        return ApiResponse.success(null);
    }
    
    @PostMapping("/export")
    @ApiOperation("导出日志")
    public ResponseEntity<Resource> exportLogs(@RequestBody LogExportRequest request) {
        Resource resource = logService.exportLogs(request);
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, 
                "attachment; filename=logs-" + System.currentTimeMillis() + ".txt")
            .contentType(MediaType.TEXT_PLAIN)
            .body(resource);
    }
}
```

## 7. API安全和限流

### JWT认证配置
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint() {
        return new JwtAuthenticationEntryPoint();
    }
    
    @Bean
    public JwtRequestFilter jwtRequestFilter() {
        return new JwtRequestFilter();
    }
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/api/v1/system/health").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/v1/metrics/current").hasRole("USER")
                .requestMatchers("/api/v1/config/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/api/v1/connections/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint())
            .and()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
            
        http.addFilterBefore(jwtRequestFilter(), UsernamePasswordAuthenticationFilter.class);
        
        return http.build();
    }
}
```

### API限流配置
```java
@Component
public class RateLimitingFilter implements Filter {
    
    private final RedisTemplate<String, String> redisTemplate;
    private final Map<String, RateLimiter> rateLimiters = new ConcurrentHashMap<>();
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String clientIp = getClientIp(httpRequest);
        String endpoint = httpRequest.getRequestURI();
        
        // 获取限流配置
        RateLimitConfig config = getRateLimitConfig(endpoint);
        if (config != null) {
            String key = "rate_limit:" + clientIp + ":" + endpoint;
            
            if (!checkRateLimit(key, config)) {
                httpResponse.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
                httpResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
                httpResponse.getWriter().write(
                    "{\"code\":429,\"message\":\"Too Many Requests\"}");
                return;
            }
        }
        
        chain.doFilter(request, response);
    }
    
    private boolean checkRateLimit(String key, RateLimitConfig config) {
        // 使用Redis滑动窗口算法实现限流
        long currentTime = System.currentTimeMillis();
        long windowStart = currentTime - config.getWindowSize();
        
        // 清理过期记录
        redisTemplate.opsForZSet().removeRangeByScore(key, 0, windowStart);
        
        // 获取当前窗口内的请求数
        Long currentCount = redisTemplate.opsForZSet().count(key, windowStart, currentTime);
        
        if (currentCount >= config.getMaxRequests()) {
            return false;
        }
        
        // 记录当前请求
        redisTemplate.opsForZSet().add(key, UUID.randomUUID().toString(), currentTime);
        redisTemplate.expire(key, Duration.ofMillis(config.getWindowSize()));
        
        return true;
    }
}
```