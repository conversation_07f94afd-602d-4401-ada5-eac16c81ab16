<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/netty_multiplex_proxy_alicloud/pom.xml" />
        <option value="$PROJECT_DIR$/netty_multiplex_proxy/proxy-server/pom.xml" />
        <option value="$PROJECT_DIR$/netty_multiplex_proxy/proxy-client/pom.xml" />
        <option value="$PROJECT_DIR$/netty_multiplex_proxy_alicloud/proxy-client-nacos/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/netty_multiplex_proxy_alicloud/proxy-client-nacos/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_21" default="true" project-jdk-name="graalvm-21" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>