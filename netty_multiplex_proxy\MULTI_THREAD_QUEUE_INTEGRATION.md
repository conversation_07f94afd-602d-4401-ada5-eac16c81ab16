# 多线程队列集成指南

## 概述

本文档介绍了多线程数据包队列（PacketQueue）的集成和使用方法。该队列系统支持随机分发策略，能够有效提高并发处理能力。

## 主要特性

### 1. 随机分发策略
- **轮询分发（ROUND_ROBIN）**: 按顺序轮流分发到各个队列，确保负载均衡
- **真随机分发（TRUE_RANDOM）**: 完全随机选择队列，适合低延迟场景

### 2. 多线程处理
- 支持多个工作线程并行处理数据包
- 每个工作线程有独立的队列和处理逻辑
- 默认使用CPU核心数作为工作线程数

### 3. 配置灵活性
- 支持通过配置文件或代码配置
- 提供预定义的性能配置模板
- 支持运行时统计监控

## 配置说明

### YAML配置文件

```yaml
queue:
  workerThreads: 8          # 工作线程数，默认为CPU核心数
  capacity: 10000           # 每个队列容量
  batchSize: 100            # 批处理大小
  flushIntervalMs: 10       # 刷新间隔（毫秒）
  retry:
    maxAttempts: 3          # 最大重试次数
    delayMs: 1000           # 重试延迟（毫秒）
  monitoring:
    enabled: true           # 是否启用监控
    reportIntervalSeconds: 30  # 报告间隔（秒）
    warningThreshold: 80    # 警告阈值（百分比）
    errorThreshold: 95      # 错误阈值（百分比）
```

### 代码配置

```java
// 使用默认配置
PacketQueue packetQueue = new PacketQueue();

// 使用自定义工作线程数
PacketQueue packetQueue = new PacketQueue(8);

// 使用完整配置
PacketQueueConfig config = new PacketQueueConfig()
    .setWorkerThreadCount(8)
    .setQueueCapacity(20000)
    .setBatchSize(200)
    .setFlushIntervalMs(5)
    .setRetryAttempts(5)
    .setRetryDelayMs(500)
    .setDistributionStrategy(PacketQueueConfig.DistributionStrategy.ROUND_ROBIN);

PacketQueue packetQueue = new PacketQueue(config);
```

### 预定义配置模板

```java
// 高性能配置
PacketQueue highPerfQueue = new PacketQueue(PacketQueueConfig.highPerformance());

// 低延迟配置
PacketQueue lowLatencyQueue = new PacketQueue(PacketQueueConfig.lowLatency());

// 内存优化配置
PacketQueue memOptQueue = new PacketQueue(PacketQueueConfig.memoryOptimized());
```

## 使用方法

### 1. 基本使用

```java
// 创建队列
PacketQueue packetQueue = new PacketQueue(4);

// 设置数据包发送器
packetQueue.setPacketSender(new MyPacketSender());

// 启动队列处理
packetQueue.start();

// 发送数据包
MultiplexProtocol.Packet packet = createPacket();
boolean success = packetQueue.enqueue(packet);

// 停止队列
packetQueue.stop();
```

### 2. 在QueuedConnectionManager中的集成

```java
public class QueuedConnectionManager implements IConnectionManager, PacketQueue.PacketSender {
    private final PacketQueue packetQueue;
    
    public QueuedConnectionManager(ConnectionManager connectionManager) {
        // 从配置文件加载队列配置
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        
        PacketQueueConfig queueConfig = new PacketQueueConfig()
            .setWorkerThreadCount(configManager.getQueueWorkerThreads())
            .setQueueCapacity(configManager.getQueueCapacity())
            .setBatchSize(configManager.getQueueBatchSize())
            .setFlushIntervalMs(configManager.getQueueFlushIntervalMs())
            .setRetryAttempts(configManager.getQueueRetryMaxAttempts())
            .setRetryDelayMs(configManager.getQueueRetryDelayMs())
            .setDistributionStrategy(PacketQueueConfig.DistributionStrategy.ROUND_ROBIN);
        
        this.packetQueue = new PacketQueue(queueConfig);
        this.packetQueue.setPacketSender(this);
    }
    
    @Override
    public boolean sendPacket(MultiplexProtocol.Packet packet) {
        // 实际发送逻辑
        return connectionManager.sendPacket(packet);
    }
}
```

## 分发策略详解

### 轮询分发（ROUND_ROBIN）
- **优点**: 确保负载均衡，各个队列处理的数据包数量基本相等
- **适用场景**: 大多数场景，特别是需要保证负载均衡的情况
- **实现**: 使用原子计数器按顺序分发

### 真随机分发（TRUE_RANDOM）
- **优点**: 完全随机，可能在某些场景下有更好的性能
- **缺点**: 可能导致负载不均衡
- **适用场景**: 低延迟要求的场景
- **实现**: 使用Random类生成随机数

## 监控和统计

### 整体统计信息

```java
PacketQueue.QueueStats stats = packetQueue.getStats();
System.out.println("队列统计: " + stats);

// 输出示例：
// QueueStats{workers=4, queueSize=0, enqueued=1000, processed=1000, dropped=0, running=true}
```

### 单个工作队列统计

```java
for (int i = 0; i < workerThreadCount; i++) {
    PacketQueue.WorkerQueue.QueueStats workerStats = packetQueue.getWorkerStats(i);
    System.out.println("工作队列" + i + "统计: " + workerStats);
}

// 输出示例：
// WorkerStats{id=0, queueSize=0, processed=250, dropped=0, running=true}
// WorkerStats{id=1, queueSize=0, processed=250, dropped=0, running=true}
// WorkerStats{id=2, queueSize=0, processed=250, dropped=0, running=true}
// WorkerStats{id=3, queueSize=0, processed=250, dropped=0, running=true}
```

## 性能调优

### 高吞吐量场景
```java
PacketQueueConfig config = new PacketQueueConfig()
    .setWorkerThreadCount(Runtime.getRuntime().availableProcessors() * 2)
    .setQueueCapacity(50000)
    .setBatchSize(200)
    .setFlushIntervalMs(5)
    .setDistributionStrategy(PacketQueueConfig.DistributionStrategy.ROUND_ROBIN);
```

### 低延迟场景
```java
PacketQueueConfig config = new PacketQueueConfig()
    .setWorkerThreadCount(Runtime.getRuntime().availableProcessors())
    .setQueueCapacity(5000)
    .setBatchSize(50)
    .setFlushIntervalMs(1)
    .setDistributionStrategy(PacketQueueConfig.DistributionStrategy.TRUE_RANDOM);
```

### 内存受限场景
```java
PacketQueueConfig config = new PacketQueueConfig()
    .setWorkerThreadCount(2)
    .setQueueCapacity(1000)
    .setBatchSize(20)
    .setFlushIntervalMs(50)
    .setDistributionStrategy(PacketQueueConfig.DistributionStrategy.ROUND_ROBIN);
```

## 注意事项

1. **线程安全**: PacketQueue是线程安全的，可以从多个线程同时调用enqueue方法
2. **资源管理**: 使用完毕后必须调用stop()方法释放资源
3. **发送器设置**: 必须在start()之前或之后设置PacketSender
4. **重试机制**: 发送失败的数据包会自动重试，超过重试次数后会被丢弃
5. **监控配置**: 建议在生产环境中启用队列监控，及时发现性能问题

## 测试验证

项目包含完整的单元测试，可以验证多线程队列的功能：

```bash
# 运行队列测试
mvn test -Dtest=PacketQueueTest
```

测试覆盖：
- 轮询分发策略测试
- 真随机分发策略测试
- 并发入队测试
- 工作队列统计测试

## 总结

多线程数据包队列提供了高效的并发处理能力，通过随机分发策略实现负载均衡。合理配置队列参数可以在不同场景下获得最佳性能。建议根据实际需求选择合适的分发策略和配置参数。