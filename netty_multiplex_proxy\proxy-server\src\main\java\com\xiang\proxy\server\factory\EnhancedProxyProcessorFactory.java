package com.xiang.proxy.server.factory;

import com.xiang.proxy.server.config.ProxyProcessorConfig;
import com.xiang.proxy.server.core.BatchProxyProcessor;
import com.xiang.proxy.server.core.EnhancedProxyProcessor;
import com.xiang.proxy.server.core.ProxyProcessor;
import com.xiang.proxy.server.router.Router;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 增强代理处理器工厂
 * 提供便捷的方法创建不同类型的代理处理器
 */
public class EnhancedProxyProcessorFactory {
    private static final Logger logger = LoggerFactory.getLogger(EnhancedProxyProcessorFactory.class);

    /**
     * 处理器类型枚举
     */
    public enum ProcessorType {
        /**
         * 标准处理器 - 基础的队列处理
         */
        STANDARD,
        
        /**
         * 批处理器 - 支持批量处理提高吞吐量
         */
        BATCH,
        
        /**
         * 增强处理器 - 支持连接复用和消息队列缓存
         */
        ENHANCED
    }

    /**
     * 创建默认的增强代理处理器
     */
    public static EnhancedProxyProcessor createEnhanced(Router router) {
        ProxyProcessorConfig config = createDefaultConfig();
        return new EnhancedProxyProcessor(router, config);
    }

    /**
     * 创建增强代理处理器
     */
    public static EnhancedProxyProcessor createEnhanced(Router router, ProxyProcessorConfig config) {
        return new EnhancedProxyProcessor(router, config);
    }

    /**
     * 创建批处理代理处理器
     */
    public static BatchProxyProcessor createBatch(Router router) {
        ProxyProcessorConfig config = createBatchConfig();
        return new BatchProxyProcessor(router, config);
    }

    /**
     * 创建批处理代理处理器
     */
    public static BatchProxyProcessor createBatch(Router router, ProxyProcessorConfig config) {
        return new BatchProxyProcessor(router, config);
    }

    /**
     * 创建标准代理处理器
     */
    public static ProxyProcessor createStandard(Router router) {
        ProxyProcessorConfig config = createDefaultConfig();
        return new ProxyProcessor(router, config);
    }

    /**
     * 创建标准代理处理器
     */
    public static ProxyProcessor createStandard(Router router, ProxyProcessorConfig config) {
        return new ProxyProcessor(router, config);
    }

    /**
     * 根据类型创建处理器
     */
    public static ProxyProcessor create(ProcessorType type, Router router) {
        return create(type, router, null);
    }

    /**
     * 根据类型创建处理器
     */
    public static ProxyProcessor create(ProcessorType type, Router router, ProxyProcessorConfig config) {
        if (config == null) {
            config = createConfigForType(type);
        }

        switch (type) {
            case ENHANCED:
                logger.info("创建增强代理处理器");
                return new EnhancedProxyProcessor(router, config);
            case BATCH:
                logger.info("创建批处理代理处理器");
                return new BatchProxyProcessor(router, config);
            case STANDARD:
            default:
                logger.info("创建标准代理处理器");
                return new ProxyProcessor(router, config);
        }
    }

    /**
     * 创建默认配置
     */
    public static ProxyProcessorConfig createDefaultConfig() {
        ProxyProcessorConfig config = new ProxyProcessorConfig();
        
        // 基础配置
        config.setQueueCount(Runtime.getRuntime().availableProcessors() * 2);
        config.setQueueCapacity(1000);
        config.setWorkerThreadPrefix("proxy-worker-");
        config.setShutdownTimeoutSeconds(30);
        
        // 启用自适应调整
        config.setEnableAdaptiveAdjustment(true);
        
        logger.debug("创建默认配置: {}", config);
        return config;
    }

    /**
     * 创建批处理配置
     */
    public static ProxyProcessorConfig createBatchConfig() {
        ProxyProcessorConfig config = createDefaultConfig();
        
        // 批处理特定配置
        config.setBatchSize(10);
        config.setBatchTimeoutMs(100);
        
        logger.debug("创建批处理配置: {}", config);
        return config;
    }

    /**
     * 创建高性能配置
     */
    public static ProxyProcessorConfig createHighPerformanceConfig() {
        ProxyProcessorConfig config = new ProxyProcessorConfig();
        
        // 高性能配置
        int cpuCount = Runtime.getRuntime().availableProcessors();
        config.setQueueCount(Math.max(4, cpuCount * 2));
        config.setQueueCapacity(2000);
        config.setWorkerThreadPrefix("hp-proxy-worker-");
        config.setShutdownTimeoutSeconds(60);
        
        // 批处理配置
        config.setBatchSize(20);
        config.setBatchTimeoutMs(50);
        
        // 启用自适应调整
        config.setEnableAdaptiveAdjustment(true);
        
        logger.debug("创建高性能配置: {}", config);
        return config;
    }

    /**
     * 创建低延迟配置
     */
    public static ProxyProcessorConfig createLowLatencyConfig() {
        ProxyProcessorConfig config = new ProxyProcessorConfig();
        
        // 低延迟配置
        int cpuCount = Runtime.getRuntime().availableProcessors();
        config.setQueueCount(Math.max(2, cpuCount));
        config.setQueueCapacity(500);
        config.setWorkerThreadPrefix("ll-proxy-worker-");
        config.setShutdownTimeoutSeconds(10);
        
        // 小批处理配置
        config.setBatchSize(5);
        config.setBatchTimeoutMs(10);
        
        // 启用自适应调整
        config.setEnableAdaptiveAdjustment(true);
        
        logger.debug("创建低延迟配置: {}", config);
        return config;
    }

    /**
     * 创建内存优化配置
     */
    public static ProxyProcessorConfig createMemoryOptimizedConfig() {
        ProxyProcessorConfig config = new ProxyProcessorConfig();
        
        // 内存优化配置
        config.setQueueCount(2);
        config.setQueueCapacity(200);
        config.setWorkerThreadPrefix("mo-proxy-worker-");
        config.setShutdownTimeoutSeconds(15);
        
        // 小批处理配置
        config.setBatchSize(3);
        config.setBatchTimeoutMs(200);
        
        // 启用自适应调整
        config.setEnableAdaptiveAdjustment(true);
        
        logger.debug("创建内存优化配置: {}", config);
        return config;
    }

    /**
     * 根据处理器类型创建对应的配置
     */
    private static ProxyProcessorConfig createConfigForType(ProcessorType type) {
        switch (type) {
            case BATCH:
                return createBatchConfig();
            case ENHANCED:
                return createDefaultConfig();
            case STANDARD:
            default:
                return createDefaultConfig();
        }
    }

    /**
     * 创建推荐配置 - 根据系统资源自动选择
     */
    public static ProxyProcessorConfig createRecommendedConfig() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        int cpuCount = runtime.availableProcessors();
        
        // 根据可用内存和CPU数量选择配置
        if (maxMemory > 2L * 1024 * 1024 * 1024 && cpuCount >= 4) { // 2GB+ 内存，4+ CPU
            logger.info("检测到高配置系统，使用高性能配置");
            return createHighPerformanceConfig();
        } else if (maxMemory > 1L * 1024 * 1024 * 1024 && cpuCount >= 2) { // 1GB+ 内存，2+ CPU
            logger.info("检测到中等配置系统，使用默认配置");
            return createDefaultConfig();
        } else { // 低配置系统
            logger.info("检测到低配置系统，使用内存优化配置");
            return createMemoryOptimizedConfig();
        }
    }

    /**
     * 创建推荐的处理器 - 根据系统资源自动选择类型和配置
     */
    public static ProxyProcessor createRecommended(Router router) {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        int cpuCount = runtime.availableProcessors();
        
        ProcessorType type;
        ProxyProcessorConfig config;
        
        if (maxMemory > 2L * 1024 * 1024 * 1024 && cpuCount >= 4) {
            // 高配置系统：使用增强处理器 + 高性能配置
            type = ProcessorType.ENHANCED;
            config = createHighPerformanceConfig();
            logger.info("推荐使用增强处理器（高性能配置）");
        } else if (maxMemory > 1L * 1024 * 1024 * 1024 && cpuCount >= 2) {
            // 中等配置系统：使用批处理器 + 默认配置
            type = ProcessorType.BATCH;
            config = createBatchConfig();
            logger.info("推荐使用批处理器（默认配置）");
        } else {
            // 低配置系统：使用标准处理器 + 内存优化配置
            type = ProcessorType.STANDARD;
            config = createMemoryOptimizedConfig();
            logger.info("推荐使用标准处理器（内存优化配置）");
        }
        
        return create(type, router, config);
    }

    /**
     * 打印系统信息和推荐配置
     */
    public static void printSystemInfo() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        int cpuCount = runtime.availableProcessors();
        
        logger.info("=== 系统信息 ===");
        logger.info("CPU核心数: {}", cpuCount);
        logger.info("最大内存: {} MB", maxMemory / 1024 / 1024);
        logger.info("总内存: {} MB", totalMemory / 1024 / 1024);
        logger.info("空闲内存: {} MB", freeMemory / 1024 / 1024);
        logger.info("已用内存: {} MB", (totalMemory - freeMemory) / 1024 / 1024);
        
        // 推荐配置
        if (maxMemory > 2L * 1024 * 1024 * 1024 && cpuCount >= 4) {
            logger.info("推荐: EnhancedProxyProcessor + 高性能配置");
        } else if (maxMemory > 1L * 1024 * 1024 * 1024 && cpuCount >= 2) {
            logger.info("推荐: BatchProxyProcessor + 默认配置");
        } else {
            logger.info("推荐: ProxyProcessor + 内存优化配置");
        }
        logger.info("===============");
    }
}
