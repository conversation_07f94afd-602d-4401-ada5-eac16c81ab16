# Connection Reset Troubleshooting Guide

## 🔍 Problem Overview

"Connection reset by peer" errors occur when the remote server forcibly closes a TCP connection. This is a common network issue that can have various causes and solutions.

## 📊 Error Analysis

### Current Error Pattern
```
15:13:42.508 [main-multiplex-worker] ERROR c.p.s.o.i.TcpDirectOutboundHandler - 发送数据失败: 030a61d3-a7d6-4295-9aa5-a01ebbea64fb, bytes=16413
java.io.IOException: Connection reset by peer
```

### Error Classification

| Error Type | Cause | Severity | Action |
|------------|-------|----------|--------|
| Connection reset by peer | Remote server closed connection | Medium | Retry with backoff |
| Connection timeout | Network latency/congestion | Medium | Retry immediately |
| Connection refused | Service unavailable | High | Backoff and retry |
| Unknown host | DNS resolution failure | High | Check configuration |

## 🛠️ Implemented Solutions

### 1. Enhanced Error Handling

**Before:**
- All connection errors logged as ERROR level
- Duplicate error logs for same connection
- No error classification

**After:**
- Connection reset errors downgraded to DEBUG level
- Error classification and appropriate logging
- Duplicate error suppression

### 2. Connection Pool Improvements

**Features:**
- Immediate removal of failed connections
- Connection health validation
- Proactive connection replacement

**Code Example:**
```java
// Remove failed connection from pool
if (ConnectionPoolConfig.ENABLE_CONNECTION_POOL) {
    String poolKey = connection.getAttribute(OutboundConnection.Attributes.CONNECTION_POOL_KEY);
    if (poolKey != null) {
        ConnectionPool.getInstance().removeFailedConnection(poolKey, connection.getBackendChannel());
    }
}
```

### 3. Intelligent Error Classification

**Connection Reset Detection:**
```java
private boolean isConnectionResetError(Throwable cause) {
    if (cause instanceof java.io.IOException) {
        String message = cause.getMessage();
        return message != null && (
            message.contains("Connection reset by peer") ||
            message.contains("Connection reset") ||
            message.contains("远程主机强迫关闭了一个现有的连接")
        );
    }
    return false;
}
```

## 📈 Monitoring and Metrics

### Key Metrics to Track

1. **Connection Reset Rate**
   - Total resets per minute
   - Reset rate by target host
   - Reset patterns over time

2. **Connection Pool Health**
   - Pool hit rate
   - Failed connection removal rate
   - Average connection lifetime

3. **Error Recovery**
   - Retry success rate
   - Time to recovery
   - Error suppression effectiveness

### Monitoring Commands

```bash
# Check connection reset patterns
grep "连接被远程主机重置" logs/proxy-server.log | tail -20

# Monitor connection pool health
grep "连接池状态" logs/proxy-server.log | tail -10

# Check error classification
grep "发送数据失败" logs/proxy-server.log | grep -v "Connection reset" | tail -10
```

## 🔧 Configuration Options

### Error Handling Configuration

```properties
# connection-error-handling.properties
connection.error.reset.log-level=DEBUG
connection.error.timeout.log-level=DEBUG
connection.error.refused.log-level=WARN
connection.error.unknown.log-level=ERROR

connection.retry.enabled=true
connection.retry.max-attempts=3
connection.retry.initial-delay-ms=100

connection.error.suppression.enabled=true
connection.error.suppression.window-ms=10000
```

### Connection Pool Configuration

```yaml
pool:
  enable: true
  max-connections:
    per-host: 20
  idle-timeout:
    seconds: 60
  cleanup-interval:
    seconds: 30
  remove-failed-immediately: true
  health-check-on-error: true
```

## 🚨 Common Scenarios and Solutions

### Scenario 1: High Connection Reset Rate

**Symptoms:**
- Many "Connection reset by peer" errors
- Degraded performance
- Client timeouts

**Possible Causes:**
- Target server overloaded
- Network infrastructure issues
- Connection pool configuration

**Solutions:**
1. Increase connection pool size
2. Implement connection retry logic
3. Add circuit breaker pattern
4. Monitor target server health

### Scenario 2: Intermittent Connection Issues

**Symptoms:**
- Sporadic connection resets
- Some requests succeed, others fail
- No clear pattern

**Possible Causes:**
- Load balancer configuration
- Network path instability
- Target server connection limits

**Solutions:**
1. Enable connection pooling
2. Implement health checks
3. Add connection validation
4. Monitor network path

### Scenario 3: Connection Pool Pollution

**Symptoms:**
- Low connection pool hit rate
- Many failed connections in pool
- Slow connection establishment

**Possible Causes:**
- Failed connections not removed
- Inadequate connection validation
- Long connection lifetime

**Solutions:**
1. Enable immediate failed connection removal
2. Implement connection health checks
3. Reduce connection lifetime
4. Add proactive connection replacement

## 📋 Troubleshooting Checklist

### Immediate Actions
- [ ] Check if connection reset errors are now DEBUG level
- [ ] Verify connection pool is removing failed connections
- [ ] Monitor error suppression effectiveness
- [ ] Check connection pool hit rate

### Investigation Steps
- [ ] Analyze error patterns by time and target host
- [ ] Check target server logs for connection limits
- [ ] Monitor network latency and packet loss
- [ ] Verify load balancer configuration

### Long-term Monitoring
- [ ] Set up alerts for high connection reset rates
- [ ] Monitor connection pool health metrics
- [ ] Track error recovery success rates
- [ ] Analyze connection lifetime patterns

## 🎯 Expected Improvements

### Immediate Benefits
1. **Reduced Log Noise**: Connection reset errors now at DEBUG level
2. **Better Pool Health**: Failed connections removed immediately
3. **Improved Diagnostics**: Error classification and context

### Long-term Benefits
1. **Better Reliability**: Proactive connection management
2. **Enhanced Monitoring**: Detailed error metrics
3. **Faster Recovery**: Intelligent retry logic

## 📞 Support Information

If connection reset issues persist after implementing these improvements:

1. **Collect Diagnostic Data:**
   - Connection pool statistics
   - Error rate trends
   - Network latency measurements
   - Target server health status

2. **Check Configuration:**
   - Connection pool settings
   - Timeout configurations
   - Retry parameters
   - Error handling settings

3. **Monitor Key Metrics:**
   - Connection reset rate per host
   - Pool hit/miss ratio
   - Average connection lifetime
   - Error recovery success rate

The implemented improvements should significantly reduce the impact of connection reset errors and provide better visibility into connection health patterns.