version: "3.9"
services:
  mysql_3307:
    networks:
      - inner_network
    restart: always
    ports:
      - 3307:3306
    environment:
      MYSQL_ROOT_PASSWORD: hfxiang
    volumes:
      - /root/traffic/mysql_datadir:/var/lib/mysql
      - /root/traffic/mysql_custom_config:/etc/mysql/conf.d
    build:
      context: ./mysqlWk
      dockerfile: Dockerfile
      tags:
        - mysql_init:latest
  traffic:
    build:
      context: ./workspace
      dockerfile: Dockerfile
      tags:
        - traffic:latest
    networks:
      - inner_network
    restart: always
    volumes:
      - /root/traffic/config/server.json:/opt/traffic-server/config/server.json
      - /root/traffic/config/user.json:/opt/traffic-server/config/user.json
    depends_on:
      - mysql_3307
    ports:
      - 1090:1090
      - 7060:7060
networks:
  inner_network:
    driver: bridge
