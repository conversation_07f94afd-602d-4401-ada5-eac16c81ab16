package com.xiang.proxy.server.service.register;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.api.naming.NamingFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.xiang.proxy.server.config.ProxyServerV2ConfigManager;
import com.xiang.proxy.server.config.properties.ProxyServerV2Properties;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.util.List;
import java.util.Properties;


// 通过额外注册一个实例，指定端口，用于注册netty服务
@Component
@RequiredArgsConstructor
public class NettyServiceRegistrar {

    private final NacosDiscoveryProperties nacosDiscoveryProperties;

    @PostConstruct
    public void registerWebSocketService() throws Exception {
        // 构建包含认证信息的Properties
        Properties properties = new Properties();
        properties.setProperty("serverAddr", nacosDiscoveryProperties.getServerAddr());

        // 添加认证信息
        if (nacosDiscoveryProperties.getUsername() != null && !nacosDiscoveryProperties.getUsername().isEmpty()) {
            properties.setProperty("username", nacosDiscoveryProperties.getUsername());
        }
        if (nacosDiscoveryProperties.getPassword() != null && !nacosDiscoveryProperties.getPassword().isEmpty()) {
            properties.setProperty("password", nacosDiscoveryProperties.getPassword());
        }
        if (nacosDiscoveryProperties.getGroup() != null && !nacosDiscoveryProperties.getGroup().isEmpty()) {
            properties.setProperty("group", nacosDiscoveryProperties.getGroup());
        }

        // 创建另一个注册实例用于
        NamingService namingService = NamingFactory.createNamingService(properties);

        registerInstances(namingService);
    }

    private void registerInstances(NamingService namingService) throws Exception {
        ProxyServerV2Properties proxyServerV2Properties = ProxyServerV2ConfigManager.getInstance()
                .getProperties();

        List<ProxyServerV2Properties.InboundProperties> inbounds = proxyServerV2Properties.getInbounds();
        for (ProxyServerV2Properties.InboundProperties inbound : inbounds) {
            Instance instance = new Instance();
            instance.setIp(InetAddress.getLocalHost().getHostAddress());
            instance.setPort(Integer.parseInt(inbound.getConfig().get("port") + ""));
            instance.setServiceName(inbound.getId());
            instance.setWeight(1.0);
            instance.setHealthy(true);
            instance.setEnabled(true);
            instance.setIp(nacosDiscoveryProperties.getIp());

            //采用inbound的id作为serviceName
            namingService.registerInstance(inbound.getId(), instance);
        }
    }
}
