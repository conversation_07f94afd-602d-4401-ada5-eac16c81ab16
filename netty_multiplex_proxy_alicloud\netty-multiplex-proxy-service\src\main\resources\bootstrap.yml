spring:
  application:
    name: netty-multiplex-proxy-service
  profiles:
    active: dev
  config:
    import: nacos:netty-multiplex-proxy-service-dev.yml
  cloud:
    openfeign:
      circuitbreaker:
        enabled: true
        # 确保使用Resilience4j而不是Hystrix
        group:
          enabled: false
    nacos:
      discovery:
#        server-addr: *************:8848
        server-addr: springcloud-nacos:8848
        namespace: public
        group: DEFAULT_GROUP
        username: nacos
        password: nacos
        enabled: true
        register-enabled: true
        # 直接指定公网IP地址注册，挪到netty-multiplex-proxy-service-dev.yml配置
#        ip: *************
      config:
#        server-addr: *************:8848
        server-addr: springcloud-nacos:8848
        namespace: public
        group: DEFAULT_GROUP
        username: nacos
        password: nacos
        file-extension: yml
        enabled: true
        # 配置文件名，默认为应用名
        name: ${spring.application.name}
        # 是否开启监听和自动刷新
        refresh-enabled: false