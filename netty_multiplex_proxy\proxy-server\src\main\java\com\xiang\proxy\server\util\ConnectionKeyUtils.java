package com.xiang.proxy.server.util;

import com.xiang.proxy.server.core.ProxyRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 连接键值生成工具类
 * 统一管理队列路由和连接复用的键值生成逻辑，确保一致性
 */
public class ConnectionKeyUtils {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionKeyUtils.class);
    
    /**
     * 生成连接键值，用于队列路由和连接复用
     * 格式: host:port:protocol:clientId
     * 
     * @param host 目标主机
     * @param port 目标端口
     * @param protocol 协议类型
     * @param clientId 客户端ID
     * @return 连接键值
     */
    public static String generateConnectionKey(String host, int port, String protocol, String clientId) {
        if (host == null || protocol == null || clientId == null) {
            throw new IllegalArgumentException("Host, protocol, and clientId cannot be null");
        }

        String connectionKey = new StringBuilder()
                .append(host).append(":")
                .append(port).append(":")
                .append(protocol).append(":")
                .append(clientId)
                .toString();

        logger.debug("生成连接键值: host={}, port={}, protocol={}, clientId={} -> connectionKey={}",
                    host, port, protocol, clientId, connectionKey);

        return connectionKey;
    }
    
    /**
     * 从ProxyRequest生成连接键值
     * 
     * @param request 代理请求
     * @return 连接键值
     */
    public static String generateConnectionKey(ProxyRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("ProxyRequest cannot be null");
        }
        
        return generateConnectionKey(
                request.getTargetHost(),
                request.getTargetPort(),
                request.getProtocol(),
                request.getClientId()
        );
    }
    
    /**
     * 解析连接键值
     * 
     * @param connectionKey 连接键值
     * @return 解析结果数组 [host, port, protocol, clientId]
     * @throws IllegalArgumentException 如果键值格式不正确
     */
    public static String[] parseConnectionKey(String connectionKey) {
        if (connectionKey == null || connectionKey.trim().isEmpty()) {
            throw new IllegalArgumentException("Connection key cannot be null or empty");
        }
        
        String[] parts = connectionKey.split(":");
        if (parts.length < 4) {
            throw new IllegalArgumentException(
                    "Invalid connection key format: " + connectionKey + 
                    ", expected format: host:port:protocol:clientId");
        }
        
        return parts;
    }
    
    /**
     * 验证连接键值格式
     * 
     * @param connectionKey 连接键值
     * @return 是否有效
     */
    public static boolean isValidConnectionKey(String connectionKey) {
        try {
            String[] parts = parseConnectionKey(connectionKey);
            // 验证端口号是否为有效数字
            Integer.parseInt(parts[1]);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 计算连接键值的哈希值，用于队列分配
     * 使用改进的哈希算法，避免负数问题
     * 
     * @param connectionKey 连接键值
     * @return 正数哈希值
     */
    public static int calculateHash(String connectionKey) {
        if (connectionKey == null) {
            return 0;
        }
        
        int hash = connectionKey.hashCode();
        // 使用位运算确保结果为正数，避免Integer.MIN_VALUE的问题
        return hash & 0x7FFFFFFF;
    }
    
    /**
     * 计算队列索引
     * 
     * @param connectionKey 连接键值
     * @param queueCount 队列数量
     * @return 队列索引
     */
    public static int calculateQueueIndex(String connectionKey, int queueCount) {
        if (queueCount <= 0) {
            throw new IllegalArgumentException("Queue count must be positive");
        }

        int hash = calculateHash(connectionKey);
        int queueIndex = hash % queueCount;

        logger.debug("计算队列索引: connectionKey={}, hash={}, queueCount={}, queueIndex={}",
                    connectionKey, hash, queueCount, queueIndex);

        return queueIndex;
    }
    
    /**
     * 从ProxyRequest计算队列索引
     * 
     * @param request 代理请求
     * @param queueCount 队列数量
     * @return 队列索引
     */
    public static int calculateQueueIndex(ProxyRequest request, int queueCount) {
        String connectionKey = generateConnectionKey(request);
        return calculateQueueIndex(connectionKey, queueCount);
    }
}
