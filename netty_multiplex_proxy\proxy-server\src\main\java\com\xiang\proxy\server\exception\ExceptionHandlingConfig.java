package com.xiang.proxy.server.exception;

/**
 * 异常处理配置
 * 用于配置异常处理的行为和策略
 */
public class ExceptionHandlingConfig {
    
    // 是否启用智能异常分类
    private static boolean enableSmartClassification = true;
    
    // 是否启用异常统计
    private static boolean enableExceptionStats = true;
    
    // Getters and Setters
    
    public static boolean isEnableSmartClassification() {
        return enableSmartClassification;
    }
    
    public static void setEnableSmartClassification(boolean enableSmartClassification) {
        ExceptionHandlingConfig.enableSmartClassification = enableSmartClassification;
    }
    
    public static boolean isEnableExceptionStats() {
        return enableExceptionStats;
    }
    
    public static void setEnableExceptionStats(boolean enableExceptionStats) {
        ExceptionHandlingConfig.enableExceptionStats = enableExceptionStats;
    }
    
    /**
     * 获取配置摘要
     */
    public static String getConfigSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("=== 异常处理配置 ===\n");
        summary.append(String.format("智能分类: %s\n", enableSmartClassification ? "启用" : "禁用"));
        summary.append(String.format("异常统计: %s\n", enableExceptionStats ? "启用" : "禁用"));
        summary.append("==================");
        return summary.toString();
    }
    
    /**
     * 重置为默认配置
     */
    public static void resetToDefaults() {
        enableSmartClassification = true;
        enableExceptionStats = true;
    }
}