package com.proxy.client.connection;

import com.alibaba.nacos.api.naming.pojo.Instance;
import com.proxy.client.config.ProxyClientConfigManager;
import com.proxy.client.discovery.NacosServiceDiscovery;
import com.proxy.client.queue.QueuedConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 动态连接管理器
 * 基于 Nacos 服务发现动态更新代理服务器连接
 */
public class DynamicConnectionManager extends QueuedConnectionManager
        implements NacosServiceDiscovery.ServiceChangeListener {

    private static final Logger logger = LoggerFactory.getLogger(DynamicConnectionManager.class);

    private final NacosServiceDiscovery serviceDiscovery;
    private final AtomicReference<Instance> currentInstance = new AtomicReference<>();
    private volatile ConnectionManager currentConnectionManager;

    /**
     * 构造函数
     *
     * @param serviceDiscovery Nacos 服务发现实例
     */
    public DynamicConnectionManager(NacosServiceDiscovery serviceDiscovery) {
        // 先用默认值初始化，稍后会更新
        super(createInitialConnectionManager());
        this.serviceDiscovery = serviceDiscovery;

        // 注册服务变更监听器
        serviceDiscovery.addServiceChangeListener(this);
    }

    /**
     * 创建初始的连接管理器
     */
    private static ConnectionManager createInitialConnectionManager() {
        ProxyClientConfigManager configManager = ProxyClientConfigManager.getInstance();
        // 使用默认值创建，稍后会被替换
        return ConnectionManager.newInstance(configManager.getProxyServerHost(), configManager.getProxyServerPort());
    }

    @Override
    public void start() {
        logger.info("启动动态连接管理器");

        try {
            // 启动 Nacos 服务发现
            if (!serviceDiscovery.isStarted()) {
                serviceDiscovery.start();
            }

            // 更新当前实例
            updateCurrentInstance();

            // 启动父类的连接管理器
            super.start();

            logger.info("动态连接管理器启动成功");
        } catch (Exception e) {
            logger.error("启动动态连接管理器失败", e);
            throw new RuntimeException("启动动态连接管理器失败", e);
        }
    }

    @Override
    public void stop() {
        logger.info("停止动态连接管理器");

        try {
            // 停止父类的连接管理器
            super.stop();

            // 移除服务变更监听器
            serviceDiscovery.removeServiceChangeListener(this);

            // 停止 Nacos 服务发现
            serviceDiscovery.stop();

            logger.info("动态连接管理器已停止");
        } catch (Exception e) {
            logger.error("停止动态连接管理器时发生错误", e);
        }
    }

    @Override
    public void onServiceChange(String serviceName, List<Instance> instances) {
        logger.info("服务 {} 实例发生变更，实例数: {}", serviceName, instances.size());

        // 更新当前使用的实例
        updateCurrentInstance();

        // 如果服务实例发生变更，可能需要重新建立连接
        // 这里可以根据需要实现连接重建逻辑
        handleServiceInstanceChange(instances);
    }

    /**
     * 更新当前使用的服务实例
     */
    private void updateCurrentInstance() {
        try {
            Instance newInstance = serviceDiscovery.getAvailableInstance();
            Instance oldInstance = currentInstance.get();

            if (newInstance == null) {
                logger.warn("没有可用的服务实例");
                return;
            }

            // 检查实例是否发生变化
            if (oldInstance == null ||
                    !oldInstance.getIp().equals(newInstance.getIp()) ||
                    oldInstance.getPort() != newInstance.getPort()) {

                currentInstance.set(newInstance);

                // 更新连接参数
                updateConnectionParameters(newInstance.getIp(), newInstance.getPort());

                logger.info("更新代理服务器实例: {}:{} -> {}:{}",
                        oldInstance != null ? oldInstance.getIp() : "null",
                        oldInstance != null ? oldInstance.getPort() : 0,
                        newInstance.getIp(), newInstance.getPort());
            }

        } catch (Exception e) {
            logger.error("更新当前服务实例失败", e);
        }
    }

    /**
     * 更新连接参数
     *
     * @param host 新的主机地址
     * @param port 新的端口
     */
    private void updateConnectionParameters(String host, int port) {
        try {
            // 创建新的连接管理器实例
            ConnectionManager newConnectionManager = ConnectionManager.newInstance(host, port);

            // 如果当前有连接管理器，先停止它
            if (currentConnectionManager != null) {
                // 这里可以添加优雅关闭逻辑
                logger.debug("准备切换连接管理器: {}:{} -> {}:{}",
                        getCurrentProxyHost(), getCurrentProxyPort(), host, port);
            }

            // 更新当前连接管理器
            currentConnectionManager = newConnectionManager;

            // 更新父类中的 connectionManager 字段
            this.setConnectionManager(newConnectionManager);

            logger.info("成功更新连接参数: {}:{}", host, port);
        } catch (Exception e) {
            logger.error("更新连接参数失败: {}:{}", host, port, e);
            // 如果更新失败，保持当前连接
        }
    }

    /**
     * 处理服务实例变更
     *
     * @param instances 新的服务实例列表
     */
    private void handleServiceInstanceChange(List<Instance> instances) {
        if (instances == null || instances.isEmpty()) {
            logger.warn("服务实例列表为空，保持当前连接");
            return;
        }

        // 检查当前实例是否仍然健康
        Instance current = currentInstance.get();
        if (current != null) {
            boolean currentInstanceStillHealthy = instances.stream()
                    .anyMatch(instance ->
                            instance.getIp().equals(current.getIp()) &&
                                    instance.getPort() == current.getPort() &&
                                    instance.isHealthy() &&
                                    instance.isEnabled());

            if (!currentInstanceStillHealthy) {
                logger.warn("当前使用的服务实例 {}:{} 不再健康，需要切换",
                        current.getIp(), current.getPort());

                // 选择新的健康实例
                updateCurrentInstance();
            }
        }
    }

    /**
     * 获取当前使用的服务实例
     */
    public Instance getCurrentInstance() {
        return currentInstance.get();
    }

    /**
     * 获取当前代理服务器地址
     */
    public String getCurrentProxyHost() {
        Instance instance = currentInstance.get();
        return instance != null ? instance.getIp() : "unknown";
    }

    /**
     * 获取当前代理服务器端口
     */
    public int getCurrentProxyPort() {
        Instance instance = currentInstance.get();
        return instance != null ? instance.getPort() : 0;
    }

    /**
     * 获取服务发现实例
     */
    public NacosServiceDiscovery getServiceDiscovery() {
        return serviceDiscovery;
    }

    /**
     * 强制刷新服务实例
     */
    public void refreshServiceInstance() {
        logger.info("强制刷新服务实例");
        updateCurrentInstance();
    }

    /**
     * 检查当前是否有可用的服务实例
     */
    public boolean hasAvailableInstance() {
        Instance instance = currentInstance.get();
        return instance != null;
    }
}