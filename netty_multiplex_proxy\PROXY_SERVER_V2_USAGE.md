# ProxyServerV2 使用指南

## 概述

`ProxyServerV2` 是基于组件化架构的新一代代理服务器，整合了 Inbound、Router、Outbound 三个核心组件，提供高性能、可扩展的代理服务。

## 架构特点

### 🏗️ 组件化架构
- **Inbound组件**: 多协议服务器支持，统一的连接管理
- **Router组件**: 智能路由决策，灵活的规则配置
- **Outbound组件**: 多种出站方式，支持直连、代理链、负载均衡

### 🚀 核心优势
- **高性能**: 优化的线程模型和网络配置
- **可扩展**: 模块化设计，易于添加新功能
- **可配置**: 丰富的配置选项，支持动态调整
- **可监控**: 完善的指标收集和健康检查

## 快速开始

### 1. 基本启动

```bash
# 使用默认配置启动
java -jar proxy-server.jar com.proxy.server.ProxyServerV2

# 指定配置文件启动
java -jar proxy-server.jar com.proxy.server.ProxyServerV2 --config=proxy-server-v2.yml

# 指定端口启动
java -jar proxy-server.jar com.proxy.server.ProxyServerV2 --port 9090
```

### 2. 编程方式启动

```java
public class MyProxyServer {
    public static void main(String[] args) throws Exception {
        // 创建代理服务器实例
        ProxyServerV2 server = new ProxyServerV2();
        
        // 启动服务器
        server.start().get();
        
        // 服务器现在正在运行...
        logger.info("代理服务器启动成功");
        
        // 优雅关闭
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                server.stop().get();
            } catch (Exception e) {
                logger.error("关闭服务器失败", e);
            }
        }));
    }
}
```

## 配置说明

### 基本配置

```yaml
# 服务器配置
server:
  port: 8080                    # 主端口
  bind_address: "0.0.0.0"       # 绑定地址
  additional_ports: [8081, 8082] # 额外端口

# 性能配置
performance:
  boss_threads: 2               # Boss线程数
  worker_threads: 0             # Worker线程数 (0=自动)
  max_connections: 50000        # 最大连接数
  backlog: 2048                # 连接队列大小
```

### 路由配置

```yaml
routing:
  default_outbound: "direct"    # 默认出站
  
  rules:
    - id: "internal"
      priority: 10              # 优先级 (数字越小优先级越高)
      outbound: "direct"
      matchers:
        - type: "host"
          operator: "regex"
          value: "^192\\.168\\."
```

### Outbound配置

```yaml
outbounds:
  - id: "direct"
    type: "direct"
    config:
      connect_timeout: 5000
      max_retries: 3
      
  - id: "proxy-chain"
    type: "proxy_chain"
    config:
      proxy_host: "upstream.com"
      proxy_port: 8080
```

## 高级功能

### 1. 多端口支持

```java
// 创建多个端口的服务器
ProxyServerV2 server = new ProxyServerV2();

// 启动后会自动创建多个端口的服务器
// 主端口: 8080 (标准配置)
// 额外端口: 8081, 8082 (高性能配置)
```

### 2. SSL/TLS支持

```yaml
ssl:
  enabled: true
  port: 8443
  key_store: "server.p12"
  key_store_password: "password"
```

### 3. 动态路由管理

```java
// 获取路由器
Router router = server.getRouter();

// 添加新路由规则
RouteRule newRule = new RouteRule("api-route", "API路由", 15, "api-outbound");
newRule.addMatcher(new RouteMatcher(RouteMatcher.Type.HOST, RouteMatcher.Operator.STARTS_WITH, "api."));
router.addRoute(newRule);

// 动态启用/禁用路由
router.setRouteEnabled("api-route", false);
```

### 4. 监控和统计

```java
// 获取服务器统计
InboundServerManager.ManagerStatistics stats = server.getInboundServerManager().getStatistics();
logger.info("服务器统计: {}", stats);

// 获取路由统计
logger.info("路由统计: {}", server.getRouter().getStatistics());

// 健康检查
InboundServerManager.HealthReport health = server.getInboundServerManager().getHealthReport();
if (!health.isAllHealthy()) {
    logger.warn("部分服务器不健康: {}", health);
}
```

## 性能优化

### 1. 线程配置优化

```yaml
performance:
  # 自动线程优化
  enable_thread_optimization: true
  io_ratio: 70                  # I/O密集型应用设置较高值
  
  # 手动配置
  boss_threads: 2               # 通常1-2个即可
  worker_threads: 16            # CPU核心数 * 2
```

### 2. 网络配置优化

```yaml
performance:
  # 高并发配置
  backlog: 8192                 # 增大连接队列
  max_connections: 100000       # 增大最大连接数
  receive_buffer_size: 262144   # 256KB缓冲区
  send_buffer_size: 262144      # 256KB缓冲区
```

### 3. 内存优化

```yaml
advanced:
  memory_optimization:
    enabled: true
    gc_threshold: 0.8           # 内存使用率达到80%时建议GC
```

## 监控集成

### 1. 内置监控

```java
// 启用详细监控
server.getProxyProcessor().getStatistics();

// 自定义监控间隔
// 在配置文件中设置
metrics:
  enabled: true
  report_interval: 30           # 30秒报告一次
```

### 2. 外部监控集成

```java
// 可以通过JMX或HTTP接口暴露指标
// 集成Prometheus、Grafana等监控系统
```

## 故障排除

### 1. 常见问题

**端口被占用**
```bash
# 检查端口占用
netstat -tulpn | grep :8080

# 修改配置文件中的端口
server:
  port: 8090
```

**内存不足**
```bash
# 增加JVM内存
java -Xmx4g -Xms2g -jar proxy-server.jar com.proxy.server.ProxyServerV2
```

**连接数过多**
```yaml
# 调整系统限制
ulimit -n 65536

# 调整服务器配置
performance:
  max_connections: 50000
```

### 2. 日志分析

```yaml
# 启用详细日志
logging:
  level: "DEBUG"
  loggers:
    "com.proxy.server": "DEBUG"
```

### 3. 性能调优

```bash
# JVM调优参数
java -server \
  -Xmx8g -Xms4g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+UnlockExperimentalVMOptions \
  -XX:+UseStringDeduplication \
  -jar proxy-server.jar com.proxy.server.ProxyServerV2
```

## 部署建议

### 1. 生产环境配置

```yaml
# 生产环境推荐配置
server:
  port: 8080
  additional_ports: [8081, 8082, 8083]  # 多端口负载分散

performance:
  max_connections: 100000
  boss_threads: 4
  worker_threads: 32
  backlog: 8192

ssl:
  enabled: true                 # 生产环境建议启用SSL

metrics:
  enabled: true                 # 启用监控
  report_interval: 60

security:
  auth:
    enabled: true               # 启用认证
  access_control:
    enabled: true               # 启用访问控制
```

### 2. Docker部署

```dockerfile
FROM openjdk:11-jre-slim

COPY proxy-server.jar /app/
COPY proxy-server-v2.yml /app/

WORKDIR /app

EXPOSE 8080 8081 8082 8443

CMD ["java", "-jar", "proxy-server.jar", "com.proxy.server.ProxyServerV2", "--config=proxy-server-v2.yml"]
```

### 3. Kubernetes部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxy-server-v2
spec:
  replicas: 3
  selector:
    matchLabels:
      app: proxy-server-v2
  template:
    metadata:
      labels:
        app: proxy-server-v2
    spec:
      containers:
      - name: proxy-server
        image: proxy-server:v2
        ports:
        - containerPort: 8080
        - containerPort: 8081
        - containerPort: 8082
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
```

## 扩展开发

### 1. 自定义Outbound

```java
public class CustomOutboundHandler implements OutboundHandler {
    @Override
    public CompletableFuture<OutboundConnection> connect(ProxyRequest request) {
        // 自定义连接逻辑
        return CompletableFuture.completedFuture(connection);
    }
    
    // 实现其他方法...
}

// 注册自定义Outbound
server.getProxyProcessor().registerOutboundHandler(new CustomOutboundHandler("custom", config));
```

### 2. 自定义路由规则

```java
// 自定义路由匹配器
public class CustomRouteMatcher extends RouteMatcher {
    public CustomRouteMatcher(String value) {
        super("custom", "custom_match", value);
    }
    
    // 实现自定义匹配逻辑
}
```

### 3. 自定义Inbound服务器

```java
public class CustomInboundServer extends AbstractInboundServer {
    @Override
    public String getServerType() {
        return "custom";
    }
    
    @Override
    public ChannelInitializer<SocketChannel> createChannelInitializer() {
        // 自定义Channel初始化逻辑
        return new CustomChannelInitializer();
    }
}
```

## 总结

`ProxyServerV2` 提供了一个完整的、生产就绪的代理服务器解决方案，具有：

- ✅ **高性能**: 优化的网络和线程配置
- ✅ **高可用**: 多端口支持和健康检查
- ✅ **易配置**: 丰富的配置选项和合理的默认值
- ✅ **可监控**: 完善的指标收集和报告
- ✅ **可扩展**: 模块化设计，易于定制和扩展

通过合理的配置和部署，可以满足从小型应用到大规模企业级部署的各种需求。