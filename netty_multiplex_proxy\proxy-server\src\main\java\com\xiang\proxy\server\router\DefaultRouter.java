package com.xiang.proxy.server.router;

import com.xiang.proxy.server.core.ProxyRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 默认路由器实现
 */
public class DefaultRouter implements Router {
    private static final Logger logger = LoggerFactory.getLogger(DefaultRouter.class);

    private final Map<String, RouteRule> routes = new ConcurrentHashMap<>();
    private final AtomicLong routeCounter = new AtomicLong(0);
    private final AtomicLong matchCounter = new AtomicLong(0);
    private final AtomicLong noMatchCounter = new AtomicLong(0);

    @Override
    public RouteResult route(ProxyRequest request) {
        long startTime = System.currentTimeMillis();
        routeCounter.incrementAndGet();

        try {
            // 获取按优先级排序的路由规则
            List<RouteRule> sortedRules = routes.values().stream()
                    .filter(RouteRule::isEnabled)
                    .sorted(Comparator.comparingInt(RouteRule::getPriority))
                    .collect(Collectors.toList());

            logger.debug("开始路由请求: {}, 可用规则数: {}", request, sortedRules.size());

            // 遍历规则进行匹配
            for (RouteRule rule : sortedRules) {
                if (matchRule(request, rule)) {
                    matchCounter.incrementAndGet();
                    logger.debug("请求 {} 匹配规则: {}", request.getRequestId(), rule);
                    
                    return RouteResult.builder()
                            .success(true)
                            .outboundId(rule.getOutboundId())
                            .ruleId(rule.getRuleId())
                            .ruleName(rule.getName())
                            .reason("Rule matched")
                            .routeTime(startTime)
                            .attributes(rule.getConfig())
                            .build();
                }
            }

            // 没有匹配的规则
            noMatchCounter.incrementAndGet();
            logger.warn("请求 {} 没有匹配的路由规则", request.getRequestId());
            return RouteResult.noMatch();

        } catch (Exception e) {
            logger.error("路由请求时发生异常: {}", request.getRequestId(), e);
            return RouteResult.error(e.getMessage());
        }
    }

    /**
     * 检查请求是否匹配路由规则
     */
    private boolean matchRule(ProxyRequest request, RouteRule rule) {
        if (!rule.hasMatchers()) {
            // 没有匹配条件的规则默认匹配所有请求
            return true;
        }

        // 所有匹配器都必须匹配（AND逻辑）
        for (RouteMatcher matcher : rule.getMatchers()) {
            if (!matchSingleMatcher(request, matcher)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查单个匹配器
     */
    private boolean matchSingleMatcher(ProxyRequest request, RouteMatcher matcher) {
        String actualValue = extractValueFromRequest(request, matcher.getType());
        if (actualValue == null) {
            return false;
        }

        String expectedValue = matcher.getValue();
        if (expectedValue == null) {
            return false;
        }

        // 大小写处理
        if (!matcher.isCaseSensitive()) {
            actualValue = actualValue.toLowerCase();
            expectedValue = expectedValue.toLowerCase();
        }

        return performMatch(actualValue, expectedValue, matcher);
    }

    /**
     * 从请求中提取指定类型的值
     */
    private String extractValueFromRequest(ProxyRequest request, String type) {
        switch (type) {
            case RouteMatcher.Type.HOST:
                return request.getTargetHost();
            case RouteMatcher.Type.PORT:
                return String.valueOf(request.getTargetPort());
            case RouteMatcher.Type.PROTOCOL:
                return request.getProtocol();
            case RouteMatcher.Type.CLIENT_IP:
                return extractClientIp(request);
            case RouteMatcher.Type.CLIENT_ID:
                return request.getClientId();
            case RouteMatcher.Type.SESSION_ID:
                return String.valueOf(request.getSessionId());
            case RouteMatcher.Type.TARGET:
                return request.getTarget();
            case RouteMatcher.Type.TIME:
                return String.valueOf(System.currentTimeMillis());
            default:
                // 尝试从属性中获取
                if (type.startsWith(RouteMatcher.Type.ATTRIBUTE + ".")) {
                    String attrKey = type.substring((RouteMatcher.Type.ATTRIBUTE + ".").length());
                    Object attrValue = request.getAttribute(attrKey);
                    return attrValue != null ? attrValue.toString() : null;
                }
                return null;
        }
    }

    /**
     * 提取客户端IP
     */
    private String extractClientIp(ProxyRequest request) {
        if (request.getClientChannel() != null && request.getClientChannel().remoteAddress() != null) {
            String address = request.getClientChannel().remoteAddress().toString();
            // 移除端口号，只保留IP
            int colonIndex = address.lastIndexOf(':');
            if (colonIndex > 0) {
                return address.substring(1, colonIndex); // 移除开头的'/'
            }
            return address.substring(1); // 移除开头的'/'
        }
        return null;
    }

    /**
     * 执行具体的匹配操作
     */
    private boolean performMatch(String actualValue, String expectedValue, RouteMatcher matcher) {
        String operator = matcher.getOperator();

        switch (operator) {
            case RouteMatcher.Operator.EQUALS:
                return actualValue.equals(expectedValue);
            case RouteMatcher.Operator.NOT_EQUALS:
                return !actualValue.equals(expectedValue);
            case RouteMatcher.Operator.CONTAINS:
                return actualValue.contains(expectedValue);
            case RouteMatcher.Operator.NOT_CONTAINS:
                return !actualValue.contains(expectedValue);
            case RouteMatcher.Operator.STARTS_WITH:
                return actualValue.startsWith(expectedValue);
            case RouteMatcher.Operator.ENDS_WITH:
                return actualValue.endsWith(expectedValue);
            case RouteMatcher.Operator.REGEX:
                return matchRegex(actualValue, matcher);
            case RouteMatcher.Operator.NOT_REGEX:
                return !matchRegex(actualValue, matcher);
            case RouteMatcher.Operator.IN:
                return matchIn(actualValue, expectedValue);
            case RouteMatcher.Operator.NOT_IN:
                return !matchIn(actualValue, expectedValue);
            case RouteMatcher.Operator.RANGE:
                return matchRange(actualValue, expectedValue);
            case RouteMatcher.Operator.GREATER_THAN:
                return compareNumeric(actualValue, expectedValue) > 0;
            case RouteMatcher.Operator.GREATER_EQUAL:
                return compareNumeric(actualValue, expectedValue) >= 0;
            case RouteMatcher.Operator.LESS_THAN:
                return compareNumeric(actualValue, expectedValue) < 0;
            case RouteMatcher.Operator.LESS_EQUAL:
                return compareNumeric(actualValue, expectedValue) <= 0;
            default:
                logger.warn("未知的匹配操作符: {}", operator);
                return false;
        }
    }

    /**
     * 正则表达式匹配
     */
    private boolean matchRegex(String actualValue, RouteMatcher matcher) {
        Pattern pattern = matcher.getCompiledPattern();
        if (pattern != null) {
            return pattern.matcher(actualValue).matches();
        }
        
        // 如果预编译失败，尝试临时编译
        try {
            int flags = matcher.isCaseSensitive() ? 0 : Pattern.CASE_INSENSITIVE;
            pattern = Pattern.compile(matcher.getValue(), flags);
            return pattern.matcher(actualValue).matches();
        } catch (Exception e) {
            logger.warn("正则表达式匹配失败: {}", matcher.getValue(), e);
            return false;
        }
    }

    /**
     * IN操作匹配（逗号分隔的列表）
     */
    private boolean matchIn(String actualValue, String expectedValue) {
        String[] values = expectedValue.split(",");
        for (String value : values) {
            if (actualValue.equals(value.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 范围匹配（格式：min-max）
     */
    private boolean matchRange(String actualValue, String expectedValue) {
        try {
            String[] range = expectedValue.split("-");
            if (range.length != 2) {
                return false;
            }
            
            double actual = Double.parseDouble(actualValue);
            double min = Double.parseDouble(range[0].trim());
            double max = Double.parseDouble(range[1].trim());
            
            return actual >= min && actual <= max;
        } catch (NumberFormatException e) {
            logger.warn("范围匹配时数值解析失败: actual={}, expected={}", actualValue, expectedValue);
            return false;
        }
    }

    /**
     * 数值比较
     */
    private int compareNumeric(String actualValue, String expectedValue) {
        try {
            double actual = Double.parseDouble(actualValue);
            double expected = Double.parseDouble(expectedValue);
            return Double.compare(actual, expected);
        } catch (NumberFormatException e) {
            // 如果不是数值，则按字符串比较
            return actualValue.compareTo(expectedValue);
        }
    }

    @Override
    public void addRoute(RouteRule rule) {
        if (rule == null || rule.getRuleId() == null) {
            throw new IllegalArgumentException("Route rule and rule ID cannot be null");
        }
        
        routes.put(rule.getRuleId(), rule);
        logger.info("添加路由规则: {}", rule);
    }

    @Override
    public boolean removeRoute(String ruleId) {
        if (ruleId == null) {
            return false;
        }
        
        RouteRule removed = routes.remove(ruleId);
        if (removed != null) {
            logger.info("移除路由规则: {}", removed);
            return true;
        }
        return false;
    }

    @Override
    public List<RouteRule> getRoutes() {
        return new ArrayList<>(routes.values());
    }

    @Override
    public RouteRule getRoute(String ruleId) {
        return routes.get(ruleId);
    }

    @Override
    public boolean updateRoute(RouteRule rule) {
        if (rule == null || rule.getRuleId() == null) {
            return false;
        }
        
        if (routes.containsKey(rule.getRuleId())) {
            routes.put(rule.getRuleId(), rule);
            logger.info("更新路由规则: {}", rule);
            return true;
        }
        return false;
    }

    @Override
    public boolean setRouteEnabled(String ruleId, boolean enabled) {
        RouteRule rule = routes.get(ruleId);
        if (rule != null) {
            rule.setEnabled(enabled);
            logger.info("设置路由规则 {} 状态为: {}", ruleId, enabled);
            return true;
        }
        return false;
    }

    @Override
    public void reloadRoutes() {
        logger.info("重新加载路由配置");
        // 这里可以从配置文件重新加载路由规则
        // 暂时保留现有实现
    }

    @Override
    public RouteStatistics getStatistics() {
        return new RouteStatistics(
                routeCounter.get(),
                matchCounter.get(),
                noMatchCounter.get(),
                routes.size()
        );
    }

    @Override
    public void clearRoutes() {
        int count = routes.size();
        routes.clear();
        logger.info("清空所有路由规则，共清空 {} 条规则", count);
    }

    @Override
    public ValidationResult validateRule(RouteRule rule) {
        if (rule == null) {
            return ValidationResult.failure("Route rule cannot be null");
        }
        
        if (rule.getRuleId() == null || rule.getRuleId().trim().isEmpty()) {
            return ValidationResult.failure("Rule ID cannot be null or empty");
        }
        
        if (rule.getOutboundId() == null || rule.getOutboundId().trim().isEmpty()) {
            return ValidationResult.failure("Outbound ID cannot be null or empty");
        }
        
        // 验证匹配器
        if (rule.hasMatchers()) {
            for (RouteMatcher matcher : rule.getMatchers()) {
                ValidationResult matcherResult = validateMatcher(matcher);
                if (!matcherResult.isValid()) {
                    return matcherResult;
                }
            }
        }
        
        return ValidationResult.success();
    }

    /**
     * 验证匹配器
     */
    private ValidationResult validateMatcher(RouteMatcher matcher) {
        if (matcher.getType() == null || matcher.getType().trim().isEmpty()) {
            return ValidationResult.failure("Matcher type cannot be null or empty");
        }
        
        if (matcher.getOperator() == null || matcher.getOperator().trim().isEmpty()) {
            return ValidationResult.failure("Matcher operator cannot be null or empty");
        }
        
        if (matcher.getValue() == null) {
            return ValidationResult.failure("Matcher value cannot be null");
        }
        
        // 验证正则表达式
        if (RouteMatcher.Operator.REGEX.equals(matcher.getOperator()) ||
            RouteMatcher.Operator.NOT_REGEX.equals(matcher.getOperator())) {
            try {
                int flags = matcher.isCaseSensitive() ? 0 : Pattern.CASE_INSENSITIVE;
                Pattern.compile(matcher.getValue(), flags);
            } catch (Exception e) {
                return ValidationResult.failure("Invalid regex pattern: " + e.getMessage());
            }
        }
        
        return ValidationResult.success();
    }
}