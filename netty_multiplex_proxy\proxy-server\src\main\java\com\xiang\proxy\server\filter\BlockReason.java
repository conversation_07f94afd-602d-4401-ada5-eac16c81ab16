package com.xiang.proxy.server.filter;

/**
 * 阻止访问的原因枚举
 */
public enum BlockReason {
    /** 恶意域名 */
    MALICIOUS_DOMAIN("恶意域名"),
    
    /** 包含恶意关键词 */
    MALICIOUS_KEYWORDS("包含恶意关键词"),
    
    /** 海外可疑网站 */
    OVERSEAS_SUSPICIOUS("海外可疑网站"),
    
    /** 地理位置限制 */
    GEO_LOCATION_RESTRICTED("地理位置限制"),
    
    /** 端口限制 */
    PORT_RESTRICTED("端口限制"),
    
    /** 其他原因 */
    OTHER("其他原因");
    
    private final String description;
    
    BlockReason(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return description;
    }
}
