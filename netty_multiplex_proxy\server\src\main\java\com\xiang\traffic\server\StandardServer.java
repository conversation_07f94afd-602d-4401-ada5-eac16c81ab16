package com.xiang.traffic.server;

import com.xiang.traffic.ComponentException;
import com.xiang.traffic.ConfigManager;
import com.xiang.traffic.TopLevelComponent;
import com.xiang.traffic.server.core.ProxyProcessor;
import com.xiang.traffic.server.db.component.DatabaseInitComponent;
import com.xiang.traffic.server.db.user.UserDatabase;

import java.io.IOException;
import java.util.Properties;


public class StandardServer extends TopLevelComponent implements Server {
    static {
        try {
            Class.forName("com.xiang.traffic.url.ClasspathURLHandlerFactory");
        } catch (ClassNotFoundException e) {
            throw new Error(e);
        }
    }

    private ServerConfig serverConfig;


    public StandardServer() {
        super("Server");
    }

    @Override
    protected void initInternal() {
        ConfigManager<?> configManager = getConfigManager();

        try {
            Properties properties = new Properties();
            properties.load(configManager.loadResource("classpath://config.properties"));
            properties.forEach((k, v) -> setSystemProperties(k.toString(), v.toString()));
        } catch (IOException e) {
            throw new ComponentException(e);
        }

        this.serverConfig = new ServerConfig(configManager);
        getConfigManager().registerConfig(serverConfig);

        ServerConfig.Node[] nodes = serverConfig.getServerNode();
        for (ServerConfig.Node node : nodes) {
            addComponent(new ProxyProcessor(this, node));
        }

        addComponent(new DatabaseInitComponent(this));

        super.initInternal();
    }


    @Override
    public ServerConfig config() {
        return serverConfig;
    }

    @Override
    public ConfigManager<?> getConfigManager() {
        return super.getConfigManager();
    }

    @Override
    public UserDatabase getUserDatabase() {
        DatabaseInitComponent component = (DatabaseInitComponent) this.getComponentByName(DatabaseInitComponent.name);
        return component.getUserDatabase();
    }

    @Override
    public String getSystemProperties(String key) {
        return super.getSystemProperties(key);
    }

    @Override
    public String getEnvironmentVariable(String key) {
        return super.getEnvironmentVariable(key);
    }

    @Override
    public void setSystemProperties(String key, String value) {
        super.setSystemProperties(key, value);
    }
}
